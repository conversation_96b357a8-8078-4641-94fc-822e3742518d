/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * cere_verify_log 核销记录实体类
 * <AUTHOR>
@Data
@TableName("cere_verify_log")
public class CereVerifyLog implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long logId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 订单商品ID
     */
    private Long orderProductId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 核销类型：1=整单核销，2=单商品核销，3=服务次数核销
     */
    private Integer verifyType;

    /**
     * 本次核销次数
     */
    private Integer verifyTimes;

    /**
     * 剩余次数（服务类商品）
     */
    private Integer remainingTimes;

    /**
     * 核销码
     */
    private String verifyCode;

    /**
     * 核销员工ID
     */
    private Long staffId;

    /**
     * 核销员工姓名
     */
    private String staffName;

    /**
     * 核销门店ID
     */
    private Long storeId;

    /**
     * 核销门店名称
     */
    private String storeName;

    /**
     * 核销时间
     */
    private Date verifyTime;

    /**
     * 服务日期（可能与核销时间不同）
     */
    private Date serviceDate;

    /**
     * 实际服务时长(分钟)
     */
    private Integer serviceDuration;

    /**
     * 核销备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private String createTime;
}
