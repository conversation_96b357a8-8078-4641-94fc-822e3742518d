/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * cere_store_staff 门店员工实体类
 * <AUTHOR>
@Data
@TableName("cere_store_staff")
public class CereStoreStaff implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    @TableId(type = IdType.AUTO)
    private Long staffId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 关联用户ID（如果员工也是平台用户）
     */
    private Long userId;

    /**
     * 员工姓名
     */
    private String staffName;

    /**
     * 员工手机号
     */
    private String staffPhone;

    /**
     * 员工工号
     */
    private String staffCode;

    /**
     * 微信OpenID
     */
    private String wechatOpenid;

    /**
     * 角色类型：1=普通员工，2=店长，3=区域经理
     */
    private Integer roleType;

    /**
     * 自提权限：0=否，1=是
     */
    private Integer canPickup;

    /**
     * 核销权限：0=否，1=是
     */
    private Integer canVerify;

    /**
     * 退款权限：0=否，1=是
     */
    private Integer canRefund;

    /**
     * 状态：0=禁用，1=启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
