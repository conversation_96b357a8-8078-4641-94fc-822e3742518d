/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 门店创建请求对象
 * <AUTHOR>
@Data
@ApiModel("门店创建请求")
public class StoreCreateRequest {

    @ApiModelProperty("门店名称")
    @NotBlank(message = "门店名称不能为空")
    private String storeName;

    @ApiModelProperty("门店编码")
    private String storeCode;

    @ApiModelProperty("联系人姓名")
    @NotBlank(message = "联系人姓名不能为空")
    private String contactName;

    @ApiModelProperty("联系电话")
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    @ApiModelProperty("省份ID")
    private Integer provinceId;

    @ApiModelProperty("城市ID")
    private Integer cityId;

    @ApiModelProperty("区域ID")
    private Integer areaId;

    @ApiModelProperty("详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("营业时间，JSON格式")
    private String businessHours;

    @ApiModelProperty("自提时间，JSON格式")
    private String pickupHours;

    @ApiModelProperty("门店图片")
    private String storeImage;

    @ApiModelProperty("门店描述")
    private String storeDesc;

    @ApiModelProperty("是否支持自提：0=否，1=是")
    private Integer isPickupEnabled;

    @ApiModelProperty("是否支持核销：0=否，1=是")
    private Integer isVerifyEnabled;

    @ApiModelProperty("排序权重")
    private Integer sortOrder;
}
