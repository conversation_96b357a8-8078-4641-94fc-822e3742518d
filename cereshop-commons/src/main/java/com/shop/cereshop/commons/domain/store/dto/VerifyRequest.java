/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 核销请求对象
 * <AUTHOR>
@Data
@ApiModel("核销请求")
public class VerifyRequest {

    @ApiModelProperty("核销码")
    @NotBlank(message = "核销码不能为空")
    private String verifyCode;

    @ApiModelProperty("员工ID")
    @NotNull(message = "员工ID不能为空")
    private Long staffId;

    @ApiModelProperty("核销备注")
    private String remark;

    @ApiModelProperty("核销类型：1=整单核销，2=单商品核销，3=服务次数核销")
    private Integer verifyType;

    @ApiModelProperty("核销次数（服务类商品使用）")
    private Integer verifyTimes;

    @ApiModelProperty("订单商品ID（部分核销时使用）")
    private Long orderProductId;

    @ApiModelProperty("服务日期")
    private String serviceDate;

    @ApiModelProperty("服务时长(分钟)")
    private Integer serviceDuration;
}
