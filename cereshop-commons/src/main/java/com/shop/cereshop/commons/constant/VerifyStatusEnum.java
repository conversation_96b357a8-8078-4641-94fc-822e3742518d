/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.constant;

/**
 * 核销状态枚举
 * <AUTHOR>
public enum VerifyStatusEnum {
    
    /**
     * 待核销
     */
    PENDING(0, "待核销"),
    
    /**
     * 部分核销
     */
    PARTIAL(1, "部分核销"),
    
    /**
     * 已核销
     */
    COMPLETED(2, "已核销");

    private final Integer code;
    private final String desc;

    VerifyStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     */
    public static VerifyStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VerifyStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为待核销状态
     */
    public static boolean isPending(Integer code) {
        return PENDING.getCode().equals(code);
    }

    /**
     * 判断是否为已完成核销状态
     */
    public static boolean isCompleted(Integer code) {
        return COMPLETED.getCode().equals(code);
    }

    /**
     * 判断是否为部分核销状态
     */
    public static boolean isPartial(Integer code) {
        return PARTIAL.getCode().equals(code);
    }
}
