/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.utils;

/**
 * 封装索引文档数据工具类
 */
public class PackageIndexUtils {

    /**
     * 封装订单管理文档数据
     * @param deliveryReceipt
     * @param deliveryGoods
     * @return
     */
//    public static DeliveryIndex setDeliveryIndex(TmsReceipt tmsReceipt, TmsGoods tmsGoods) {
//        DeliveryIndex deliveryIndex=new DeliveryIndex();
//        deliveryIndex.setId(tmsGoods.getId());
//        deliveryIndex.setDeliveryReceiptId(tmsReceipt.getId());
//        deliveryIndex.setDeliveryName(tmsReceipt.getDeliveryName());
//        deliveryIndex.setDeliveryPhone(tmsReceipt.getDeliveryPhone());
//        deliveryIndex.setDeliveryRegion(tmsReceipt.getDeliveryRegion());
//        deliveryIndex.setReceiptName(tmsReceipt.getReceiptName());
//        deliveryIndex.setReceiptPhone(tmsReceipt.getReceiptPhone());
//        deliveryIndex.setReceiptRegion(tmsReceipt.getReceiptRegion());
//        deliveryIndex.setAdress(tmsReceipt.getAdress());
//        deliveryIndex.setGoodsName(tmsGoods.getGoodsName());
//        deliveryIndex.setGoodsBrand(tmsGoods.getGoodsBrand());
//        return deliveryIndex;
//    }
}
