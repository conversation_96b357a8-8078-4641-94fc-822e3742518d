/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.constant;

/**
 * 配送方式枚举
 * <AUTHOR>
public enum DeliveryTypeEnum {
    
    /**
     * 快递配送
     */
    EXPRESS(1, "快递配送"),
    
    /**
     * 门店自提
     */
    PICKUP(2, "门店自提");

    private final Integer code;
    private final String desc;

    DeliveryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     */
    public static DeliveryTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeliveryTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为自提方式
     */
    public static boolean isPickup(Integer code) {
        return PICKUP.getCode().equals(code);
    }

    /**
     * 判断是否为快递方式
     */
    public static boolean isExpress(Integer code) {
        return EXPRESS.getCode().equals(code);
    }
}
