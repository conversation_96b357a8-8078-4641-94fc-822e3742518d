/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.store.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 核销记录响应对象
 * <AUTHOR>
@Data
@ApiModel("核销记录")
public class VerifyLogVO {

    @ApiModelProperty("日志ID")
    private Long logId;

    @ApiModelProperty("订单ID")
    private Long orderId;

    @ApiModelProperty("订单号")
    private String orderSn;

    @ApiModelProperty("商户ID")
    private Long merchantId;

    @ApiModelProperty("订单商品ID")
    private Long orderProductId;

    @ApiModelProperty("商品ID")
    private Long productId;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("SKU名称")
    private String skuName;

    @ApiModelProperty("核销类型：1=整单核销，2=单商品核销，3=服务次数核销")
    private Integer verifyType;

    @ApiModelProperty("核销类型描述")
    private String verifyTypeDesc;

    @ApiModelProperty("本次核销次数")
    private Integer verifyTimes;

    @ApiModelProperty("剩余次数")
    private Integer remainingTimes;

    @ApiModelProperty("核销码")
    private String verifyCode;

    @ApiModelProperty("核销员工ID")
    private Long staffId;

    @ApiModelProperty("核销员工姓名")
    private String staffName;

    @ApiModelProperty("核销门店ID")
    private Long storeId;

    @ApiModelProperty("核销门店名称")
    private String storeName;

    @ApiModelProperty("核销时间")
    private Date verifyTime;

    @ApiModelProperty("服务日期")
    private Date serviceDate;

    @ApiModelProperty("服务时长(分钟)")
    private Integer serviceDuration;

    @ApiModelProperty("核销备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private String createTime;
}
