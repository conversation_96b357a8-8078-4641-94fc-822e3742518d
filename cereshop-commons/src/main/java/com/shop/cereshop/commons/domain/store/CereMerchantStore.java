/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * cere_merchant_store 商户门店实体类
 * <AUTHOR>
@Data
@TableName("cere_merchant_store")
public class CereMerchantStore implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 门店ID
     */
    @TableId(type = IdType.AUTO)
    private Long storeId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 省份ID
     */
    private Integer provinceId;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 区域ID
     */
    private Integer areaId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 营业时间，JSON格式
     */
    private String businessHours;

    /**
     * 自提时间，JSON格式
     */
    private String pickupHours;

    /**
     * 门店图片
     */
    private String storeImage;

    /**
     * 门店描述
     */
    private String storeDesc;

    /**
     * 是否支持自提：0=否，1=是
     */
    private Integer isPickupEnabled;

    /**
     * 是否支持核销：0=否，1=是
     */
    private Integer isVerifyEnabled;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 状态：0=禁用，1=启用
     */
    private Integer status;

    /**
     * 是否默认门店：0=否，1=是
     */
    private Integer isDefault;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
