/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.trigger;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * cere_trigger 触发器配置实体类
 * <AUTHOR>
@Data
@TableName("cere_trigger")
public class CereTrigger implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 触发器ID
     */
    @TableId(type = IdType.AUTO)
    private Long triggerId;

    /**
     * 触发器名称
     */
    private String triggerName;

    /**
     * 触发器编码
     */
    private String triggerCode;

    /**
     * 触发器描述
     */
    private String triggerDesc;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 触发器类型：1=数据变更触发，2=时间触发，3=事件触发
     */
    private Integer triggerType;

    /**
     * 触发事件：INSERT/UPDATE/DELETE/TIMER/CUSTOM
     */
    private String triggerEvent;

    /**
     * 目标表名（数据变更触发时使用）
     */
    private String targetTable;

    /**
     * 触发条件JSON配置
     */
    private String triggerCondition;

    /**
     * 是否启用：0=禁用，1=启用
     */
    private Integer isEnabled;

    /**
     * 优先级，数字越大优先级越高
     */
    private Integer priority;

    /**
     * 执行模式：1=同步执行，2=异步执行
     */
    private Integer executeMode;

    /**
     * 失败重试次数
     */
    private Integer retryTimes;

    /**
     * 重试间隔(秒)
     */
    private Integer retryInterval;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;
}
