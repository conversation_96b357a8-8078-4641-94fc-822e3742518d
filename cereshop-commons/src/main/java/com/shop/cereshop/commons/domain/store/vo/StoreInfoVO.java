/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.store.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 门店信息响应对象
 * <AUTHOR>
@Data
@ApiModel("门店信息")
public class StoreInfoVO {

    @ApiModelProperty("门店ID")
    private Long storeId;

    @ApiModelProperty("商户ID")
    private Long merchantId;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("门店编码")
    private String storeCode;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("省份ID")
    private Integer provinceId;

    @ApiModelProperty("城市ID")
    private Integer cityId;

    @ApiModelProperty("区域ID")
    private Integer areaId;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("营业时间")
    private String businessHours;

    @ApiModelProperty("自提时间")
    private String pickupHours;

    @ApiModelProperty("门店图片")
    private String storeImage;

    @ApiModelProperty("门店描述")
    private String storeDesc;

    @ApiModelProperty("是否支持自提")
    private Integer isPickupEnabled;

    @ApiModelProperty("是否支持核销")
    private Integer isVerifyEnabled;

    @ApiModelProperty("排序权重")
    private Integer sortOrder;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("距离(公里)")
    private Double distance;

    @ApiModelProperty("员工数量")
    private Integer staffCount;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("更新时间")
    private String updateTime;
}
