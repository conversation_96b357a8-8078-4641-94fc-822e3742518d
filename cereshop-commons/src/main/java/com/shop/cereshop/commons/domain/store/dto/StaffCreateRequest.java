/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.commons.domain.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 员工创建请求对象
 * <AUTHOR>
@Data
@ApiModel("员工创建请求")
public class StaffCreateRequest {

    @ApiModelProperty("门店ID")
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    @ApiModelProperty("关联用户ID")
    private Long userId;

    @ApiModelProperty("员工姓名")
    @NotBlank(message = "员工姓名不能为空")
    private String staffName;

    @ApiModelProperty("员工手机号")
    @NotBlank(message = "员工手机号不能为空")
    private String staffPhone;

    @ApiModelProperty("员工工号")
    private String staffCode;

    @ApiModelProperty("微信OpenID")
    private String wechatOpenid;

    @ApiModelProperty("角色类型：1=普通员工，2=店长，3=区域经理")
    private Integer roleType;

    @ApiModelProperty("自提权限：0=否，1=是")
    private Integer canPickup;

    @ApiModelProperty("核销权限：0=否，1=是")
    private Integer canVerify;

    @ApiModelProperty("退款权限：0=否，1=是")
    private Integer canRefund;
}
