<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.after.CereAfterProductDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.after.CereAfterProduct">
    <id column="after_product_id" jdbcType="BIGINT" property="afterProductId" />
    <result column="after_id" jdbcType="BIGINT" property="afterId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="product_price" jdbcType="DECIMAL" property="productPrice" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="SKU" jdbcType="VARCHAR" property="SKU" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
  </resultMap>
  <insert id="insertSelective" keyColumn="after_product_id" keyProperty="afterProductId" parameterType="com.shop.cereshop.commons.domain.after.CereAfterProduct" useGeneratedKeys="true">
    insert into cere_after_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterId != null">
        after_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="productName != null and productName!=''">
        product_name,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="productPrice != null">
        product_price,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="SKU != null and SKU!=''">
        SKU,
      </if>
      <if test="weight != null">
        weight,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterId != null">
        #{afterId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="productName != null and productName!=''">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="SKU != null and SKU!=''">
        #{SKU,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <select id="findValuesBySkuId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.after.CereAfterProductAttribute">
    SELECT sku_name,sku_value,name_code,value_code FROM cere_sku_name where sku_id=#{skuId}
  </select>

  <select id="findSkusByAfterId" parameterType="java.lang.Object" resultType="com.shop.cereshop.app.page.cart.CartSku">
    SELECT a.product_id,a.sku_id,a.product_name,a.product_price price,
    a.image,a.number,a.SKU,a.weight,a.product_price*a.number total,
    b.`value` from cere_after_product a
    LEFT JOIN (SELECT GROUP_CONCAT(sku_value) `value`,after_product_id from
    cere_after_product_attribute GROUP BY after_product_id) b ON a.after_product_id=b.after_product_id
    where a.after_id=#{afterId}
  </select>

  <select id="findSkuBySkus" parameterType="com.shop.cereshop.app.param.after.AfterParam" resultType="com.shop.cereshop.commons.domain.after.CereAfterProduct">
    SELECT a.product_id,a.sku_id,b.product_name,e.product_price,a.weight,
    a.SKU,
    IF(c.image IS NULL OR c.image='',d.product_image,c.image) image FROM cere_product_sku a
    LEFT JOIN cere_shop_product b ON a.product_id=b.product_id
    LEFT JOIN (SELECT sku_id,image,GROUP_CONCAT(sku_value) `value`
    from cere_sku_name GROUP BY sku_id) c ON a.sku_id=c.sku_id
    LEFT JOIN (SELECT a.product_id,a.product_image from cere_product_image a,cere_shop_product b
    where a.product_id=b.product_id GROUP BY a.product_id) d ON a.product_id=d.product_id
    LEFT JOIN cere_order_product e ON a.sku_id=e.sku_id and e.order_id=#{orderId}
    where a.sku_id in (
    <foreach collection="skus" item="item" index="index" separator=",">
      #{item.skuId}
    </foreach>
    )
  </select>
  <select id="findByOrderIdForCheck" resultType="com.shop.cereshop.commons.domain.after.CereAfterProduct">
    select a.after_id, a.order_id, b.sku_id
    from cere_order_after a
    join cere_after_product b on b.after_id = a.after_id
    where a.order_id = #{orderId}
    and a.after_state in (1, 2, 3, 4, 7, 8, 9, 10)
  </select>
</mapper>
