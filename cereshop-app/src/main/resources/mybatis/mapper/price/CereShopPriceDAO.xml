<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.price.CereShopPriceDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereShopPrice">
    <id column="price_id" jdbcType="BIGINT" property="priceId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="compose_name" jdbcType="VARCHAR" property="composeName" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    price_id, shop_id, compose_name, start_time, end_time,`state`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_shop_price
    where price_id = #{priceId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_shop_price
    where price_id = #{priceId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="price_id" keyProperty="priceId" parameterType="com.shop.cereshop.commons.domain.tool.CereShopPrice" useGeneratedKeys="true">
    insert into cere_shop_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="composeName != null">
        compose_name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="composeName != null">
        #{composeName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.tool.CereShopPrice">
    update cere_shop_price
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="composeName != null">
        compose_name = #{composeName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where price_id = #{priceId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.tool.CereShopPrice">
    update cere_shop_price
    set shop_id = #{shopId,jdbcType=BIGINT},
        compose_name = #{composeName,jdbcType=VARCHAR},
        start_time = #{startTime,jdbcType=VARCHAR},
        end_time = #{endTime,jdbcType=VARCHAR},
        `state` = #{state,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=VARCHAR}
    where price_id = #{priceId,jdbcType=BIGINT}
  </update>

  <select id="selectPriceList" resultType="com.shop.cereshop.app.param.price.ShopPriceWithRule">
    select a.price_id,a.shop_id,c.number,c.price from cere_shop_price a
        join cere_price_rule c on a.price_id = c.price_id
    where a.shop_id in
    <foreach collection="list" item="shopId" open="(" separator="," close=")">
      #{shopId}
    </foreach>
    and a.state = 1
    order by c.number desc
  </select>
  <select id="selectProductListByPriceId" resultType="com.shop.cereshop.app.page.index.Product">
    select b.product_id, c.shop_id, c.product_name, d.product_image as image, min(e.price) as price, min(e.sku_id) as sku_id from cere_shop_price a
      join cere_price_product b on b.price_id = a.price_id
      join cere_shop_product c on c.product_id = b.product_id
      join cere_product_image d on d.product_id = b.product_id
      join cere_product_sku e on e.product_id = b.product_id
    where a.price_id = #{priceId} and a.state = 1 and c.shelve_state = 1
    group by b.product_id
    order by price desc
  </select>
  <select id="getPrices" resultType="com.shop.cereshop.app.page.price.ShopPriceDetail">
    SELECT price_id, shop_id, compose_name, start_time, end_time,`state` FROM cere_shop_price
    where shop_id=#{shopId} and state in (0,1)
    <if test="ids!=null and ids.size()>0">
      and price_id in (
      <foreach collection="ids" item="id" index="index" separator=",">
        #{id}
      </foreach>
      )
    </if>
  </select>
  <select id="selectPriceByProductId" resultType="java.lang.Long">
    select a.price_id from cere_shop_price a
    join cere_price_product b on b.price_id = a.price_id
    where a.start_time &lt; now() and a.end_time > now()
    and a.state = 1 and b.product_id = #{productId}
    order by a.update_time desc limit 1
  </select>
</mapper>
