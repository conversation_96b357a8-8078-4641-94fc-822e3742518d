<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.product.CereSkuNameDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.product.CereSkuName">
    <id column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="sku_value" jdbcType="VARCHAR" property="skuValue" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="name_code" jdbcType="VARCHAR" property="nameCode" />
    <result column="value_code" jdbcType="VARCHAR" property="valueCode" />
  </resultMap>
  <sql id="Base_Column_List">
    sku_id, sku_name, sku_value,image,name_code,value_code
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_sku_name
    where sku_id = #{skuId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="sku_id" keyProperty="skuId" parameterType="com.shop.cereshop.commons.domain.product.CereSkuName" useGeneratedKeys="true">
    insert into cere_sku_name
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="skuName != null and skuName!=''">
        sku_name,
      </if>
      <if test="skuValue != null and skuValue!=''">
        sku_value,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="nameCode != null and nameCode!=''">
        name_code,
      </if>
      <if test="valueCode != null and valueCode!=''">
        value_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="skuName != null and skuName!=''">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuValue != null and skuValue!=''">
        #{skuValue,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="nameCode != null and nameCode!=''">
        #{nameCode,jdbcType=VARCHAR},
      </if>
      <if test="valueCode != null and valueCode!=''">
        #{valueCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.product.CereSkuName">
    update cere_sku_name
    <set>
      <if test="skuName != null and skuName!=''">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuValue != null and skuValue!=''">
        sku_value = #{skuValue,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="nameCode != null and nameCode!=''">
        name_code = #{nameCode,jdbcType=VARCHAR},
      </if>
      <if test="valueCode != null and valueCode!=''">
        value_code = #{valueCode,jdbcType=VARCHAR},
      </if>
    </set>
    where sku_id = #{skuId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.product.CereSkuName">
    update cere_sku_name
    set sku_name = #{skuName,jdbcType=VARCHAR},
      sku_value = #{skuValue,jdbcType=VARCHAR},
      image = #{image,jdbcType=VARCHAR},
      name_code = #{nameCode,jdbcType=VARCHAR},
      value_code = #{valueCode,jdbcType=VARCHAR}
    where sku_id = #{skuId,jdbcType=BIGINT}
  </update>
</mapper>
