<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.role.CerePlatformRoleDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.role.CerePlatformRole">
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="role_describe" jdbcType="VARCHAR" property="roleDescribe" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    role_id, role_name, role_describe, create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_role
    where role_id = #{roleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_platform_role
    where role_id = #{roleId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="role_id" keyProperty="roleId" parameterType="com.shop.cereshop.commons.domain.role.CerePlatformRole" useGeneratedKeys="true">
    insert into cere_platform_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleName != null and roleName!=''">
        role_name,
      </if>
      <if test="roleDescribe != null and roleDescribe!=''">
        role_describe,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleName != null and roleName!=''">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="roleDescribe != null and roleDescribe!=''">
        #{roleDescribe,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.role.CerePlatformRole">
    update cere_platform_role
    <set>
      <if test="roleName != null and roleName!=''">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="roleDescribe != null and roleDescribe!=''">
        role_describe = #{roleDescribe,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where role_id = #{roleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.role.CerePlatformRole">
    update cere_platform_role
    set role_name = #{roleName,jdbcType=VARCHAR},
      role_describe = #{roleDescribe,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where role_id = #{roleId,jdbcType=BIGINT}
  </update>
</mapper>
