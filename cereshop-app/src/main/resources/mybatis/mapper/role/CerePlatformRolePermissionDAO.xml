<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.role.CerePlatformRolePermissionDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.role.CerePlatformRolePermission">
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="permission_id" jdbcType="BIGINT" property="permissionId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.role.CerePlatformRolePermission">
    insert into cere_platform_role_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="permissionId != null">
        permission_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="permissionId != null">
        #{permissionId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>
