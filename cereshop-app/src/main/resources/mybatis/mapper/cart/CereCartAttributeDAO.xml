<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.cart.CereCartAttributeDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.cart.CereCartAttribute">
    <result column="cart_id" jdbcType="BIGINT" property="cartId" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="sku_value" jdbcType="VARCHAR" property="skuValue" />
    <result column="name_code" jdbcType="VARCHAR" property="nameCode" />
    <result column="value_code" jdbcType="VARCHAR" property="valueCode" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.cart.CereCartAttribute">
    insert into cere_cart_attribute
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cartId != null">
        cart_id,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="skuValue != null">
        sku_value,
      </if>
      <if test="nameCode != null">
        name_code,
      </if>
      <if test="valueCode != null">
        value_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cartId != null">
        #{cartId,jdbcType=BIGINT},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuValue != null">
        #{skuValue,jdbcType=VARCHAR},
      </if>
      <if test="nameCode != null">
        #{nameCode,jdbcType=VARCHAR},
      </if>
      <if test="valueCode != null">
        #{valueCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" parameterType="java.util.List">
  insert into cere_cart_attribute (cart_id, sku_name,sku_value,name_code,value_code) values
  <foreach collection="list" item="item" index="index" separator=",">
    (
    #{item.cartId},
    #{item.skuName},
    #{item.skuValue},
    #{item.nameCode},
    #{item.valueCode}
    )
  </foreach>
  </insert>
</mapper>
