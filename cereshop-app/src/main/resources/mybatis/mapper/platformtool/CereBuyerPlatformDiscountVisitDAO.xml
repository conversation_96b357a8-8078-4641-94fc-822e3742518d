<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.platformtool.CereBuyerPlatformDiscountVisitDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.buyer.CereBuyerPlatformDiscountVisit">
    <result column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="visit_time" jdbcType="VARCHAR" property="visitTime" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
  </resultMap>
  <sql id="Base_Column_List">
    discount_id, buyer_user_id, visit_time, shop_id
  </sql>
</mapper>
