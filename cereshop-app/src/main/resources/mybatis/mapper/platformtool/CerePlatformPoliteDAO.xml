<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.platformtool.CerePlatformPoliteDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.platformtool.CerePlatformPolite">
    <id column="polite_id" jdbcType="BIGINT" property="politeId" />
    <result column="polite_name" jdbcType="VARCHAR" property="politeName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="buyer_mode" jdbcType="BIT" property="buyerMode" />
    <result column="buyer" jdbcType="DECIMAL" property="buyer" />
    <result column="growth" jdbcType="INTEGER" property="growth" />
    <result column="credit" jdbcType="INTEGER" property="credit" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    polite_id, polite_name, remark, start_time, end_time, buyer_mode, buyer, growth,
    credit, `state`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_polite
    where polite_id = #{politeId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_platform_polite
    where polite_id = #{politeId,jdbcType=BIGINT}
  </delete>

  <select id="selectOnGoingPolite" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from cere_platform_polite
    where state = 1
    limit 1
  </select>
</mapper>
