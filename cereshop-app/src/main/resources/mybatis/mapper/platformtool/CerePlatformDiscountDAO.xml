<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.platformtool.CerePlatformDiscountDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.platformtool.CerePlatformDiscount">
    <id column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="discount_name" jdbcType="VARCHAR" property="discountName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sign_start_time" jdbcType="VARCHAR" property="signStartTime" />
    <result column="sign_end_time" jdbcType="VARCHAR" property="signEndTime" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="if_limit" jdbcType="BIT" property="ifLimit" />
    <result column="limit_number" jdbcType="INTEGER" property="limitNumber" />
    <result column="if_add" jdbcType="BIT" property="ifAdd" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="if_bond" jdbcType="BIT" property="ifBond" />
    <result column="bond_money" jdbcType="DECIMAL" property="bondMoney" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    discount_id, discount_name, remark, sign_start_time, sign_end_time, start_time, end_time,
    if_limit, limit_number, if_add, `state`, if_bond, bond_money,discount, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_discount
    where discount_id = #{discountId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_platform_discount
    where discount_id = #{discountId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="discount_id" keyProperty="discountId" parameterType="com.shop.cereshop.commons.domain.platformtool.CerePlatformDiscount" useGeneratedKeys="true">
    insert into cere_platform_discount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="discountName != null">
        discount_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="signStartTime != null">
        sign_start_time,
      </if>
      <if test="signEndTime != null">
        sign_end_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="ifLimit != null">
        if_limit,
      </if>
      <if test="limitNumber != null">
        limit_number,
      </if>
      <if test="ifAdd != null">
        if_add,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="ifBond != null">
        if_bond,
      </if>
      <if test="bondMoney != null">
        bond_money,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="discountName != null">
        #{discountName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="signStartTime != null">
        #{signStartTime,jdbcType=VARCHAR},
      </if>
      <if test="signEndTime != null">
        #{signEndTime,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="ifLimit != null">
        #{ifLimit,jdbcType=BIT},
      </if>
      <if test="limitNumber != null">
        #{limitNumber,jdbcType=INTEGER},
      </if>
      <if test="ifAdd != null">
        #{ifAdd,jdbcType=BIT},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="ifBond != null">
        #{ifBond,jdbcType=BIT},
      </if>
      <if test="bondMoney != null">
        #{bondMoney,jdbcType=DECIMAL},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.platformtool.CerePlatformDiscount">
    update cere_platform_discount
    <set>
      <if test="discountName != null">
        discount_name = #{discountName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="signStartTime != null">
        sign_start_time = #{signStartTime,jdbcType=VARCHAR},
      </if>
      <if test="signEndTime != null">
        sign_end_time = #{signEndTime,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="ifLimit != null">
        if_limit = #{ifLimit,jdbcType=BIT},
      </if>
      <if test="limitNumber != null">
        limit_number = #{limitNumber,jdbcType=INTEGER},
      </if>
      <if test="ifAdd != null">
        if_add = #{ifAdd,jdbcType=BIT},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="ifBond != null">
        if_bond = #{ifBond,jdbcType=BIT},
      </if>
      <if test="bondMoney != null">
        bond_money = #{bondMoney,jdbcType=DECIMAL},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where discount_id = #{discountId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.platformtool.CerePlatformDiscount">
    update cere_platform_discount
    set discount_name = #{discountName,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        sign_start_time = #{signStartTime,jdbcType=VARCHAR},
        sign_end_time = #{signEndTime,jdbcType=VARCHAR},
        start_time = #{startTime,jdbcType=VARCHAR},
        end_time = #{endTime,jdbcType=VARCHAR},
        if_limit = #{ifLimit,jdbcType=BIT},
        limit_number = #{limitNumber,jdbcType=INTEGER},
        if_add = #{ifAdd,jdbcType=BIT},
        `state` = #{state,jdbcType=BIT},
        if_bond = #{ifBond,jdbcType=BIT},
        bond_money = #{bondMoney,jdbcType=DECIMAL},
        discount = #{discount,jdbcType=DECIMAL},
        create_time = #{createTime,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=VARCHAR}
    where discount_id = #{discountId,jdbcType=BIGINT}
  </update>
  <update id="rollbackStock">
    update cere_sign_product a
    join cere_activity_sign b on a.sign_id = b.sign_id
    join cere_platform_discount c on b.activity_id = c.discount_id and b.sign_type = 2
    set a.number = a.number + #{buyNumber}
    where c.discount_id = #{discountId} and a.product_id = #{productId}
  </update>

  <select id="queryPlatformDiscountProductList"
          resultType="com.shop.cereshop.app.page.discount.PlatformDiscountProduct">
    select distinct c.product_id, ps.sku_id, d.shop_id, d.product_name, pi.product_image, ps.original_price,
                    min(ps.price) * a.discount / 10 as price, c.total,
                    (c.total - c.number) + d.fictitious_number as sale_number
    from cere_platform_discount a
     join cere_activity_sign b on b.activity_id = a.discount_id and b.sign_type = 3
     join cere_sign_product c on c.sign_id = b.sign_id
     join cere_shop_product d on d.product_id = c.product_id
     join cere_product_image pi on pi.product_id = c.product_id
     join cere_product_sku ps on ps.product_id = c.product_id
    where discount_id = #{discountId}
    group by c.product_id
    <if test="type != null">
      <if test="type == 1">
        order by price
      </if>
      <if test="type == 2">
        order by price desc
      </if>
    </if>
    <if test="volume != null">
      <if test="volume == 1">
        order by sale_number
      </if>
      <if test="volume == 2">
        order by sale_number desc
      </if>
    </if>
    <if test="volume == null and type == null">
      order by price
    </if>
  </select>
  <select id="getMinDiscount" resultType="com.shop.cereshop.app.page.canvas.CanvasPlatformDiscount">
    SELECT discount_id, discount_name, remark, sign_start_time, sign_end_time, start_time, end_time,
    if_limit, limit_number, if_add, `state`, if_bond, bond_money, discount FROM cere_platform_discount
    where state in (2,3)
    <if test="ids!=null and ids.size()>0">
      and discount_id in (
      <foreach collection="ids" item="id" index="index" separator=",">
        #{id}
      </foreach>
      )
    </if>
    ORDER BY start_time
  </select>
  <select id="findDistinctProducts" resultType="com.shop.cereshop.app.page.tool.ToolProduct">
    SELECT b.product_id,
           b.product_name,
           b.shop_id,
           c.original_price,
           pi.product_image image,
           a.number as stockNumber,
           c.price,
           c.sku_id,
           e.discount,
           a.total
    from cere_sign_product a
           LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
           LEFT JOIN (SELECT a.sku_id, a.product_id, a.stock_number, a.original_price, a.price, a.total
                      FROM cere_product_sku a,
                           cere_shop_product b
                      where a.product_id = b.product_id
                      GROUP BY a.product_id) c ON a.product_id = c.product_id
           LEFT JOIN (SELECT a.product_id, a.product_image
                      from cere_product_image a,
                           cere_shop_product b
                      where a.product_id = b.product_id
                      GROUP BY a.product_id) pi
                     ON b.product_id = pi.product_id
           LEFT JOIN cere_activity_sign d ON a.sign_id = d.sign_id and sign_type = 3
           LEFT JOIN cere_platform_discount e ON d.activity_id = e.discount_id
    where e.discount_id = #{discountId}
    GROUP BY a.product_id
  </select>
  <select id="selectByShopIdList" resultType="com.shop.cereshop.app.param.discount.ShopPlatformDiscount">
    select a.discount_id, d.shop_id, a.if_limit, a.limit_number, a.discount, c.product_id, c.number, c.total,
        a.if_add
    from cere_platform_discount a join cere_activity_sign b
    on b.activity_id = a.discount_id and b.sign_type = 3 and b.state = 1 and a.state = 3
    join cere_sign_product c on c.sign_id = b.sign_id
    join cere_shop_product d on d.product_id = c.product_id
    where d.shop_id in
    <foreach collection="list" item="shopId" open="(" separator="," close=")">
      #{shopId}
    </foreach>
  </select>

  <select id="selectProductStockInfo" resultType="com.shop.cereshop.app.page.product.ProductStockInfo">
    select c.total, c.number as stockNumber from cere_platform_discount a join cere_activity_sign b on b.activity_id = a.discount_id and b.sign_type = 3
    join cere_sign_product c on c.sign_id = b.sign_id
    where a.discount_id = #{platformDiscountId} and b.state = 1 and c.product_id = #{productId}
  </select>

  <select id="selectRelateInfoByProductId" resultType="com.shop.cereshop.app.param.discount.DiscountRelateProductInfo">
    select
      a.discount_id,
      a.discount,
      a.start_time,
      a.end_time,
      c.product_id,
      c.number,
      c.total,
      a.if_limit,
      a.limit_number,
      a.if_add
    from cere_platform_discount a
    join cere_activity_sign b on b.activity_id = a.discount_id and b.sign_type = 3 and b.state = 1
    join cere_sign_product c on c.sign_id = b.sign_id and c.product_id = #{productId}
    where a.start_time &lt;= now() and a.end_time > now() and a.state = 3
    order by a.start_time desc
    limit 1
  </select>

  <select id="queryProductIdListByDiscountId" resultType="java.lang.Long">
    select b.product_id from cere_activity_sign a
    join cere_sign_product b on b.sign_id = a.sign_id
    where a.activity_id = #{discountId} and a.sign_type = 3
  </select>
</mapper>
