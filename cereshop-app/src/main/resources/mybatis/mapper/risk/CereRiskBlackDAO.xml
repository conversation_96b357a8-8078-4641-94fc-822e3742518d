<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.risk.CereRiskBlackDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.risk.CereRiskBlack">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, ip, buyer_user_id, state, create_time, update_time
    </sql>

    <select id="getEnabled" resultType="com.shop.cereshop.commons.domain.risk.CereRiskBlack">
        select
        <include refid="Base_Column_List"/>
        from cere_risk_black
        where state = 1
    </select>

</mapper>
