<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.store.CereVerifyLogDAO">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.store.CereVerifyLog">
        <id column="log_id" jdbcType="BIGINT" property="logId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_sn" jdbcType="VARCHAR" property="orderSn"/>
        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
        <result column="order_product_id" jdbcType="BIGINT" property="orderProductId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="verify_type" jdbcType="TINYINT" property="verifyType"/>
        <result column="verify_times" jdbcType="INTEGER" property="verifyTimes"/>
        <result column="remaining_times" jdbcType="INTEGER" property="remainingTimes"/>
        <result column="verify_code" jdbcType="VARCHAR" property="verifyCode"/>
        <result column="staff_id" jdbcType="BIGINT" property="staffId"/>
        <result column="staff_name" jdbcType="VARCHAR" property="staffName"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="verify_time" jdbcType="TIMESTAMP" property="verifyTime"/>
        <result column="service_date" jdbcType="DATE" property="serviceDate"/>
        <result column="service_duration" jdbcType="INTEGER" property="serviceDuration"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        log_id, order_id, order_sn, merchant_id, order_product_id, product_id, 
        product_name, sku_name, verify_type, verify_times, remaining_times, 
        verify_code, staff_id, staff_name, store_id, store_name, verify_time, 
        service_date, service_duration, remark, create_time
    </sql>

    <!-- 根据订单ID查询核销记录 -->
    <select id="findByOrderId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE order_id = #{orderId,jdbcType=BIGINT}
        ORDER BY verify_time DESC
    </select>

    <!-- 根据订单号查询核销记录 -->
    <select id="findByOrderSn" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE order_sn = #{orderSn,jdbcType=VARCHAR}
        ORDER BY verify_time DESC
    </select>

    <!-- 根据核销码查询核销记录 -->
    <select id="findByVerifyCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE verify_code = #{verifyCode,jdbcType=VARCHAR}
        ORDER BY verify_time DESC
    </select>

</mapper>
