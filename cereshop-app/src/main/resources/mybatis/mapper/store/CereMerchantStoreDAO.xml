<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.store.CereMerchantStoreDAO">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.store.CereMerchantStore">
        <id column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="store_code" jdbcType="VARCHAR" property="storeCode"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="province_id" jdbcType="INTEGER" property="provinceId"/>
        <result column="city_id" jdbcType="INTEGER" property="cityId"/>
        <result column="area_id" jdbcType="INTEGER" property="areaId"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="longitude" jdbcType="DECIMAL" property="longitude"/>
        <result column="latitude" jdbcType="DECIMAL" property="latitude"/>
        <result column="business_hours" jdbcType="VARCHAR" property="businessHours"/>
        <result column="pickup_hours" jdbcType="VARCHAR" property="pickupHours"/>
        <result column="store_image" jdbcType="VARCHAR" property="storeImage"/>
        <result column="store_desc" jdbcType="LONGVARCHAR" property="storeDesc"/>
        <result column="is_pickup_enabled" jdbcType="TINYINT" property="isPickupEnabled"/>
        <result column="is_verify_enabled" jdbcType="TINYINT" property="isVerifyEnabled"/>
        <result column="sort_order" jdbcType="INTEGER" property="sortOrder"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        store_id, merchant_id, store_name, store_code, contact_name, contact_phone, 
        province_id, city_id, area_id, address, longitude, latitude, business_hours, 
        pickup_hours, store_image, store_desc, is_pickup_enabled, is_verify_enabled, 
        sort_order, status, create_time, update_time
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE store_id = #{storeId,jdbcType=BIGINT}
    </select>

    <!-- 根据商户ID查询启用的门店列表 -->
    <select id="findEnabledByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND status = 1
        ORDER BY sort_order DESC, create_time DESC
    </select>

    <!-- 根据商户ID查询支持自提的门店列表 -->
    <select id="findPickupEnabledByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND status = 1
        AND is_pickup_enabled = 1
        ORDER BY sort_order DESC, create_time DESC
    </select>

    <!-- 根据坐标范围查询附近门店 -->
    <select id="findNearbyStores" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>,
        (6371 * acos(cos(radians(#{latitude})) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(#{longitude})) + 
        sin(radians(#{latitude})) * sin(radians(latitude)))) AS distance
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND status = 1
        AND is_pickup_enabled = 1
        AND longitude IS NOT NULL
        AND latitude IS NOT NULL
        HAVING distance &lt;= #{distance}
        ORDER BY distance ASC
    </select>

    <!-- 检查门店是否支持自提 -->
    <select id="checkPickupEnabled" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_merchant_store
        WHERE store_id = #{storeId,jdbcType=BIGINT}
        AND status = 1
        AND is_pickup_enabled = 1
    </select>

    <!-- 检查门店是否支持核销 -->
    <select id="checkVerifyEnabled" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_merchant_store
        WHERE store_id = #{storeId,jdbcType=BIGINT}
        AND status = 1
        AND is_verify_enabled = 1
    </select>

</mapper>
