<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.channel.ShopChannelActivityDAO">

    <update id="updateCheckRemainCount">
        update cere_shop_channel_activity
        set remain_count = remain_count - 1
        where id = #{shopChannelActivityId} and remain_count >= 1
    </update>

</mapper>
