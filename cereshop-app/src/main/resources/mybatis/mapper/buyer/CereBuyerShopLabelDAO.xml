<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.buyer.CereBuyerShopLabelDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.label.CereBuyerShopLabel">
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.label.CereBuyerShopLabel">
    insert into cere_buyer_shop_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="labelId != null">
        label_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <update id="updateBuyerData" parameterType="java.lang.Object">
    UPDATE cere_buyer_shop_label SET buyer_user_id=#{buyerUserId} where buyer_user_id=#{id}
  </update>
</mapper>
