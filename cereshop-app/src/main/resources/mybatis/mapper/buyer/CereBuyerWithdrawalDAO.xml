<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.buyer.CereBuyerWithdrawalDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.buyer.CereBuyerWithdrawal">
    <id column="withdrawal_id" jdbcType="BIGINT" property="withdrawalId" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_card" jdbcType="VARCHAR" property="bankCard" />
    <result column="withdrawal_money" jdbcType="DECIMAL" property="withdrawalMoney" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="apply_time" jdbcType="VARCHAR" property="applyTime" />
    <result column="handle_time" jdbcType="VARCHAR" property="handleTime" />
  </resultMap>
  <sql id="Base_Column_List">
    withdrawal_id, buyer_user_id, bank_name, bank_card, withdrawal_money, `state`, apply_time,
    handle_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_buyer_withdrawal
    where withdrawal_id = #{withdrawalId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_buyer_withdrawal
    where withdrawal_id = #{withdrawalId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="withdrawal_id" keyProperty="withdrawalId" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerWithdrawal" useGeneratedKeys="true">
    insert into cere_buyer_withdrawal
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankCard != null">
        bank_card,
      </if>
      <if test="withdrawalMoney != null">
        withdrawal_money,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="handleTime != null">
        handle_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankCard != null">
        #{bankCard,jdbcType=VARCHAR},
      </if>
      <if test="withdrawalMoney != null">
        #{withdrawalMoney,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=VARCHAR},
      </if>
      <if test="handleTime != null">
        #{handleTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerWithdrawal">
    update cere_buyer_withdrawal
    <set>
      <if test="buyerUserId != null">
        buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankCard != null">
        bank_card = #{bankCard,jdbcType=VARCHAR},
      </if>
      <if test="withdrawalMoney != null">
        withdrawal_money = #{withdrawalMoney,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=VARCHAR},
      </if>
      <if test="handleTime != null">
        handle_time = #{handleTime,jdbcType=VARCHAR},
      </if>
    </set>
    where withdrawal_id = #{withdrawalId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerWithdrawal">
    update cere_buyer_withdrawal
    set buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_card = #{bankCard,jdbcType=VARCHAR},
      withdrawal_money = #{withdrawalMoney,jdbcType=DECIMAL},
      `state` = #{state,jdbcType=BIT},
      apply_time = #{applyTime,jdbcType=VARCHAR},
      handle_time = #{handleTime,jdbcType=VARCHAR}
    where withdrawal_id = #{withdrawalId,jdbcType=BIGINT}
  </update>

  <update id="updateBuyerData" parameterType="java.lang.Object">
    UPDATE cere_buyer_withdrawal SET buyer_user_id=#{buyerUserId} where buyer_user_id=#{id}
  </update>
</mapper>
