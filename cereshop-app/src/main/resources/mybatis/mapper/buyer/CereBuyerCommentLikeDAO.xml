<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.buyer.CereBuyerCommentLikeDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.buyer.CereBuyerCommentLike">
    <id column="comment_id" jdbcType="BIGINT" property="commentId" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="if_like" jdbcType="BIT" property="ifLike" />
  </resultMap>
  <sql id="Base_Column_List">
    comment_id, buyer_user_id, if_like
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_buyer_comment_like
    where comment_id = #{commentId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_buyer_comment_like
    where comment_id = #{commentId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerCommentLike">
    insert into cere_buyer_comment_like
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="commentId != null">
        comment_id,
      </if>
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="ifLike != null">
        if_like,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="commentId != null">
        #{commentId,jdbcType=BIGINT},
      </if>
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="ifLike != null">
        #{ifLike,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <insert id="insertOrUpdate">
    insert into cere_buyer_comment_like
    (buyer_user_id, comment_id, if_like)
    values (#{buyerUserId}, #{commentId}, #{ifLike})
    on duplicate key update if_like = #{ifLike}
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerCommentLike">
    update cere_buyer_comment_like
    <set>
      <if test="buyerUserId != null">
        buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="ifLike != null">
        if_like = #{ifLike,jdbcType=BIT},
      </if>
    </set>
    where comment_id = #{commentId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerCommentLike">
    update cere_buyer_comment_like
    set buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
      if_like = #{ifLike,jdbcType=BIT}
    where comment_id = #{commentId,jdbcType=BIGINT}
  </update>

  <select id="findByUserIdAndCommentId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.buyer.CereBuyerCommentLike">
    SELECT * FROM cere_buyer_comment_like where buyer_user_id=#{buyerUserId} and comment_id=#{commentId}
  </select>

  <update id="updateBuyerData" parameterType="java.lang.Object">
    UPDATE cere_buyer_comment_like SET buyer_user_id=#{buyerUserId} where buyer_user_id=#{id}
  </update>
</mapper>
