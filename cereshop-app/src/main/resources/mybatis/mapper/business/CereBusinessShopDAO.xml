<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.business.CereBusinessShopDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.business.CereBusinessShop">
    <result column="business_user_id" jdbcType="BIGINT" property="businessUserId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="if_binding" jdbcType="BIT" property="ifBinding" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.business.CereBusinessShop">
    insert into cere_business_shop
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessUserId != null">
        business_user_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="ifBinding != null">
        if_binding,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessUserId != null">
        #{businessUserId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="ifBinding != null">
        #{ifBinding,jdbcType=BIT},
      </if>
    </trim>
  </insert>
</mapper>
