<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.shop.CereShopExtensionDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.shop.CereShopExtension">
    <id column="extension_id" jdbcType="BIGINT" property="extensionId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="if_logo" jdbcType="BIT" property="ifLogo" />
    <result column="if_head" jdbcType="BIT" property="ifHead" />
    <result column="extension_reason" jdbcType="VARCHAR" property="extensionReason" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="poster" jdbcType="VARCHAR" property="poster" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    extension_id,title, shop_id, if_logo, if_head, extension_reason, image,poster, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_shop_extension
    where extension_id = #{extensionId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_shop_extension
    where extension_id = #{extensionId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="extension_id" keyProperty="extensionId" parameterType="com.shop.cereshop.commons.domain.shop.CereShopExtension" useGeneratedKeys="true">
    insert into cere_shop_extension
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="title != null and title!=''">
        title,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="ifLogo != null">
        if_logo,
      </if>
      <if test="ifHead != null">
        if_head,
      </if>
      <if test="extensionReason != null and extensionReason!=''">
        extension_reason,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="poster != null and poster!=''">
        poster,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="title != null and title!=''">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="ifLogo != null">
        #{ifLogo,jdbcType=BIT},
      </if>
      <if test="ifHead != null">
        #{ifHead,jdbcType=BIT},
      </if>
      <if test="extensionReason != null and extensionReason!=''">
        #{extensionReason,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="poster != null and poster!=''">
        #{poster,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.shop.CereShopExtension">
    update cere_shop_extension
    <set>
      <if test="title != null and title!=''">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="ifLogo != null">
        if_logo = #{ifLogo,jdbcType=BIT},
      </if>
      <if test="ifHead != null">
        if_head = #{ifHead,jdbcType=BIT},
      </if>
      <if test="extensionReason != null and extensionReason!=''">
        extension_reason = #{extensionReason,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="poster != null and poster!=''">
        poster = #{poster,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where extension_id = #{extensionId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.shop.CereShopExtension">
    update cere_shop_extension
    set title = #{title,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=BIGINT},
      if_logo = #{ifLogo,jdbcType=BIT},
      if_head = #{ifHead,jdbcType=BIT},
      extension_reason = #{extensionReason,jdbcType=VARCHAR},
      image = #{image,jdbcType=VARCHAR},
      poster = #{poster,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where extension_id = #{extensionId,jdbcType=BIGINT}
  </update>

  <select id="findByShopIdAndTitle" parameterType="java.lang.Object" resultType="com.shop.cereshop.app.page.shop.Extension">
    SELECT shop_id,if_head,extension_reason,image,poster,if_logo FROM cere_shop_extension where shop_id=#{shopId} and title=#{title}
  </select>
</mapper>
