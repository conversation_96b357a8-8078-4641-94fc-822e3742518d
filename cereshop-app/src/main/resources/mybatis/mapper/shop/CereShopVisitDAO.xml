<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.shop.CereShopVisitDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.shop.CereShopVisit">
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="visit_time" jdbcType="VARCHAR" property="visitTime" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.shop.CereShopVisit">
    insert into cere_shop_visit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="visitTime != null and visitTime!=''">
        visit_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="visitTime != null and visitTime!=''">
        #{visitTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateBuyerData" parameterType="java.lang.Object">
    UPDATE cere_shop_visit SET buyer_user_id=#{buyerUserId} where buyer_user_id=#{id}
  </update>
</mapper>
