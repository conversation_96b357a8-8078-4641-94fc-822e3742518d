<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.app.dao.shop.CereShopIndividualBusinessesDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.shop.CereShopIndividualBusinesses">
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="subject_name" jdbcType="VARCHAR" property="subjectName" />
    <result column="subject_code" jdbcType="VARCHAR" property="subjectCode" />
    <result column="subject_region" jdbcType="VARCHAR" property="subjectRegion" />
    <result column="subject_adress" jdbcType="VARCHAR" property="subjectAdress" />
    <result column="subject_start_time" jdbcType="VARCHAR" property="subjectStartTime" />
    <result column="subject_end_time" jdbcType="VARCHAR" property="subjectEndTime" />
    <result column="subject_license" jdbcType="VARCHAR" property="subjectLicense" />
    <result column="subject_operator" jdbcType="VARCHAR" property="subjectOperator" />
    <result column="subject_card_type" jdbcType="BIGINT" property="subjectCardType" />
    <result column="subject_id_card" jdbcType="VARCHAR" property="subjectIdCard" />
    <result column="subject_card_start_time" jdbcType="VARCHAR" property="subjectCardStartTime" />
    <result column="subject_card_end_time" jdbcType="VARCHAR" property="subjectCardEndTime" />
    <result column="subject_card_positive" jdbcType="VARCHAR" property="subjectCardPositive" />
    <result column="subject_card_side" jdbcType="VARCHAR" property="subjectCardSide" />
    <result column="administrators_phone" jdbcType="VARCHAR" property="administratorsPhone" />
    <result column="administrators_email" jdbcType="VARCHAR" property="administratorsEmail" />
    <result column="account_type" jdbcType="BIT" property="accountType" />
    <result column="account_open_bank" jdbcType="BIGINT" property="accountOpenBank" />
    <result column="account_bank_region" jdbcType="VARCHAR" property="accountBankRegion" />
    <result column="account_bank_card" jdbcType="VARCHAR" property="accountBankCard" />
    <result column="shop_abbreviation" jdbcType="VARCHAR" property="shopAbbreviation" />
    <result column="service_phone" jdbcType="VARCHAR" property="servicePhone" />
    <result column="service_providing" jdbcType="BIGINT" property="serviceProviding" />
    <result column="shop_index_image" jdbcType="VARCHAR" property="shopIndexImage" />
    <result column="shop_backstage_image" jdbcType="VARCHAR" property="shopBackstageImage" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>

  <insert id="insertParam" parameterType="com.shop.cereshop.app.param.shop.CereShopIndividualBusinessesParam">
    insert into cere_shop_individual_businesses (shop_id, subject_name,
    subject_code, subject_region, subject_adress,
    subject_start_time, subject_end_time, subject_license,
    subject_operator, subject_card_type, subject_id_card,
    subject_card_start_time, subject_card_end_time,
    subject_card_positive, subject_card_side, administrators_phone,
    administrators_email, account_type, account_open_bank,
    account_bank_region, account_bank_card, shop_abbreviation,
    service_phone, service_providing, shop_index_image,
    shop_backstage_image, remark,
    create_time, update_time)
    values (#{shopId,jdbcType=BIGINT},  #{subjectName,jdbcType=VARCHAR},
    #{subjectCode,jdbcType=VARCHAR}, #{subjectRegion,jdbcType=VARCHAR}, #{subjectAdress,jdbcType=VARCHAR},
    #{subjectStartTime,jdbcType=VARCHAR}, #{subjectEndTime,jdbcType=VARCHAR}, #{subjectLicense,jdbcType=VARCHAR},
    #{subjectOperator,jdbcType=VARCHAR}, #{subjectCardType,jdbcType=BIGINT}, #{subjectIdCard,jdbcType=VARCHAR},
    #{subjectCardStartTime,jdbcType=VARCHAR}, #{subjectCardEndTime,jdbcType=VARCHAR},
    #{subjectCardPositive,jdbcType=VARCHAR}, #{subjectCardSide,jdbcType=VARCHAR}, #{administratorsPhone,jdbcType=VARCHAR},
    #{administratorsEmail,jdbcType=VARCHAR}, #{accountType,jdbcType=BIT}, #{accountOpenBank,jdbcType=BIGINT},
    #{accountBankRegion,jdbcType=VARCHAR}, #{accountBankCard,jdbcType=VARCHAR}, #{shopAbbreviation,jdbcType=VARCHAR},
    #{servicePhone,jdbcType=VARCHAR}, #{serviceProviding,jdbcType=BIGINT}, #{shopIndexImage,jdbcType=VARCHAR},
    #{shopBackstageImage,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
    #{createTime,jdbcType=VARCHAR}, #{updateTime,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.shop.CereShopIndividualBusinesses">
    insert into cere_shop_individual_businesses
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="subjectName != null and subjectName!=''">
        subject_name,
      </if>
      <if test="subjectCode != null and subjectCode!=''">
        subject_code,
      </if>
      <if test="subjectRegion != null and subjectRegion!=''">
        subject_region,
      </if>
      <if test="subjectAdress != null and subjectAdress!=''">
        subject_adress,
      </if>
      <if test="subjectStartTime != null and subjectStartTime!=''">
        subject_start_time,
      </if>
      <if test="subjectEndTime != null and subjectEndTime!=''">
        subject_end_time,
      </if>
      <if test="subjectLicense != null and subjectLicense!=''">
        subject_license,
      </if>
      <if test="subjectOperator != null and subjectOperator!=''">
        subject_operator,
      </if>
      <if test="subjectCardType != null">
        subject_card_type,
      </if>
      <if test="subjectIdCard != null and subjectIdCard!=''">
        subject_id_card,
      </if>
      <if test="subjectCardStartTime != null and subjectCardStartTime!=''">
        subject_card_start_time,
      </if>
      <if test="subjectCardEndTime != null and subjectCardEndTime!=''">
        subject_card_end_time,
      </if>
      <if test="subjectCardPositive != null and subjectCardPositive!=''">
        subject_card_positive,
      </if>
      <if test="subjectCardSide != null and subjectCardSide!=''">
        subject_card_side,
      </if>
      <if test="administratorsPhone != null and administratorsPhone!=''">
        administrators_phone,
      </if>
      <if test="administratorsEmail != null and administratorsEmail!=''">
        administrators_email,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="accountOpenBank != null">
        account_open_bank,
      </if>
      <if test="accountBankRegion != null and accountBankRegion!=''">
        account_bank_region,
      </if>
      <if test="accountBankCard != null and accountBankCard!=''">
        account_bank_card,
      </if>
      <if test="shopAbbreviation != null and shopAbbreviation!=''">
        shop_abbreviation,
      </if>
      <if test="servicePhone != null and servicePhone!=''">
        service_phone,
      </if>
      <if test="serviceProviding != null">
        service_providing,
      </if>
      <if test="shopIndexImage != null and shopIndexImage!=''">
        shop_index_image,
      </if>
      <if test="shopBackstageImage != null and shopBackstageImage!=''">
        shop_backstage_image,
      </if>
      <if test="remark != null and remark!=''">
        remark,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="subjectName != null and subjectName!=''">
        #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="subjectCode != null and subjectCode!=''">
        #{subjectCode,jdbcType=VARCHAR},
      </if>
      <if test="subjectRegion != null and subjectRegion!=''">
        #{subjectRegion,jdbcType=VARCHAR},
      </if>
      <if test="subjectAdress != null and subjectAdress!=''">
        #{subjectAdress,jdbcType=VARCHAR},
      </if>
      <if test="subjectStartTime != null and subjectStartTime!=''">
        #{subjectStartTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectEndTime != null and subjectEndTime!=''">
        #{subjectEndTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectLicense != null and subjectLicense!=''">
        #{subjectLicense,jdbcType=VARCHAR},
      </if>
      <if test="subjectOperator != null and subjectOperator!=''">
        #{subjectOperator,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardType != null">
        #{subjectCardType,jdbcType=BIGINT},
      </if>
      <if test="subjectIdCard != null and subjectIdCard!=''">
        #{subjectIdCard,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardStartTime != null and subjectCardStartTime!=''">
        #{subjectCardStartTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardEndTime != null and subjectCardEndTime!=''">
        #{subjectCardEndTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardPositive != null and subjectCardPositive!=''">
        #{subjectCardPositive,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardSide != null and subjectCardSide!=''">
        #{subjectCardSide,jdbcType=VARCHAR},
      </if>
      <if test="administratorsPhone != null and administratorsPhone!=''">
        #{administratorsPhone,jdbcType=VARCHAR},
      </if>
      <if test="administratorsEmail != null and administratorsEmail!=''">
        #{administratorsEmail,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=BIT},
      </if>
      <if test="accountOpenBank != null">
        #{accountOpenBank,jdbcType=BIGINT},
      </if>
      <if test="accountBankRegion != null and accountBankRegion!=''">
        #{accountBankRegion,jdbcType=VARCHAR},
      </if>
      <if test="accountBankCard != null and accountBankCard!=''">
        #{accountBankCard,jdbcType=VARCHAR},
      </if>
      <if test="shopAbbreviation != null and shopAbbreviation!=''">
        #{shopAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="servicePhone != null and servicePhone!=''">
        #{servicePhone,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviding != null">
        #{serviceProviding,jdbcType=BIGINT},
      </if>
      <if test="shopIndexImage != null and shopIndexImage!=''">
        #{shopIndexImage,jdbcType=VARCHAR},
      </if>
      <if test="shopBackstageImage != null and shopBackstageImage!=''">
        #{shopBackstageImage,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateIndividual" parameterType="com.shop.cereshop.commons.domain.shop.CereShopIndividualBusinesses">
    UPDATE cere_shop_individual_businesses
    <set>
      <if test="subjectName != null and subjectName!=''">
        subject_name = #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="subjectCode != null and subjectCode!=''">
        subject_code = #{subjectCode,jdbcType=VARCHAR},
      </if>
      <if test="subjectRegion != null and subjectRegion!=''">
        subject_region = #{subjectRegion,jdbcType=VARCHAR},
      </if>
      <if test="subjectAdress != null and subjectAdress!=''">
        subject_adress = #{subjectAdress,jdbcType=VARCHAR},
      </if>
      <if test="subjectStartTime != null and subjectStartTime!=''">
        subject_start_time = #{subjectStartTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectEndTime != null and subjectEndTime!=''">
        subject_end_time = #{subjectEndTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectLicense != null and subjectLicense!=''">
        subject_license = #{subjectLicense,jdbcType=VARCHAR},
      </if>
      <if test="subjectOperator != null and subjectOperator!=''">
        subject_operator = #{subjectOperator,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardType != null">
        subject_card_type = #{subjectCardType,jdbcType=BIGINT},
      </if>
      <if test="subjectIdCard != null and subjectIdCard!=''">
        subject_id_card = #{subjectIdCard,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardStartTime != null and subjectCardStartTime!=''">
        subject_card_start_time = #{subjectCardStartTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardEndTime != null and subjectCardEndTime!=''">
        subject_card_end_time = #{subjectCardEndTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardPositive != null and subjectCardPositive!=''">
        subject_card_positive = #{subjectCardPositive,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardSide != null and subjectCardSide!=''">
        subject_card_side = #{subjectCardSide,jdbcType=VARCHAR},
      </if>
      <if test="administratorsPhone != null and administratorsPhone!=''">
        administrators_phone = #{administratorsPhone,jdbcType=VARCHAR},
      </if>
      <if test="administratorsEmail != null and administratorsEmail!=''">
        administrators_email = #{administratorsEmail,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=BIT},
      </if>
      <if test="accountOpenBank != null">
        account_open_bank = #{accountOpenBank,jdbcType=BIGINT},
      </if>
      <if test="accountBankRegion != null and accountBankRegion!=''">
        account_bank_region = #{accountBankRegion,jdbcType=VARCHAR},
      </if>
      <if test="accountBankCard != null and accountBankCard!=''">
        account_bank_card = #{accountBankCard,jdbcType=VARCHAR},
      </if>
      <if test="shopAbbreviation != null and shopAbbreviation!=''">
        shop_abbreviation = #{shopAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="servicePhone != null and servicePhone!=''">
        service_phone = #{servicePhone,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviding != null">
        service_providing = #{serviceProviding,jdbcType=BIGINT},
      </if>
      <if test="shopIndexImage != null and shopIndexImage!=''">
        shop_index_image = #{shopIndexImage,jdbcType=VARCHAR},
      </if>
      <if test="shopBackstageImage != null and shopBackstageImage!=''">
        shop_backstage_image = #{shopBackstageImage,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where shop_id=#{shopId}
  </update>

  <update id="updateParam" parameterType="com.shop.cereshop.app.param.shop.CereShopIndividualBusinessesParam">
    UPDATE cere_shop_individual_businesses
    <set>
      <if test="subjectName != null and subjectName!=''">
        subject_name = #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="subjectCode != null and subjectCode!=''">
        subject_code = #{subjectCode,jdbcType=VARCHAR},
      </if>
      <if test="subjectRegion != null and subjectRegion!=''">
        subject_region = #{subjectRegion,jdbcType=VARCHAR},
      </if>
      <if test="subjectAdress != null and subjectAdress!=''">
        subject_adress = #{subjectAdress,jdbcType=VARCHAR},
      </if>
      <if test="subjectStartTime != null and subjectStartTime!=''">
        subject_start_time = #{subjectStartTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectEndTime != null and subjectEndTime!=''">
        subject_end_time = #{subjectEndTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectLicense != null and subjectLicense!=''">
        subject_license = #{subjectLicense,jdbcType=VARCHAR},
      </if>
      <if test="subjectOperator != null and subjectOperator!=''">
        subject_operator = #{subjectOperator,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardType != null">
        subject_card_type = #{subjectCardType,jdbcType=BIGINT},
      </if>
      <if test="subjectIdCard != null and subjectIdCard!=''">
        subject_id_card = #{subjectIdCard,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardStartTime != null and subjectCardStartTime!=''">
        subject_card_start_time = #{subjectCardStartTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardEndTime != null and subjectCardEndTime!=''">
        subject_card_end_time = #{subjectCardEndTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardPositive != null and subjectCardPositive!=''">
        subject_card_positive = #{subjectCardPositive,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardSide != null and subjectCardSide!=''">
        subject_card_side = #{subjectCardSide,jdbcType=VARCHAR},
      </if>
      <if test="administratorsPhone != null and administratorsPhone!=''">
        administrators_phone = #{administratorsPhone,jdbcType=VARCHAR},
      </if>
      <if test="administratorsEmail != null and administratorsEmail!=''">
        administrators_email = #{administratorsEmail,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=BIT},
      </if>
      <if test="accountOpenBank != null">
        account_open_bank = #{accountOpenBank,jdbcType=BIGINT},
      </if>
      <if test="accountBankRegion != null and accountBankRegion!=''">
        account_bank_region = #{accountBankRegion,jdbcType=VARCHAR},
      </if>
      <if test="accountBankCard != null and accountBankCard!=''">
        account_bank_card = #{accountBankCard,jdbcType=VARCHAR},
      </if>
      <if test="shopAbbreviation != null and shopAbbreviation!=''">
        shop_abbreviation = #{shopAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="servicePhone != null and servicePhone!=''">
        service_phone = #{servicePhone,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviding != null">
        service_providing = #{serviceProviding,jdbcType=BIGINT},
      </if>
      <if test="shopIndexImage != null and shopIndexImage!=''">
        shop_index_image = #{shopIndexImage,jdbcType=VARCHAR},
      </if>
      <if test="shopBackstageImage != null and shopBackstageImage!=''">
        shop_backstage_image = #{shopBackstageImage,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where shop_id=#{shopId}
  </update>

  <select id="findByShopId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.shop.CereShopIndividualBusinesses">
    SELECT * FROM cere_shop_individual_businesses where shop_id=#{shopId}
  </select>
</mapper>
