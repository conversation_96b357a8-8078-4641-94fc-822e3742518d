
server:
  port: 9007
#  servlet:
#    context-path: /scott
  # THYMELEAF 网页静态化配置
  thymeleaf:
    # 是否启用模板缓存
    cache: false
    # 应用于模板的模板模式，参考TemplateMode 枚举
    mode: HTML5
    # 模板文件编码
    encoding: UTF-8
    # 构建URL时预先查看视图名称的前缀
    prefix: classpath:/templates/
    # 在构建URL时附加到视图名称的后缀
    suffix: .html
    # 文件解析格式
    content-type: text/html
  resources: # 指定静态资源的路径
    static-locations: classpath:/static/,classpath:/templates/
  # 默认值为 classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
  static-path-pattern: /**
  address: 0.0.0.0

spring:
  # 开发环境|dev正式环境prod
  profiles:
    active: app-dev,security
  # jackson
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

# Mybatis-plus
mybatis-plus:
  mapper-locations: mybatis/mapper/*/*.xml
  type-aliases-package: com.shop.cereshop.commons.domain
  configuration.map-underscore-to-camel-case: true

#快递100配置
kd100:
  key: sxx
  customer: xx
  queryUrl: https://poll.kuaidi100.com/poll/query.do

#快递鸟配置
kdNiao:
  eBusinessID: xx
  appKey: xxx
  queryUrl: http://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx
  getCodeUrl: https://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx

#商品缓存时间，单位：毫秒
productCacheTime: 300000

#密码加密传输，前端公钥加密，后端私钥解密
rsa:
  private_key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAN31AUTArXLkMhf7uZA5o1oJbNBzDjCZIaadFPzL1eHrGrEjD82EHGQQYBUjUq7IhwkBmnFufi+wbb5NVxUq3SVLpseRzrUajtexLe+tLuV0YHKejiM8EeI0C2mE5v3fz6rHv1lTyCUfA5d9nM8Dbuvdzm8Ew8rwdHb81plNkao5AgMBAAECgYBhd5R+B4pjOHgS9hRvlA/6msbw5oVlq+kODE7KEAy1L5PCE5595jS9cD9xKNjHLgG2X8DdbOL2uCgI+Fd0USVYyKPY1t6M3OL5FqP6N1tKAnyB1frT6waJpI0jhqIwNcTVdMxw/+M2GQyZz+pWaa1XfOHw4HngxxYr9kkDdoytcQJBAPx3YLbViOeEuanDl5IinkG1ScP8yUd//571jWgcUlDc46wVHcZHIJDLTPVvYX2I78KIASo1MzQRqoFjo1i7oz0CQQDhEE7kzJv0/KHThzFxgarInQNYt1FYjwhZ8SXdtU4KrHnB1Nxue+KimhRIieIouLSzjR0o+xZcovXuPDZD02KtAkEAng8HB7BsQ7X+nuALVrxBpBWhlwf947upB5Xn79Q7XNDN4QBfYbFKHByLk+UlffhrUrjfMdOYuPKOcu80ZusiaQJAYlVXwwvK0D1ZDyywY6F91u0TOz3NIt5LjdCY9Md0c6FsCU5RiUxDdzvOxOrJj3U0LobnSLJ70Lm6rf5Mz1GaeQJAVjFUSxG0NaOt4Q2M0hBa1Et2jtjQ6jfmZ2gVvcEwwwMkcXfvdlsNaKU21Vb1iYJmrdEcdZouCLMakAbPA/j/Ng==

#用户默认头像地址
defaultHeadImg: https://zk-cereshop.oss-cn-shenzhen.aliyuncs.com/zkthink/2022-01-11/6a05d44b9e0947a2add5a37c94953750_default_head_img.png

#scrm埋点上报链接
scrmTrackReportUrl: https://scrm-demo.zkthink.com/stage-api/wecom/clue-user/add

#查询sku实时信息表
querySkuRealInfo: false

#刷新sku实时信息的校验码verifyCode
verifyCode:

# sa-token 配置
sa-token:
  # 是否打印操作日志
  is-log: true
  # sso-client 相关配置
  sso-client:
    # client 标识
    client: cereshop-app
    # sso-server 端主机地址
    server-url: https://uat-oss.puhuicare.com/api
    # 使用 Http 请求校验ticket (模式三)
    is-http: true
    # API 接口调用秘钥
    secret-key: SSO-cereshop-app-kQwIOrYvnXmSDkwEiFngrKidMcdrgKor

