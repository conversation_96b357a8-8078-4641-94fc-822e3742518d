/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.app.service.shop;

import com.shop.cereshop.app.param.shop.CereShopIndividualBusinessesParam;
import com.shop.cereshop.commons.domain.buyer.CereBuyerUser;
import com.shop.cereshop.commons.domain.shop.CereShopIndividualBusinesses;
import com.shop.cereshop.commons.exception.CoBusinessException;

public interface CereShopIndividualBusinessesService {
    void individual(CereShopIndividualBusinesses individualBusinesses, CereBuyerUser user) throws CoBusinessException;

    void updateIndividual(CereShopIndividualBusinesses individualBusinesses, CereBuyerUser user) throws CoBusinessException;

    CereShopIndividualBusinesses findByShopId(Long shopId);

    void individualCheck(CereShopIndividualBusinessesParam param, <PERSON>reBuyerUser user) throws CoBusinessException;

    void updateIndividualCheck(CereShopIndividualBusinessesParam param, <PERSON>re<PERSON>uyerUser user) throws CoBusinessException;
}
