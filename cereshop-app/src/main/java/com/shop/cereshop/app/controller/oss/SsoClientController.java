package com.shop.cereshop.app.controller.oss;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.spring.SpringMVCUtil;
import cn.dev33.satoken.sso.model.SaCheckTicketResult;
import cn.dev33.satoken.sso.processor.SaSsoClientProcessor;
import cn.dev33.satoken.sso.template.SaSsoClientTemplate;
import com.alibaba.fastjson.JSONObject;
import com.shop.cereshop.app.page.login.BuyerUser;
import com.shop.cereshop.app.param.index.LoginParam;
import com.shop.cereshop.app.service.buyer.CereBuyerUserService;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.result.Result;
import com.shop.cereshop.commons.utils.AppletPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * SSO Client端 Controller 
 * <AUTHOR>
 */
@Slf4j
@RestController
public class SsoClientController{



	@Autowired
	HttpServletRequest request;

	@Autowired
	private CereBuyerUserService cereBuyerUserService;
	/*
	 * SSO-Client端：处理所有 SSO 相关请求
	 * 		http://{host}:{port}/sso/login			-- Client 端登录地址
	 * 		http://{host}:{port}/sso/logout			-- Client 端注销地址（isSlo=true时打开）
	 * 		http://{host}:{port}/sso/pushC			-- Client 端接收消息推送地址
	 */
	@RequestMapping("/sso/*")
	public Object ssoLogin() {
		return SaSsoClientProcessor.instance.dister();
	}

	// 当前应用独自注销 (不退出其它应用)
	@RequestMapping("/sso/logoutByAlone")
	public Object logoutByAlone(HttpSession session) {
		session.removeAttribute("userId");
		return SaSsoClientProcessor.instance._ssoLogoutBack(SaHolder.getRequest(), SaHolder.getResponse());
	}

	// 配置SSO相关参数
	@Autowired
	private void configSso(SaSsoClientTemplate ssoClientTemplate) {


		// 自定义校验 ticket 返回值的处理逻辑 （每次从认证中心获取校验 ticket 的结果后调用）
		ssoClientTemplate.strategy.ticketResultHandle = (ctr, back) -> {
			HttpSession session = SpringMVCUtil.getRequest().getSession();
			session.setAttribute("userId", ctr.loginId);
			return SaHolder.getResponse().redirect(back);
		};
	}



	// 根据 ticket 进行登录
	@SaIgnore
	@RequestMapping("/sso/doLoginByTicket")
	public Result<BuyerUser> doLoginByTicket(String ticket) throws CoBusinessException {
		SaCheckTicketResult ctr = SaSsoClientProcessor.instance.checkTicket(ticket);
		String open = (String) ctr.result.get("wechatOpenId");
		String phone = (String) ctr.result.get("phone");


		LoginParam param = new LoginParam();
		param.setCode(open);
		param.setPhone(phone);
		BuyerUser personal=cereBuyerUserService.wxLoginBySSO(param);
		return new Result(personal,CoReturnFormat.SUCCESS);

	}



}
