/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.app.dao.store;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商户门店DAO接口（App模块）
 * <AUTHOR>
@Mapper
public interface CereMerchantStoreDAO extends BaseMapper<CereMerchantStore> {
    
    /**
     * 根据主键查询
     */
    CereMerchantStore selectByPrimaryKey(Long storeId);

    /**
     * 根据商户ID查询启用的门店列表
     */
    List<CereMerchantStore> findEnabledByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 根据商户ID查询支持自提的门店列表
     */
    List<CereMerchantStore> findPickupEnabledByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 根据坐标范围查询附近门店
     */
    List<CereMerchantStore> findNearbyStores(@Param("merchantId") Long merchantId,
                                           @Param("longitude") Double longitude,
                                           @Param("latitude") Double latitude,
                                           @Param("distance") Double distance);

    /**
     * 检查门店是否支持自提
     */
    int checkPickupEnabled(@Param("storeId") Long storeId);

    /**
     * 检查门店是否支持核销
     */
    int checkVerifyEnabled(@Param("storeId") Long storeId);
}
