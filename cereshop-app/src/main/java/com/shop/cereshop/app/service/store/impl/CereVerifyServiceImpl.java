/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.app.service.store.impl;

import com.shop.cereshop.app.dao.store.CereVerifyLogDAO;
import com.shop.cereshop.app.service.store.CereVerifyService;
import com.shop.cereshop.commons.domain.store.CereVerifyLog;
import com.shop.cereshop.commons.utils.RandomStringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 核销服务实现类（App模块）
 * <AUTHOR>
@Service
@Slf4j
public class CereVerifyServiceImpl implements CereVerifyService {

    @Autowired
    private CereVerifyLogDAO cereVerifyLogDAO;

    @Override
    public String generateVerifyCode(Long orderId) {
        if (orderId == null) {
            return null;
        }
        
        // 生成格式：CS + 8位随机数字
        String prefix = "CS";
        String randomCode = RandomStringUtil.getRandomCode(8, 1); // 纯数字
        return prefix + randomCode;
    }

    @Override
    public List<CereVerifyLog> getVerifyLogsByOrderId(Long orderId) {
        if (orderId == null) {
            return null;
        }
        return cereVerifyLogDAO.findByOrderId(orderId);
    }

    @Override
    public List<CereVerifyLog> getVerifyLogsByOrderSn(String orderSn) {
        if (orderSn == null || orderSn.trim().isEmpty()) {
            return null;
        }
        return cereVerifyLogDAO.findByOrderSn(orderSn);
    }

    @Override
    public List<CereVerifyLog> getVerifyLogsByVerifyCode(String verifyCode) {
        if (verifyCode == null || verifyCode.trim().isEmpty()) {
            return null;
        }
        return cereVerifyLogDAO.findByVerifyCode(verifyCode);
    }
}
