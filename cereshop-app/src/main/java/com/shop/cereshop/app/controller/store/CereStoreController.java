/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.app.controller.store;

import com.shop.cereshop.app.service.store.CereMerchantStoreService;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户端门店查询Controller
 * <AUTHOR>
@RestController
@RequestMapping("/app/store")
@Api(tags = "门店查询")
@Slf4j
public class CereStoreController {

    @Autowired
    private CereMerchantStoreService cereMerchantStoreService;

    /**
     * 根据商户ID获取支持自提的门店列表
     */
    @GetMapping("/pickup/{merchantId}")
    @ApiOperation("获取支持自提的门店列表")
    public Result<List<CereMerchantStore>> getPickupStoresByMerchantId(@PathVariable Long merchantId) {
        try {
            List<CereMerchantStore> stores = cereMerchantStoreService.getPickupEnabledStoresByMerchantId(merchantId);
            return new Result<>(stores, CoReturnFormat.SUCCESS);
        } catch (Exception e) {
            log.error("获取支持自提门店列表失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据商户ID获取附近支持自提的门店
     */
    @GetMapping("/pickup/{merchantId}/nearby")
    @ApiOperation("获取附近支持自提的门店")
    public Result<List<CereMerchantStore>> getNearbyPickupStores(@PathVariable Long merchantId,
                                                                @RequestParam Double longitude,
                                                                @RequestParam Double latitude,
                                                                @RequestParam(defaultValue = "50") Double distance) {
        try {
            List<CereMerchantStore> stores = cereMerchantStoreService.getNearbyStores(merchantId, longitude, latitude, distance);
            return new Result<>(stores, CoReturnFormat.SUCCESS);
        } catch (Exception e) {
            log.error("获取附近支持自提门店失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据门店ID获取门店详情
     */
    @GetMapping("/{storeId}")
    @ApiOperation("获取门店详情")
    public Result<CereMerchantStore> getStoreById(@PathVariable Long storeId) {
        try {
            CereMerchantStore store = cereMerchantStoreService.getStoreById(storeId);
            return new Result<>(store, CoReturnFormat.SUCCESS);
        } catch (CoBusinessException e) {
            return new Result<>(e.getCode());
        } catch (Exception e) {
            log.error("获取门店详情失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 检查门店是否支持自提
     */
    @GetMapping("/{storeId}/pickup-enabled")
    @ApiOperation("检查门店是否支持自提")
    public Result<Boolean> isPickupEnabled(@PathVariable Long storeId) {
        try {
            boolean enabled = cereMerchantStoreService.isPickupEnabled(storeId);
            return new Result<>(enabled, CoReturnFormat.SUCCESS);
        } catch (CoBusinessException e) {
            return new Result<>(e.getCode());
        } catch (Exception e) {
            log.error("检查门店自提支持失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 检查门店是否支持核销
     */
    @GetMapping("/{storeId}/verify-enabled")
    @ApiOperation("检查门店是否支持核销")
    public Result<Boolean> isVerifyEnabled(@PathVariable Long storeId) {
        try {
            boolean enabled = cereMerchantStoreService.isVerifyEnabled(storeId);
            return new Result<>(enabled, CoReturnFormat.SUCCESS);
        } catch (CoBusinessException e) {
            return new Result<>(e.getCode());
        } catch (Exception e) {
            log.error("检查门店核销支持失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }
}
