/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.app.dao.store;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.commons.domain.store.CereVerifyLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 核销记录DAO接口（App模块）
 * <AUTHOR>
@Mapper
public interface CereVerifyLogDAO extends BaseMapper<CereVerifyLog> {
    
    /**
     * 根据订单ID查询核销记录
     */
    List<CereVerifyLog> findByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单号查询核销记录
     */
    List<CereVerifyLog> findByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 根据核销码查询核销记录
     */
    List<CereVerifyLog> findByVerifyCode(@Param("verifyCode") String verifyCode);
}
