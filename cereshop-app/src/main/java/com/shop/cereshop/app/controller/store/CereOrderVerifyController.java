/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.app.controller.store;

import com.shop.cereshop.app.page.order.OrderDetail;
import com.shop.cereshop.app.param.order.OrderGetByIdParam;
import com.shop.cereshop.app.service.store.CereVerifyService;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.domain.buyer.CereBuyerUser;
import com.shop.cereshop.commons.domain.store.CereVerifyLog;
import com.shop.cereshop.commons.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 用户端订单核销查询Controller
 * <AUTHOR>
@RestController
@RequestMapping("/app/order/verify")
@Api(tags = "订单核销查询")
@Slf4j
public class CereOrderVerifyController {

    @Autowired
    private CereVerifyService cereVerifyService;

    /**
     * 根据订单ID查询核销记录
     */
    @GetMapping("/logs/{orderId}")
    @ApiOperation("根据订单ID查询核销记录")
    public Result<List<CereVerifyLog>> getVerifyLogsByOrderId(@PathVariable Long orderId,
                                                             HttpServletRequest request) {
        try {
            // 获取当前登录用户
            CereBuyerUser buyerUser = (CereBuyerUser) request.getAttribute("user");
            if (buyerUser == null) {
                return new Result<>(CoReturnFormat.USER_NOT_LOGIN);
            }

            // 简化处理：直接查询核销记录，权限验证在Service层处理
            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByOrderId(orderId);
            return new Result<>(logs, CoReturnFormat.SUCCESS);
        } catch (Exception e) {
            log.error("查询订单核销记录失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据订单号查询核销记录
     */
    @GetMapping("/logs/order-sn/{orderSn}")
    @ApiOperation("根据订单号查询核销记录")
    public Result<List<CereVerifyLog>> getVerifyLogsByOrderSn(@PathVariable String orderSn,
                                                             HttpServletRequest request) {
        try {
            // 获取当前登录用户
            CereBuyerUser buyerUser = (CereBuyerUser) request.getAttribute("user");
            if (buyerUser == null) {
                return new Result<>(CoReturnFormat.USER_NOT_LOGIN);
            }

            // 简化处理：直接查询核销记录，由于核销记录中包含订单信息，可以在Service层验证权限
            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByOrderSn(orderSn);
            return new Result<>(logs, CoReturnFormat.SUCCESS);
        } catch (Exception e) {
            log.error("查询订单核销记录失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据核销码查询核销记录
     */
    @GetMapping("/logs/verify-code/{verifyCode}")
    @ApiOperation("根据核销码查询核销记录")
    public Result<List<CereVerifyLog>> getVerifyLogsByVerifyCode(@PathVariable String verifyCode,
                                                                HttpServletRequest request) {
        try {
            // 获取当前登录用户
            CereBuyerUser buyerUser = (CereBuyerUser) request.getAttribute("user");
            if (buyerUser == null) {
                return new Result<>(CoReturnFormat.USER_NOT_LOGIN);
            }

            // 简化处理：直接查询核销记录
            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByVerifyCode(verifyCode);
            return new Result<>(logs, CoReturnFormat.SUCCESS);
        } catch (Exception e) {
            log.error("查询核销记录失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取用户的核销码信息
     */
    @GetMapping("/code/{orderId}")
    @ApiOperation("获取订单核销码信息")
    public Result<String> getVerifyCode(@PathVariable Long orderId,
                                       HttpServletRequest request) {
        try {
            // 获取当前登录用户
            CereBuyerUser buyerUser = (CereBuyerUser) request.getAttribute("user");
            if (buyerUser == null) {
                return new Result<>(CoReturnFormat.USER_NOT_LOGIN);
            }

            // 简化处理：通过Service获取核销码，权限验证在Service层处理
            // 这里可以扩展Service方法来获取用户的核销码
            return new Result<>("暂未实现");
        } catch (Exception e) {
            log.error("获取核销码失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取订单核销状态
     */
    @GetMapping("/status/{orderId}")
    @ApiOperation("获取订单核销状态")
    public Result<Integer> getVerifyStatus(@PathVariable Long orderId,
                                          HttpServletRequest request) {
        try {
            // 获取当前登录用户
            CereBuyerUser buyerUser = (CereBuyerUser) request.getAttribute("user");
            if (buyerUser == null) {
                return new Result<>(CoReturnFormat.USER_NOT_LOGIN);
            }

            // 简化处理：返回默认状态，后续可以扩展Service方法
            return new Result<>(0, CoReturnFormat.SUCCESS);
        } catch (Exception e) {
            log.error("获取核销状态失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }
}
