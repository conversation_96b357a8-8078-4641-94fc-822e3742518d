/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.app.service.store;

import com.shop.cereshop.commons.domain.store.CereVerifyLog;

import java.util.List;

/**
 * 核销服务接口（App模块）
 * <AUTHOR>
public interface CereVerifyService {

    /**
     * 生成核销码
     */
    String generateVerifyCode(Long orderId);

    /**
     * 根据订单ID查询核销记录
     */
    List<CereVerifyLog> getVerifyLogsByOrderId(Long orderId);

    /**
     * 根据订单号查询核销记录
     */
    List<CereVerifyLog> getVerifyLogsByOrderSn(String orderSn);

    /**
     * 根据核销码查询核销记录
     */
    List<CereVerifyLog> getVerifyLogsByVerifyCode(String verifyCode);
}
