/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.app.service.store;

import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import com.shop.cereshop.commons.exception.CoBusinessException;

import java.util.List;

/**
 * 商户门店服务接口（App模块）
 * <AUTHOR>
public interface CereMerchantStoreService {

    /**
     * 根据ID查询门店
     */
    CereMerchantStore getStoreById(Long storeId) throws CoBusinessException;

    /**
     * 根据商户ID查询启用的门店列表
     */
    List<CereMerchantStore> getEnabledStoresByMerchantId(Long merchantId);

    /**
     * 根据商户ID查询支持自提的门店列表
     */
    List<CereMerchantStore> getPickupEnabledStoresByMerchantId(Long merchantId);

    /**
     * 根据坐标查询附近门店
     */
    List<CereMerchantStore> getNearbyStores(Long merchantId, Double longitude, Double latitude, Double distance);

    /**
     * 检查门店是否支持自提
     */
    boolean isPickupEnabled(Long storeId) throws CoBusinessException;

    /**
     * 检查门店是否支持核销
     */
    boolean isVerifyEnabled(Long storeId) throws CoBusinessException;

    /**
     * 验证门店是否属于指定商户
     */
    boolean validateStoreOwnership(Long storeId, Long merchantId) throws CoBusinessException;
}
