/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.app.service.store.impl;

import com.shop.cereshop.app.dao.store.CereMerchantStoreDAO;
import com.shop.cereshop.app.service.store.CereMerchantStoreService;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import com.shop.cereshop.commons.exception.CoBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商户门店服务实现类（App模块）
 * <AUTHOR>
@Service
@Slf4j
public class CereMerchantStoreServiceImpl implements CereMerchantStoreService {

    @Autowired
    private CereMerchantStoreDAO cereMerchantStoreDAO;

    @Override
    public CereMerchantStore getStoreById(Long storeId) throws CoBusinessException {
        if (storeId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereMerchantStore store = cereMerchantStoreDAO.selectByPrimaryKey(storeId);
        if (store == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店不存在");
        }

        return store;
    }

    @Override
    public List<CereMerchantStore> getEnabledStoresByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return null;
        }
        return cereMerchantStoreDAO.findEnabledByMerchantId(merchantId);
    }

    @Override
    public List<CereMerchantStore> getPickupEnabledStoresByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return null;
        }
        return cereMerchantStoreDAO.findPickupEnabledByMerchantId(merchantId);
    }

    @Override
    public List<CereMerchantStore> getNearbyStores(Long merchantId, Double longitude, Double latitude, Double distance) {
        if (merchantId == null || longitude == null || latitude == null) {
            return null;
        }
        if (distance == null) {
            distance = 50.0; // 默认50公里
        }
        return cereMerchantStoreDAO.findNearbyStores(merchantId, longitude, latitude, distance);
    }

    @Override
    public boolean isPickupEnabled(Long storeId) throws CoBusinessException {
        if (storeId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }
        
        int count = cereMerchantStoreDAO.checkPickupEnabled(storeId);
        return count > 0;
    }

    @Override
    public boolean isVerifyEnabled(Long storeId) throws CoBusinessException {
        if (storeId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }
        
        int count = cereMerchantStoreDAO.checkVerifyEnabled(storeId);
        return count > 0;
    }

    @Override
    public boolean validateStoreOwnership(Long storeId, Long merchantId) throws CoBusinessException {
        if (storeId == null || merchantId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereMerchantStore store = cereMerchantStoreDAO.selectByPrimaryKey(storeId);
        if (store == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店不存在");
        }

        return merchantId.equals(store.getMerchantId());
    }
}
