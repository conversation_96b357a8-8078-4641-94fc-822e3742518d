/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.app.service.price;

import com.shop.cereshop.commons.domain.tool.CerePriceRule;

import java.util.List;
import java.util.Map;

public interface CerePriceRuleService {

    List<CerePriceRule> findRules(Long priceId);

    List<CerePriceRule> findRulesByShopId(Long shopId);
}
