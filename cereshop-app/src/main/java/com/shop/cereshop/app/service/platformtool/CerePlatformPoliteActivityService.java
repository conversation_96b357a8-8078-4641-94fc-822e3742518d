/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.app.service.platformtool;

import com.shop.cereshop.commons.domain.platformtool.CerePlatformPoliteActivity;

import java.util.List;

public interface CerePlatformPoliteActivityService {

    List<CerePlatformPoliteActivity> selectByPoliteId(Long politeId);

}
