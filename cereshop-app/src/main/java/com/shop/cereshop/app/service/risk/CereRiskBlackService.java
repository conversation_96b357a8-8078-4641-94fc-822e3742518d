/*
* Copyright (C) 2017-2021
* All rights reserved, Designed By 深圳中科鑫智科技有限公司
* Copyright authorization contact 18814114118
*/
package com.shop.cereshop.app.service.risk;

import com.shop.cereshop.commons.domain.risk.CereRiskBlack;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 黑名单表
 * </p>
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
public interface CereRiskBlackService {

    List<CereRiskBlack> getEnabled();
}
