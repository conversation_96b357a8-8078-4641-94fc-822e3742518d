/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.controller.store;

import com.github.pagehelper.PageInfo;
import com.shop.cereshop.business.param.store.BatchUpdateStatusParam;
import com.shop.cereshop.business.param.store.StoreListParam;
import com.shop.cereshop.business.service.store.CereMerchantStoreService;
import com.shop.cereshop.business.utils.ContextUtil;
import com.shop.cereshop.business.vo.store.StoreStatistics;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.domain.common.Page;
import com.shop.cereshop.commons.domain.shop.CerePlatformShop;
import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.result.Result;
import com.shop.cereshop.commons.utils.EmptyUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 商户门店管理Controller
 * <AUTHOR>
@RestController
@RequestMapping("/business/store")
@Api(tags = "商户门店管理")
@Slf4j
public class CereMerchantStoreController {

    @Autowired
    private CereMerchantStoreService cereMerchantStoreService;

    /**
     * 获取门店列表（支持分页和筛选）
     */
    @GetMapping("/list")
    @ApiOperation("获取门店列表")
    public Result<Page<CereMerchantStore>> getStoreList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer isPickupEnabled,
            @RequestParam(required = false) Integer isVerifyEnabled,
            @RequestParam(required = false) String keyword,
            HttpServletRequest request) {
        try {

            StoreListParam param = new StoreListParam();
            param.setMerchantId(ContextUtil.getShopId());
            param.setPage(page);
            param.setSize(size);
            param.setStatus(status);
            param.setIsPickupEnabled(isPickupEnabled);
            param.setIsVerifyEnabled(isVerifyEnabled);
            param.setKeyword(keyword);

            Page<CereMerchantStore> pageInfo = cereMerchantStoreService.getStoreListWithPage(param);
            return new Result<>(pageInfo);
        } catch (Exception e) {
            log.error("获取门店列表失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取启用的门店列表
     */
    @GetMapping("/enabled")
    @ApiOperation("获取启用的门店列表")
    public Result<List<CereMerchantStore>> getEnabledStoreList() {
        try {
            List<CereMerchantStore> stores = cereMerchantStoreService.getEnabledStoresByMerchantId(ContextUtil.getShopId());
            return new Result<>(stores);
        } catch (Exception e) {
            log.error("获取启用门店列表失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取支持自提的门店列表
     */
    @GetMapping("/pickup-enabled")
    @ApiOperation("获取支持自提的门店列表")
    public Result<List<CereMerchantStore>> getPickupEnabledStoreList() {
        try {
            List<CereMerchantStore> stores = cereMerchantStoreService.getPickupEnabledStoresByMerchantId(ContextUtil.getShopId());
            return new Result(stores);
        } catch (Exception e) {
            log.error("获取支持自提门店列表失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取支持核销的门店列表
     */
    @GetMapping("/verify-enabled")
    @ApiOperation("获取支持核销的门店列表")
    public Result<List<CereMerchantStore>> getVerifyEnabledStoreList() {
        try {
            List<CereMerchantStore> stores = cereMerchantStoreService.getVerifyEnabledStoresByMerchantId(ContextUtil.getShopId());
            return new Result(stores);
        } catch (Exception e) {
            log.error("获取支持核销门店列表失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }



    /**
     * 创建门店
     */
    @PostMapping("/create")
    @ApiOperation("创建门店")
    public Result<CereMerchantStore> createStore(@RequestBody CereMerchantStore store) throws CoBusinessException {
        try {
            // 设置商户ID
            store.setMerchantId(ContextUtil.getShopId());

            CereMerchantStore createdStore = cereMerchantStoreService.createStore(store);
            return new Result<>(createdStore, CoReturnFormat.SUCCESS);
        } catch (CoBusinessException e) {
            return new Result<>( e.getMessage());
        } catch (Exception e) {
            log.error("创建门店失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 更新门店
     */
    @PutMapping("/update")
    @ApiOperation("更新门店")
    public Result<CereMerchantStore> updateStore(@RequestBody CereMerchantStore store) throws CoBusinessException {
        try {
            if (store.getStoreId() == null) {
                return new Result<>(CoReturnFormat.PARAM_INVALID);
            }

            // 验证门店是否属于当前商户
            if (!cereMerchantStoreService.validateStoreOwnership(store.getStoreId(), ContextUtil.getShopId())) {
                return new Result<>(CoReturnFormat.PARAM_INVALID);
            }

            store.setStoreId(store.getStoreId());
            store.setMerchantId(ContextUtil.getShopId());
            CereMerchantStore updatedStore = cereMerchantStoreService.updateStore(store);
            return new Result<>(updatedStore, CoReturnFormat.SUCCESS);
        } catch (CoBusinessException e) {
            return new Result<>( e.getMessage());
        } catch (Exception e) {
            log.error("更新门店失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 删除门店
     */
    @DeleteMapping("/delete/{storeId}")
    @ApiOperation("删除门店")
    public Result<Void> deleteStore(@PathVariable Long storeId, HttpServletRequest request) {
        try {


            // 验证门店是否属于当前商户
            if (!cereMerchantStoreService.validateStoreOwnership(storeId, ContextUtil.getShopId())) {
                return new Result<>(CoReturnFormat.PARAM_INVALID);
            }

            cereMerchantStoreService.deleteStore(storeId);
            return new Result<>();
        } catch (CoBusinessException e) {
            return new Result<>( e.getMessage());
        } catch (Exception e) {
            log.error("删除门店失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 更新门店状态
     */
    @PutMapping("/{storeId}/status")
    @ApiOperation("更新门店状态")
    public Result<Void> updateStoreStatus(@PathVariable Long storeId,
                                         @RequestParam Integer status,
                                         HttpServletRequest request) {
        try {


            // 验证门店是否属于当前商户
            if (!cereMerchantStoreService.validateStoreOwnership(storeId, ContextUtil.getShopId())) {
                return new Result<>(CoReturnFormat.PARAM_INVALID);
            }

            cereMerchantStoreService.updateStoreStatus(storeId, status);
            return new Result<>();
        } catch (CoBusinessException e) {
            return new Result<>( e.getMessage());
        } catch (Exception e) {
            log.error("更新门店状态失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取附近门店
     */
    @GetMapping("/nearby")
    @ApiOperation("获取附近门店")
    public Result<List<CereMerchantStore>> getNearbyStores(@RequestParam Double longitude,
                                                          @RequestParam Double latitude,
                                                          @RequestParam(defaultValue = "50") Double distance,
                                                          HttpServletRequest request) {
        try {


            List<CereMerchantStore> stores = cereMerchantStoreService.getNearbyStores(ContextUtil.getShopId(), longitude, latitude, distance);
            return new Result<>(stores);
        } catch (Exception e) {
            log.error("获取附近门店失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 统计门店数量
     */
    @GetMapping("/count")
    @ApiOperation("统计门店数量")
    public Result<Integer> countStores(HttpServletRequest request) {
        try {


            int count = cereMerchantStoreService.countStoresByMerchantId(ContextUtil.getShopId());
            return new Result<>(count);
        } catch (Exception e) {
            log.error("统计门店数量失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取门店详情
     */
    @GetMapping("/{storeId}")
    @ApiOperation("获取门店详情")
    public Result<CereMerchantStore> getStoreDetail(@PathVariable Long storeId, HttpServletRequest request) {
        try {


            // 验证门店是否属于当前商户
            if (!cereMerchantStoreService.validateStoreOwnership(storeId, ContextUtil.getShopId())) {
                return new Result<>(CoReturnFormat.PARAM_INVALID);
            }

            CereMerchantStore store = cereMerchantStoreService.getStoreById(storeId);
            return new Result<>(store);
        } catch (CoBusinessException e) {
            return new Result<>( e.getMessage());
        } catch (Exception e) {
            log.error("获取门店详情失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }



    /**
     * 批量更新门店状态
     */
    @PutMapping("/batch-status")
    @ApiOperation("批量更新门店状态")
    public Result<Void> batchUpdateStoreStatus(@Valid @RequestBody BatchUpdateStatusParam param, HttpServletRequest request) {
        try {

            if (param.getStoreIds() == null || param.getStoreIds().isEmpty()) {
                return new Result<>(CoReturnFormat.PARAM_INVALID);
            }

            // 验证所有门店是否属于当前商户
            for (Long storeId : param.getStoreIds()) {
                if (!cereMerchantStoreService.validateStoreOwnership(storeId, ContextUtil.getShopId())) {
                    return new Result<>(CoReturnFormat.PARAM_INVALID);
                }
            }

            cereMerchantStoreService.batchUpdateStoreStatus(param.getStoreIds(), param.getStatus());
            return new Result<>();
        } catch (CoBusinessException e) {
            return new Result<>( e.getMessage());
        } catch (Exception e) {
            log.error("批量更新门店状态失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 检查门店编码是否唯一
     */
    @GetMapping("/check-code")
    @ApiOperation("检查门店编码是否唯一")
    public Result<Boolean> checkStoreCodeUnique(@RequestParam String storeCode, @RequestParam(required = false) Long excludeStoreId, HttpServletRequest request) {
        try {
            boolean isUnique = cereMerchantStoreService.isStoreCodeUnique(storeCode, excludeStoreId);
            return new Result<>(isUnique);
        } catch (Exception e) {
            log.error("检查门店编码唯一性失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取门店统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取门店统计信息")
    public Result<StoreStatistics> getStoreStatistics() throws CoBusinessException {
        try {
            // 获取当前商户ID
            Long shopId = ContextUtil.getShopId();

            StoreStatistics statistics = cereMerchantStoreService.getStoreStatistics(shopId);
            return new Result<>(statistics, CoReturnFormat.SUCCESS);
        } catch (CoBusinessException e) {
            return new Result<>(e.getMessage());
        } catch (Exception e) {
            log.error("获取门店统计信息失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 获取默认门店
     */
    @GetMapping("/default")
    @ApiOperation("获取默认门店")
    public Result<CereMerchantStore> getDefaultStore() throws CoBusinessException {
        try {
            // 获取当前商户ID
            Long shopId = ContextUtil.getShopId();

            CereMerchantStore defaultStore = cereMerchantStoreService.getDefaultStore(shopId);
            return new Result<>(defaultStore, CoReturnFormat.SUCCESS);
        } catch (CoBusinessException e) {
            return new Result<>(e.getMessage());
        } catch (Exception e) {
            log.error("获取默认门店失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 设置默认门店
     */
    @PutMapping("/{storeId}/set-default")
    @ApiOperation("设置默认门店")
    public Result<Void> setDefaultStore(@PathVariable Long storeId) throws CoBusinessException {
        try {
            // 获取当前商户ID
            Long shopId = ContextUtil.getShopId();

            cereMerchantStoreService.setDefaultStore(storeId, shopId);
            return new Result<>(CoReturnFormat.SUCCESS);
        } catch (CoBusinessException e) {
            return new Result<>(e.getMessage());
        } catch (Exception e) {
            log.error("设置默认门店失败", e);
            return new Result<>(CoReturnFormat.SYS_ERROR);
        }
    }
}
