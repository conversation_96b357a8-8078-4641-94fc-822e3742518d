/*
* Copyright (C) 2017-2021
* All rights reserved, Designed By 深圳中科鑫智科技有限公司
* Copyright authorization contact ***********
*/
package com.shop.cereshop.business.service.customer_service;


import com.shop.cereshop.commons.domain.customer_service.CereCustomerService;
import com.shop.cereshop.commons.domain.customer_service.CereCustomerServiceReceptionist;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 客服表
 * </p>
 *
 * <AUTHOR>
 * @date 2021-12-08
 */
public interface CereCustomerServiceService {

    /*
    int save(CereCustomerService param);

    int update(CereCustomerService param);

    int delete(Long shopId, Long id);

    CereCustomerService getById(Long id);

    int saveReceptionist(CereCustomerServiceReceptionist param);

    int deleteReceptionist(Long id);

    List<CereCustomerService> getAll();

    List<CereCustomerServiceReceptionist> getAllReceptionist(Long serviceId);*/
}
