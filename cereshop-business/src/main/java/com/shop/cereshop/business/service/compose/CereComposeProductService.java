/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.service.compose;

import com.shop.cereshop.business.page.compose.ComposeProduct;
import com.shop.cereshop.commons.domain.tool.CereComposeProduct;
import com.shop.cereshop.commons.exception.CoBusinessException;

import java.util.List;

public interface CereComposeProductService {
    void insertBatch(List<CereComposeProduct> composeProducts) throws CoBusinessException;

    void deleteByComposeId(Long composeId) throws CoBusinessException;

    List<ComposeProduct> findProducts(Long composeId);
}
