/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.dao.log;

import com.shop.cereshop.commons.domain.log.CerePlatformLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CerePlatformLogDAO extends BaseMapper<CerePlatformLog> {
    int deleteByPrimaryKey(Long logId);

    int insertSelective(CerePlatformLog record);

    CerePlatformLog selectByPrimaryKey(Long logId);

    int updateByPrimaryKeySelective(CerePlatformLog record);

    int updateByPrimaryKey(CerePlatformLog record);

    void deleteSignActivityByOnly(@Param("activityId") Long activityId,@Param("userId") Long userId);
}
