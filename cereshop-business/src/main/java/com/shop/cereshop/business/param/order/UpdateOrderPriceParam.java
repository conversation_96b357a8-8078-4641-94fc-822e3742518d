/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.param.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单改价请求
 */
@Data
@ApiModel(value = "UpdateOrderPriceParam", description = "订单改价请求")
public class UpdateOrderPriceParam {

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private Long orderId;

    /**
     * 最新支付金额
     */
    @ApiModelProperty(value = "最新支付金额")
    private BigDecimal price;

}
