///*
// * Copyright (C) 2017-2021
// * All rights reserved, Designed By 深圳中科鑫智科技有限公司
// * Copyright authorization contact ***********
// */
//package com.shop.cereshop.business.service.store.impl;
//
//import com.shop.cereshop.business.dao.order.CereOrderProductDAO;
//import com.shop.cereshop.business.dao.order.CereShopOrderDAO;
//import com.shop.cereshop.business.dao.store.CereStoreStaffDAO;
//import com.shop.cereshop.business.dao.store.CereVerifyLogDAO;
//import com.shop.cereshop.business.service.store.CereVerifyService;
//import com.shop.cereshop.commons.constant.CoReturnFormat;
//import com.shop.cereshop.commons.constant.DeliveryTypeEnum;
//import com.shop.cereshop.commons.constant.VerifyStatusEnum;
//import com.shop.cereshop.commons.constant.IntegerEnum;
//import com.shop.cereshop.commons.domain.order.CereOrderProduct;
//import com.shop.cereshop.commons.domain.order.CereShopOrder;
//import com.shop.cereshop.commons.domain.store.CereStoreStaff;
//import com.shop.cereshop.commons.domain.store.CereVerifyLog;
//import com.shop.cereshop.commons.exception.CoBusinessException;
//import com.shop.cereshop.commons.utils.EmptyUtils;
//import com.shop.cereshop.commons.utils.RandomStringUtil;
//import com.shop.cereshop.commons.utils.TimeUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Isolation;
//import org.springframework.transaction.annotation.Propagation;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.List;
//
///**
// * 核销服务实现类
// * <AUTHOR> */
//@Service
//@Slf4j
//public class CereVerifyServiceImpl implements CereVerifyService {
//
//    @Autowired
//    private CereShopOrderDAO cereShopOrderDAO;
//
//    @Autowired
//    private CereOrderProductDAO cereOrderProductDAO;
//
//    @Autowired
//    private CereStoreStaffDAO cereStoreStaffDAO;
//
//    @Autowired
//    private CereVerifyLogDAO cereVerifyLogDAO;
//
//    @Override
//    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
//    public void verifyWholeOrder(String verifyCode, Long staffId, String remark) throws CoBusinessException {
//        // 1. 验证核销码和权限
//        CereShopOrder order = validateVerifyCode(verifyCode, staffId);
//
//        // 2. 检查订单状态
//        if (VerifyStatusEnum.COMPLETED.getCode().equals(order.getVerifyStatus())) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "订单已核销");
//        }
//
//        // 3. 获取员工信息
//        CereStoreStaff staff = cereStoreStaffDAO.selectByPrimaryKey(staffId);
//        if (staff == null) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工不存在");
//        }
//
//        // 4. 更新订单状态
//        CereShopOrder updateOrder = new CereShopOrder();
//        updateOrder.setOrderId(order.getOrderId());
//        updateOrder.setVerifyStatus(VerifyStatusEnum.COMPLETED.getCode());
//
//        Date now = new Date();
//        if (order.getFirstVerifyTime() == null) {
//            updateOrder.setFirstVerifyTime(now);
//        }
//        updateOrder.setLastVerifyTime(now);
//        updateOrder.setUpdateTime(TimeUtils.yyMMddHHmmss());
//
//        // 核销完成后，将订单状态更新为已完成
//        updateOrder.setState(IntegerEnum.ORDER_FINISH.getCode());
//
//        cereShopOrderDAO.updateByPrimaryKeySelective(updateOrder);
//
//        // 5. 获取订单商品并记录核销日志
//        List<CereOrderProduct> products = cereOrderProductDAO.findByOrderId(order.getOrderId());
//        for (CereOrderProduct product : products) {
//            // 记录核销日志
//            CereVerifyLog verifyLog = new CereVerifyLog();
//            verifyLog.setOrderId(order.getOrderId());
//            verifyLog.setOrderSn(order.getOrderSn());
//            verifyLog.setMerchantId(order.getShopId());
//            verifyLog.setOrderProductId(product.getOrderProductId());
//            verifyLog.setProductId(product.getProductId());
//            verifyLog.setProductName(product.getProductName());
//            verifyLog.setSkuName(product.getSkuName());
//            verifyLog.setVerifyType(1); // 整单核销
//            verifyLog.setVerifyTimes(product.getProductNum());
//            verifyLog.setVerifyCode(verifyCode);
//            verifyLog.setStaffId(staffId);
//            verifyLog.setStaffName(staff.getStaffName());
//            verifyLog.setStoreId(staff.getStoreId());
//            verifyLog.setStoreName(order.getPickupStoreName());
//            verifyLog.setVerifyTime(now);
//            verifyLog.setRemark(remark);
//            verifyLog.setCreateTime(TimeUtils.yyMMddHHmmss());
//
//            cereVerifyLogDAO.insert(verifyLog);
//        }
//
//        // 触发后续业务事件
//        triggerAfterVerifyEvents(order);
//
//        log.info("整单核销成功，订单号：{}，核销码：{}，员工：{}", order.getOrderSn(), verifyCode, staff.getStaffName());
//    }
//
//    /**
//     * 触发核销后的业务事件
//     */
//    private void triggerAfterVerifyEvents(CereShopOrder order) {
//        try {
//            // TODO: 这里可以添加核销后的业务逻辑
//            // 1. 更新用户积分
//            // 2. 计算分销佣金
//            // 3. 发送核销成功通知
//            // 4. 更新商品销量统计
//            log.info("触发核销后业务事件，订单号：{}", order.getOrderSn());
//        } catch (Exception e) {
//            log.error("触发核销后业务事件失败，订单号：{}", order.getOrderSn(), e);
//            // 不抛出异常，避免影响核销流程
//        }
//    }
//
//    @Override
//    public CereShopOrder validateVerifyCode(String verifyCode, Long staffId) throws CoBusinessException {
//        // 1. 参数验证
//        if (EmptyUtils.isEmpty(verifyCode) || staffId == null) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
//        }
//
//        // 2. 查询订单
//        CereShopOrder order = cereShopOrderDAO.findByVerifyCode(verifyCode);
//        if (order == null) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "核销码无效");
//        }
//
//        // 3. 检查订单是否为自提订单
//        if (!DeliveryTypeEnum.isPickup(order.getDeliveryType())) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "该订单不是自提订单");
//        }
//
//        // 4. 检查订单状态
//        if (order.getOrderStatus() != 2) { // 假设2表示已支付待核销
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "订单状态不正确");
//        }
//
//        // 5. 获取员工信息
//        CereStoreStaff staff = cereStoreStaffDAO.selectByPrimaryKey(staffId);
//        if (staff == null) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工不存在");
//        }
//
//        // 6. 验证员工权限
//        if (staff.getCanVerify() != 1 || staff.getStatus() != 1) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工无核销权限");
//        }
//
//        // 7. 验证门店权限（员工只能核销自己门店的订单）
//        if (!staff.getStoreId().equals(order.getPickupStoreId())) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工无权限核销该订单");
//        }
//
//        // 8. 验证商户权限
//        if (!staff.getMerchantId().equals(order.getShopId())) {
//            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "商户权限不匹配");
//        }
//
//        return order;
//    }
//
//    @Override
//    public String generateVerifyCode(Long orderId) {
//        if (orderId == null) {
//            return null;
//        }
//
//        // 生成格式：CS + 8位随机数字
//        String prefix = "CS";
//        String randomCode = RandomStringUtil.getRandomCode(8, 1); // 纯数字
//        return prefix + randomCode;
//    }
//
//    @Override
//    public List<CereVerifyLog> getVerifyLogsByOrderId(Long orderId) {
//        if (orderId == null) {
//            return null;
//        }
//        return cereVerifyLogDAO.findByOrderId(orderId);
//    }
//
//    @Override
//    public List<CereVerifyLog> getVerifyLogsByOrderSn(String orderSn) {
//        if (EmptyUtils.isEmpty(orderSn)) {
//            return null;
//        }
//        return cereVerifyLogDAO.findByOrderSn(orderSn);
//    }
//
//    @Override
//    public List<CereVerifyLog> getVerifyLogsByVerifyCode(String verifyCode) {
//        if (EmptyUtils.isEmpty(verifyCode)) {
//            return null;
//        }
//        return cereVerifyLogDAO.findByVerifyCode(verifyCode);
//    }
//
//    @Override
//    public List<CereVerifyLog> getVerifyLogsByMerchantId(Long merchantId, Date startTime, Date endTime) {
//        if (merchantId == null) {
//            return null;
//        }
//        return cereVerifyLogDAO.findByMerchantId(merchantId, startTime, endTime);
//    }
//
//    @Override
//    public List<CereVerifyLog> getVerifyLogsByStoreId(Long storeId, Date startTime, Date endTime) {
//        if (storeId == null) {
//            return null;
//        }
//        return cereVerifyLogDAO.findByStoreId(storeId, startTime, endTime);
//    }
//
//    @Override
//    public int countVerifyByMerchantId(Long merchantId, Date startTime, Date endTime) {
//        if (merchantId == null) {
//            return 0;
//        }
//        return cereVerifyLogDAO.countByMerchantId(merchantId, startTime, endTime);
//    }
//
//    @Override
//    public int countVerifyByStoreId(Long storeId, Date startTime, Date endTime) {
//        if (storeId == null) {
//            return 0;
//        }
//        return cereVerifyLogDAO.countByStoreId(storeId, startTime, endTime);
//    }
//
//    @Override
//    public boolean canVerifyOrder(Long orderId, Long staffId) throws CoBusinessException {
//        if (orderId == null || staffId == null) {
//            return false;
//        }
//
//        try {
//            CereShopOrder order = cereShopOrderDAO.selectByPrimaryKey(orderId);
//            if (order == null || EmptyUtils.isEmpty(order.getVerifyCode())) {
//                return false;
//            }
//
//            validateVerifyCode(order.getVerifyCode(), staffId);
//            return true;
//        } catch (CoBusinessException e) {
//            return false;
//        }
//    }
//
//    @Override
//    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
//    public void recordVerifyLog(CereVerifyLog verifyLog) {
//        if (verifyLog != null) {
//            if (EmptyUtils.isEmpty(verifyLog.getCreateTime())) {
//                verifyLog.setCreateTime(TimeUtils.yyMMddHHmmss());
//            }
//            cereVerifyLogDAO.insert(verifyLog);
//        }
//    }
//}
