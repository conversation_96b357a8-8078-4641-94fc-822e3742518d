/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.param.label;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 添加标签请求
 */
@Data
@ApiModel(value = "LabelSaveParam", description = "添加标签请求")
public class LabelSaveParam {

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    @NotBlank(message = "标签名称不能为空")
    private String labelName;


    /**
     * 素材类型 1-图片 2-视频
     */
    @ApiModelProperty(value = "素材类型 1-图片 2-视频")
    private Integer labelType;
}
