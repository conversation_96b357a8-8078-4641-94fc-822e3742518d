///*
// * Copyright (C) 2017-2021
// * All rights reserved, Designed By 深圳中科鑫智科技有限公司
// * Copyright authorization contact ***********
// */
//package com.shop.cereshop.business.controller.store;
//
//import com.shop.cereshop.business.annotation.CurrentShop;
//import com.shop.cereshop.business.service.store.CereStoreStaffService;
//import com.shop.cereshop.business.service.store.CereVerifyService;
//import com.shop.cereshop.commons.constant.CoReturnFormat;
//import com.shop.cereshop.commons.domain.order.CereShopOrder;
//import com.shop.cereshop.commons.domain.shop.CerePlatformShop;
//import com.shop.cereshop.commons.domain.store.CereStoreStaff;
//import com.shop.cereshop.commons.domain.store.CereVerifyLog;
//import com.shop.cereshop.commons.exception.CoBusinessException;
//import com.shop.cereshop.commons.result.Result;
//import com.shop.cereshop.commons.utils.EmptyUtils;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.format.annotation.DateTimeFormat;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * 商户端核销管理Controller
// * <AUTHOR> */
//@RestController
//@RequestMapping("/business/verify")
//@Api(tags = "核销管理")
//@Slf4j
//public class CereVerifyController {
//
//    @Autowired
//    private CereVerifyService cereVerifyService;
//
//    @Autowired
//    private CereStoreStaffService cereStoreStaffService;
//
//    /**
//     * 验证核销码
//     */
//    @GetMapping("/validate")
//    @ApiOperation("验证核销码")
//    public Result<CereShopOrder> validateVerifyCode(@RequestParam String verifyCode,
//                                                   @RequestParam Long staffId,
//                                                   @CurrentShop CerePlatformShop shop) {
//        try {
//            // 验证员工是否属于当前商户
//            if (!cereStoreStaffService.validateStaffOwnership(staffId, shop.getShopId())) {
//                return  new Result("无权限操作");
//            }
//
//            CereShopOrder order = cereVerifyService.validateVerifyCode(verifyCode, staffId);
//            return new Result(order);
//        } catch (CoBusinessException e) {
//            return new Result( e.getMessage());
//        } catch (Exception e) {
//            log.error("验证核销码失败", e);
//            return new Result(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 整单核销
//     */
//    @PostMapping("/whole-order")
//    @ApiOperation("整单核销")
//    public Result<Void> verifyWholeOrder(@RequestParam String verifyCode,
//                                        @RequestParam Long staffId,
//                                        @RequestParam(required = false) String remark,
//                                        @CurrentShop CerePlatformShop shop) {
//        try {
//            // 验证员工是否属于当前商户
//            if (!cereStoreStaffService.validateStaffOwnership(staffId, shop.getShopId())) {
//                return  new Result("无权限操作");
//            }
//
//            cereVerifyService.verifyWholeOrder(verifyCode, staffId, remark);
//            return  new Result("success");
//        } catch (CoBusinessException e) {
//            return new Result( e.getMessage());
//        } catch (Exception e) {
//            log.error("整单核销失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 根据订单ID查询核销记录
//     */
//    @GetMapping("/logs/order/{orderId}")
//    @ApiOperation("根据订单ID查询核销记录")
//    public Result<List<CereVerifyLog>> getVerifyLogsByOrderId(@PathVariable Long orderId,
//                                                             @CurrentShop CerePlatformShop shop) {
//        try {
//            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByOrderId(orderId);
//
//            // 验证核销记录是否属于当前商户
//            if (!EmptyUtils.isEmpty(logs)) {
//                CereVerifyLog firstLog = logs.get(0);
//                if (!shop.getShopId().equals(firstLog.getMerchantId())) {
//                    return Result.failure(CoReturnFormat.PARAM_ERROR, "无权限访问");
//                }
//            }
//
//            return Result.success(logs);
//        } catch (Exception e) {
//            log.error("查询核销记录失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 根据订单号查询核销记录
//     */
//    @GetMapping("/logs/order-sn/{orderSn}")
//    @ApiOperation("根据订单号查询核销记录")
//    public Result<List<CereVerifyLog>> getVerifyLogsByOrderSn(@PathVariable String orderSn,
//                                                             @CurrentShop CerePlatformShop shop) {
//        try {
//            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByOrderSn(orderSn);
//
//            // 验证核销记录是否属于当前商户
//            if (!EmptyUtils.isEmpty(logs)) {
//                CereVerifyLog firstLog = logs.get(0);
//                if (!shop.getShopId().equals(firstLog.getMerchantId())) {
//                    return Result.failure(CoReturnFormat.PARAM_ERROR, "无权限访问");
//                }
//            }
//
//            return Result.success(logs);
//        } catch (Exception e) {
//            log.error("查询核销记录失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 根据核销码查询核销记录
//     */
//    @GetMapping("/logs/verify-code/{verifyCode}")
//    @ApiOperation("根据核销码查询核销记录")
//    public Result<List<CereVerifyLog>> getVerifyLogsByVerifyCode(@PathVariable String verifyCode,
//                                                                @CurrentShop CerePlatformShop shop) {
//        try {
//            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByVerifyCode(verifyCode);
//
//            // 验证核销记录是否属于当前商户
//            if (!EmptyUtils.isEmpty(logs)) {
//                CereVerifyLog firstLog = logs.get(0);
//                if (!shop.getShopId().equals(firstLog.getMerchantId())) {
//                    return Result.failure(CoReturnFormat.PARAM_ERROR, "无权限访问");
//                }
//            }
//
//            return Result.success(logs);
//        } catch (Exception e) {
//            log.error("查询核销记录失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 查询商户核销记录
//     */
//    @GetMapping("/logs/merchant")
//    @ApiOperation("查询商户核销记录")
//    public Result<List<CereVerifyLog>> getVerifyLogsByMerchantId(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//                                                                @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
//                                                                @CurrentShop CerePlatformShop shop) {
//        try {
//            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByMerchantId(shop.getShopId(), startTime, endTime);
//            return Result.success(logs);
//        } catch (Exception e) {
//            log.error("查询商户核销记录失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 根据门店ID查询核销记录
//     */
//    @GetMapping("/logs/store/{storeId}")
//    @ApiOperation("根据门店ID查询核销记录")
//    public Result<List<CereVerifyLog>> getVerifyLogsByStoreId(@PathVariable Long storeId,
//                                                             @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//                                                             @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
//                                                             @CurrentShop CerePlatformShop shop) {
//        try {
//            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByStoreId(storeId, startTime, endTime);
//
//            // 验证门店核销记录是否属于当前商户
//            if (!EmptyUtils.isEmpty(logs)) {
//                CereVerifyLog firstLog = logs.get(0);
//                if (!shop.getShopId().equals(firstLog.getMerchantId())) {
//                    return Result.failure(CoReturnFormat.PARAM_ERROR, "无权限访问");
//                }
//            }
//
//            return Result.success(logs);
//        } catch (Exception e) {
//            log.error("查询门店核销记录失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 统计商户核销数量
//     */
//    @GetMapping("/count/merchant")
//    @ApiOperation("统计商户核销数量")
//    public Result<Integer> countVerifyByMerchantId(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//                                                  @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
//                                                  @CurrentShop CerePlatformShop shop) {
//        try {
//            int count = cereVerifyService.countVerifyByMerchantId(shop.getShopId(), startTime, endTime);
//            return Result.success(count);
//        } catch (Exception e) {
//            log.error("统计商户核销数量失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 统计门店核销数量
//     */
//    @GetMapping("/count/store/{storeId}")
//    @ApiOperation("统计门店核销数量")
//    public Result<Integer> countVerifyByStoreId(@PathVariable Long storeId,
//                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
//                                               @CurrentShop CerePlatformShop shop) {
//        try {
//            int count = cereVerifyService.countVerifyByStoreId(storeId, startTime, endTime);
//            return Result.success(count);
//        } catch (Exception e) {
//            log.error("统计门店核销数量失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//
//    /**
//     * 检查订单是否可以核销
//     */
//    @GetMapping("/can-verify/{orderId}")
//    @ApiOperation("检查订单是否可以核销")
//    public Result<Boolean> canVerifyOrder(@PathVariable Long orderId,
//                                         @RequestParam Long staffId,
//                                         @CurrentShop CerePlatformShop shop) {
//        try {
//            // 验证员工是否属于当前商户
//            if (!cereStoreStaffService.validateStaffOwnership(staffId, shop.getShopId())) {
//                return Result.failure(CoReturnFormat.PARAM_ERROR, "无权限操作");
//            }
//
//            boolean canVerify = cereVerifyService.canVerifyOrder(orderId, staffId);
//            return Result.success(canVerify);
//        } catch (Exception e) {
//            log.error("检查订单核销权限失败", e);
//            return Result.failure(CoReturnFormat.SYS_ERROR);
//        }
//    }
//}
