/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.message.service.miaoxin.impl;

import com.alibaba.fastjson.JSONObject;
import com.shop.cereshop.business.message.sdk.MiaoxinSmsClient;
import com.shop.cereshop.business.message.service.miaoxin.MiaoxinMessageService;
import com.shop.cereshop.commons.domain.message.MessageResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.Map;

@Service
public class MiaoxinMessageServiceImpl implements MiaoxinMessageService {

    /**
     * 秒信云账号app_id
     */
    @Value("${miaoxinyun.account}")
    private String miaoxin_account;

    /**
     * 秒信云密钥
     */
    @Value("${miaoxinyun.secret}")
    private String miaoxin_secret;

    /**
     * 秒信云域名
     */
    @Value("${miaoxinyun.domain}")
    private String miaoxin_domain;

    /**
     * 秒信云签名
     */
    @Value("${miaoxinyun.sign_name}")
    private String signName;

    /**
     * 秒信云模板
     */
    @Value("${miaoxinyun.template}")
    private String template;

    @Override
    public String sendNotice(String phone, Map<String, String> map) throws Exception {
        MiaoxinSmsClient client = new MiaoxinSmsClient();
        String format = MessageFormat.format(template, map.values().toArray(new String[0]));
        String content = "【" + signName + "】" + format;
        client.setAccount(miaoxin_account);
        client.setPassword(miaoxin_secret);
        String result = client.send(phone, content, "ref", "103");
        //返回结果转对象
        MessageResult messageResult = JSONObject.parseObject(result, MessageResult.class);
        return messageResult.getCode();
    }

    @Override
    public String sendContent(String phone, String content) throws Exception {
        MiaoxinSmsClient client = new MiaoxinSmsClient();
        client.setAccount(miaoxin_account);
        client.setPassword(miaoxin_secret);
        String result = client.send(phone, content, "sendContent", "104");
        //返回结果转对象
        MessageResult messageResult = JSONObject.parseObject(result, MessageResult.class);
        return messageResult.getCode();
    }
}
