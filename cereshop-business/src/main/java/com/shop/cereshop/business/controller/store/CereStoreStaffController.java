/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.controller.store;

import com.shop.cereshop.business.service.store.CereMerchantStoreService;
import com.shop.cereshop.business.service.store.CereStoreStaffService;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.domain.store.CereStoreStaff;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商户端门店员工管理Controller
 * <AUTHOR>
@RestController
@RequestMapping("/business/staff")
@Api(tags = "门店员工管理")
@Slf4j
public class CereStoreStaffController {

    @Autowired
    private CereStoreStaffService cereStoreStaffService;

    @Autowired
    private CereMerchantStoreService cereMerchantStoreService;

    /**
     * 获取商户所有员工列表
     */
    @GetMapping("/list")
    @ApiOperation("获取商户所有员工列表")
    public Result<List<CereStoreStaff>> getStaffList(@PathVariable Long shopId) {
        try {
            List<CereStoreStaff> staffList = cereStoreStaffService.getStaffByMerchantId(shopId);
            return new Result(staffList);
        } catch (Exception e) {
            log.error("获取员工列表失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据门店ID获取员工列表
     */
    @GetMapping("/store/{storeId}")
    @ApiOperation("根据门店ID获取员工列表")
    public Result<List<CereStoreStaff>> getStaffByStoreId(@PathVariable Long storeId, @PathVariable Long shopId) {
        try {
            // 验证门店是否属于当前商户
            if (!cereMerchantStoreService.validateStoreOwnership(storeId, shopId)) {
                return new Result("无权限访问该门店");
            }
            
            List<CereStoreStaff> staffList = cereStoreStaffService.getStaffByStoreId(storeId);
            return new Result(staffList);
        } catch (CoBusinessException e) {
            return new Result(e.getMessage());
        } catch (Exception e) {
            log.error("获取门店员工列表失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据门店ID获取启用的员工列表
     */
    @GetMapping("/store/{storeId}/enabled")
    @ApiOperation("根据门店ID获取启用的员工列表")
    public Result<List<CereStoreStaff>> getEnabledStaffByStoreId(@PathVariable Long storeId, @PathVariable Long shopId) {
        try {
            // 验证门店是否属于当前商户
            if (!cereMerchantStoreService.validateStoreOwnership(storeId, shopId)) {
                return new Result( "无权限访问该门店");
            }
            
            List<CereStoreStaff> staffList = cereStoreStaffService.getEnabledStaffByStoreId(storeId);
            return new Result(staffList);
        } catch (CoBusinessException e) {
            return new Result( e.getMessage());
        } catch (Exception e) {
            log.error("获取启用员工列表失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据门店ID获取有核销权限的员工列表
     */
    @GetMapping("/store/{storeId}/verify-enabled")
    @ApiOperation("根据门店ID获取有核销权限的员工列表")
    public Result<List<CereStoreStaff>> getVerifyEnabledStaffByStoreId(@PathVariable Long storeId, @PathVariable Long shopId) {
        try {
            // 验证门店是否属于当前商户
            if (!cereMerchantStoreService.validateStoreOwnership(storeId, shopId)) {
                return new Result( "无权限访问该门店");
            }
            
            List<CereStoreStaff> staffList = cereStoreStaffService.getVerifyEnabledStaffByStoreId(storeId);
            return new Result(staffList);
        } catch (CoBusinessException e) {
            return new Result( e.getMessage());
        } catch (Exception e) {
            log.error("获取有核销权限员工列表失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据ID获取员工详情
     */
    @GetMapping("/{staffId}")
    @ApiOperation("获取员工详情")
    public Result<CereStoreStaff> getStaffById(@PathVariable Long staffId, @PathVariable Long shopId) {
        try {
            CereStoreStaff staff = cereStoreStaffService.getStaffById(staffId);
            
            // 验证员工是否属于当前商户
            if (!cereStoreStaffService.validateStaffOwnership(staffId, shopId)) {
                return new Result( "无权限访问该员工");
            }
            
            return new Result(staff);
        } catch (CoBusinessException e) {
            return new Result( e.getMessage());
        } catch (Exception e) {
            log.error("获取员工详情失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 创建员工
     */
    @PostMapping("/create")
    @ApiOperation("创建员工")
    public Result<CereStoreStaff> createStaff(@RequestBody CereStoreStaff staff, @PathVariable Long shopId) {
        try {
            // 设置商户ID
            staff.setMerchantId(shopId);

            // 验证门店是否属于当前商户
            if (staff.getStoreId() != null) {
                if (!cereMerchantStoreService.validateStoreOwnership(staff.getStoreId(), shopId)) {
                    return new Result( "无权限操作该门店");
                }
            }
            
            CereStoreStaff createdStaff = cereStoreStaffService.createStaff(staff);
            return new Result(createdStaff);
        } catch (CoBusinessException e) {
            return new Result( e.getMessage());
        } catch (Exception e) {
            log.error("创建员工失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 更新员工
     */
    @PutMapping("/update")
    @ApiOperation("更新员工")
    public Result<CereStoreStaff> updateStaff(@RequestBody CereStoreStaff staff, @PathVariable Long shopId) {
        try {
            if (staff.getStaffId() == null) {
                return new Result( "员工ID不能为空");
            }
            
            // 验证员工是否属于当前商户
            if (!cereStoreStaffService.validateStaffOwnership(staff.getStaffId(), shopId)) {
                return new Result("无权限操作该员工");
            }
            
            CereStoreStaff updatedStaff = cereStoreStaffService.updateStaff(staff);
            return new Result(updatedStaff);
        } catch (CoBusinessException e) {
            return new Result( e.getMessage());
        } catch (Exception e) {
            log.error("更新员工失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 删除员工
     */
    @DeleteMapping("/{staffId}")
    @ApiOperation("删除员工")
    public Result<Void> deleteStaff(@PathVariable Long staffId, @PathVariable Long shopId) {
        try {
            // 验证员工是否属于当前商户
            if (!cereStoreStaffService.validateStaffOwnership(staffId, shopId)) {
                return new Result( "无权限操作该员工");
            }
            
            cereStoreStaffService.deleteStaff(staffId);
            return new Result();
        } catch (CoBusinessException e) {
            return new Result( e.getMessage());
        } catch (Exception e) {
            log.error("删除员工失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 更新员工状态
     */
    @PutMapping("/{staffId}/status")
    @ApiOperation("更新员工状态")
    public Result<Void> updateStaffStatus(@PathVariable Long staffId, 
                                         @RequestParam Integer status, 
                                         @PathVariable Long shopId) {
        try {
            // 验证员工是否属于当前商户
            if (!cereStoreStaffService.validateStaffOwnership(staffId, shopId)) {
                return new Result( "无权限操作该员工");
            }
            
            cereStoreStaffService.updateStaffStatus(staffId, status);
            return new Result();
        } catch (CoBusinessException e) {
            return new Result( e.getMessage());
        } catch (Exception e) {
            log.error("更新员工状态失败", e);
            return new Result(CoReturnFormat.SYS_ERROR);
        }
    }
}
