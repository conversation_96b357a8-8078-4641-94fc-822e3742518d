/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.service.store;

import com.shop.cereshop.commons.domain.store.CereStoreStaff;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.utils.EmptyUtils;

import java.util.List;

/**
 * 门店员工服务接口
 * <AUTHOR>
public interface CereStoreStaffService {

    /**
     * 创建员工
     */
    CereStoreStaff createStaff(CereStoreStaff staff) throws CoBusinessException;

    /**
     * 更新员工信息
     */
    CereStoreStaff updateStaff(CereStoreStaff staff) throws CoBusinessException;

    /**
     * 删除员工
     */
    void deleteStaff(Long staffId) throws CoBusinessException;

    /**
     * 根据ID查询员工
     */
    CereStoreStaff getStaffById(Long staffId) throws CoBusinessException;

    /**
     * 根据门店ID查询员工列表
     */
    List<CereStoreStaff> getStaffByStoreId(Long storeId);

    /**
     * 根据商户ID查询员工列表
     */
    List<CereStoreStaff> getStaffByMerchantId(Long merchantId);

    /**
     * 根据门店ID查询启用的员工列表
     */
    List<CereStoreStaff> getEnabledStaffByStoreId(Long storeId);

    /**
     * 根据手机号查询员工
     */
    CereStoreStaff getStaffByPhone(String staffPhone);

    /**
     * 根据微信OpenID查询员工
     */
    CereStoreStaff getStaffByWechatOpenid(String wechatOpenid);

    /**
     * 根据员工工号查询员工
     */
    CereStoreStaff getStaffByStaffCode(String staffCode);

    /**
     * 根据门店ID查询有核销权限的员工
     */
    List<CereStoreStaff> getVerifyEnabledStaffByStoreId(Long storeId);

    /**
     * 启用/禁用员工
     */
    void updateStaffStatus(Long staffId, Integer status) throws CoBusinessException;

    /**
     * 验证员工是否有核销权限
     */
    boolean hasVerifyPermission(Long staffId, Long storeId) throws CoBusinessException;

    /**
     * 验证员工是否属于指定商户
     */
    boolean validateStaffOwnership(Long staffId, Long merchantId) throws CoBusinessException;


     boolean isStaffCodeUnique(String staffCode, Long excludeStaffId);
    boolean isPhoneUniqueInStore(Long storeId, String staffPhone, Long excludeStaffId);
}
