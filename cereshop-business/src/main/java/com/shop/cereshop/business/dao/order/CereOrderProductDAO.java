/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.dao.order;

import com.shop.cereshop.commons.domain.order.CereOrderProduct;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CereOrderProductDAO extends BaseMapper<CereOrderProduct> {

    int insertSelective(CereOrderProduct record);

    /**
     * 根据订单ID查询订单商品列表
     */
    List<CereOrderProduct> findByOrderId(@Param("orderId") Long orderId);
}
