/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.service.store;

import com.github.pagehelper.PageInfo;
import com.shop.cereshop.business.param.store.StoreListParam;
import com.shop.cereshop.business.vo.store.StoreStatistics;
import com.shop.cereshop.commons.domain.common.Page;
import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import com.shop.cereshop.commons.exception.CoBusinessException;

import java.util.List;

/**
 * 商户门店服务接口
 * <AUTHOR>
public interface CereMerchantStoreService {

    /**
     * 创建门店
     */
    CereMerchantStore createStore(CereMerchantStore store) throws CoBusinessException;

    /**
     * 更新门店信息
     */
    CereMerchantStore updateStore(CereMerchantStore store) throws CoBusinessException;

    /**
     * 删除门店
     */
    void deleteStore(Long storeId) throws CoBusinessException;

    /**
     * 根据ID查询门店
     */
    CereMerchantStore getStoreById(Long storeId) throws CoBusinessException;

    /**
     * 根据商户ID查询门店列表
     */
    List<CereMerchantStore> getStoresByMerchantId(Long merchantId);

    /**
     * 分页查询门店列表（支持筛选）
     */
    Page getStoreListWithPage(StoreListParam param);

    /**
     * 根据商户ID查询启用的门店列表
     */
    List<CereMerchantStore> getEnabledStoresByMerchantId(Long merchantId);

    /**
     * 根据商户ID查询支持自提的门店列表
     */
    List<CereMerchantStore> getPickupEnabledStoresByMerchantId(Long merchantId);

    /**
     * 根据商户ID查询支持核销的门店列表
     */
    List<CereMerchantStore> getVerifyEnabledStoresByMerchantId(Long merchantId);

    /**
     * 根据坐标查询附近门店
     */
    List<CereMerchantStore> getNearbyStores(Long merchantId, Double longitude, Double latitude, Double distance);

    /**
     * 启用/禁用门店
     */
    void updateStoreStatus(Long storeId, Integer status) throws CoBusinessException;

    /**
     * 批量更新门店状态
     */
    void batchUpdateStoreStatus(List<Long> storeIds, Integer status) throws CoBusinessException;

    /**
     * 验证门店编码是否唯一
     */
    boolean isStoreCodeUnique(String storeCode, Long excludeStoreId);

    /**
     * 统计商户门店数量
     */
    int countStoresByMerchantId(Long merchantId);

    /**
     * 获取门店统计信息
     */
    StoreStatistics getStoreStatistics(Long merchantId);

    /**
     * 验证门店是否属于指定商户
     */
    boolean validateStoreOwnership(Long storeId, Long merchantId) throws CoBusinessException;

    /**
     * 检查门店是否支持自提
     */
    boolean isPickupEnabled(Long storeId) throws CoBusinessException;

    /**
     * 检查门店是否支持核销
     */
    boolean isVerifyEnabled(Long storeId) throws CoBusinessException;

    /**
     * 获取默认门店
     */
    CereMerchantStore getDefaultStore(Long merchantId) throws CoBusinessException;

    /**
     * 设置默认门店
     */
    void setDefaultStore(Long storeId, Long merchantId) throws CoBusinessException;
}
