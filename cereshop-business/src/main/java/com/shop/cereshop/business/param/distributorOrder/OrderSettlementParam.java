/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.param.distributorOrder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分销订单标记结算请求
 */
@Data
@ApiModel(value = "OrderSettlementParam", description = "分销订单标记结算请求")
public class OrderSettlementParam {

    /**
     * 订单id数组
     */
    @ApiModelProperty(value = "订单id数组")
    private List<Long> ids;
}
