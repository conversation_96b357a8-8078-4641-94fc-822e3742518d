/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.service.pay;

import com.shop.cereshop.business.page.pay.PayLog;
import com.shop.cereshop.commons.domain.pay.CerePayLog;
import com.shop.cereshop.commons.exception.CoBusinessException;

public interface CerePayLogService {
    void insert(CerePayLog payLog) throws CoBusinessException;

    void update(CerePayLog payLog) throws CoBusinessException;

    PayLog findByOrderFormid(String orderFormid) throws CoBusinessException;

    PayLog findByOutRefundNo(String refundNo);

    PayLog findByTransactionId(String transactionId);
}
