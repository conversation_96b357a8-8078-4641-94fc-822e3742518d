/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.param.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量更新门店状态参数
 * <AUTHOR>
@Data
@ApiModel("批量更新门店状态参数")
public class BatchUpdateStatusParam {

    @ApiModelProperty("门店ID列表")
    @NotEmpty(message = "门店ID列表不能为空")
    private List<Long> storeIds;

    @ApiModelProperty("状态：0=禁用，1=启用")
    @NotNull(message = "状态不能为空")
    private Integer status;
}
