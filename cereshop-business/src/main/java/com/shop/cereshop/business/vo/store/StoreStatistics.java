/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.vo.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 门店统计信息
 * <AUTHOR>
@Data
@ApiModel("门店统计信息")
public class StoreStatistics {

    @ApiModelProperty("门店总数")
    private Integer totalStores;

    @ApiModelProperty("启用门店数")
    private Integer enabledStores;

    @ApiModelProperty("禁用门店数")
    private Integer disabledStores;

    @ApiModelProperty("支持自提的门店数")
    private Integer pickupEnabledStores;

    @ApiModelProperty("支持核销的门店数")
    private Integer verifyEnabledStores;

    @ApiModelProperty("今日核销订单数")
    private Integer todayVerifyOrders;

    @ApiModelProperty("本月核销订单数")
    private Integer monthVerifyOrders;

    @ApiModelProperty("今日自提订单数")
    private Integer todayPickupOrders;

    @ApiModelProperty("本月自提订单数")
    private Integer monthPickupOrders;
}
