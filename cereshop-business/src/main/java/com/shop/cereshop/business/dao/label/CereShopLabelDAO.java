/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.dao.label;

import com.shop.cereshop.business.page.label.ShopLabel;
import com.shop.cereshop.business.param.label.LabelGetAllParam;
import com.shop.cereshop.commons.domain.label.CereShopLabel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CereShopLabelDAO extends BaseMapper<CereShopLabel> {
    int deleteByPrimaryKey(Long labelId);

    int insertSelective(CereShopLabel record);

    CereShopLabel selectByPrimaryKey(Long labelId);

    int updateByPrimaryKeySelective(CereShopLabel record);

    int updateByPrimaryKey(CereShopLabel record);

    CereShopLabel findByShopIdNotGroup(@Param("shopId") Long shopId);

    CereShopLabel getById(@Param("labelId") Long labelId);

    List<ShopLabel> getAll(LabelGetAllParam param);
}
