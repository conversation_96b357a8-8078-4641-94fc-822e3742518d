/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.service.store;

import com.shop.cereshop.commons.domain.order.CereShopOrder;
import com.shop.cereshop.commons.domain.store.CereVerifyLog;
import com.shop.cereshop.commons.exception.CoBusinessException;

import java.util.Date;
import java.util.List;

/**
 * 核销服务接口
 * <AUTHOR>
public interface CereVerifyService {

    /**
     * 整单核销
     */
    void verifyWholeOrder(String verifyCode, Long staffId, String remark) throws CoBusinessException;

    /**
     * 验证核销码有效性
     */
    CereShopOrder validateVerifyCode(String verifyCode, Long staffId) throws CoBusinessException;

    /**
     * 生成核销码
     */
    String generateVerifyCode(Long orderId);

    /**
     * 根据订单ID查询核销记录
     */
    List<CereVerifyLog> getVerifyLogsByOrderId(Long orderId);

    /**
     * 根据订单号查询核销记录
     */
    List<CereVerifyLog> getVerifyLogsByOrderSn(String orderSn);

    /**
     * 根据核销码查询核销记录
     */
    List<CereVerifyLog> getVerifyLogsByVerifyCode(String verifyCode);

    /**
     * 根据商户ID查询核销记录
     */
    List<CereVerifyLog> getVerifyLogsByMerchantId(Long merchantId, Date startTime, Date endTime);

    /**
     * 根据门店ID查询核销记录
     */
    List<CereVerifyLog> getVerifyLogsByStoreId(Long storeId, Date startTime, Date endTime);

    /**
     * 统计商户核销数量
     */
    int countVerifyByMerchantId(Long merchantId, Date startTime, Date endTime);

    /**
     * 统计门店核销数量
     */
    int countVerifyByStoreId(Long storeId, Date startTime, Date endTime);

    /**
     * 检查订单是否可以核销
     */
    boolean canVerifyOrder(Long orderId, Long staffId) throws CoBusinessException;

    /**
     * 记录核销日志
     */
    void recordVerifyLog(CereVerifyLog verifyLog);
}
