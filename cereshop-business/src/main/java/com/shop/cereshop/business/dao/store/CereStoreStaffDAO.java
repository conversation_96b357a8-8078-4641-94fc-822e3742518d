/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.dao.store;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.commons.domain.store.CereStoreStaff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 门店员工DAO接口
 * <AUTHOR>
@Mapper
public interface CereStoreStaffDAO extends BaseMapper<CereStoreStaff> {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long staffId);

    /**
     * 根据主键查询
     */
    CereStoreStaff selectByPrimaryKey(Long staffId);

    /**
     * 选择性更新
     */
    int updateByPrimaryKeySelective(CereStoreStaff record);

    /**
     * 根据门店ID查询员工列表
     */
    List<CereStoreStaff> findByStoreId(@Param("storeId") Long storeId);

    /**
     * 根据商户ID查询员工列表
     */
    List<CereStoreStaff> findByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 根据门店ID查询启用的员工列表
     */
    List<CereStoreStaff> findEnabledByStoreId(@Param("storeId") Long storeId);

    /**
     * 根据手机号查询员工
     */
    CereStoreStaff findByPhone(@Param("staffPhone") String staffPhone);

    /**
     * 根据微信OpenID查询员工
     */
    CereStoreStaff findByWechatOpenid(@Param("wechatOpenid") String wechatOpenid);

    /**
     * 根据员工工号查询
     */
    CereStoreStaff findByStaffCode(@Param("staffCode") String staffCode);

    /**
     * 检查手机号是否存在（同一门店内）
     */
    int countByStoreIdAndPhone(@Param("storeId") Long storeId, 
                              @Param("staffPhone") String staffPhone, 
                              @Param("excludeStaffId") Long excludeStaffId);

    /**
     * 检查员工工号是否存在
     */
    int countByStaffCode(@Param("staffCode") String staffCode, @Param("excludeStaffId") Long excludeStaffId);

    /**
     * 根据门店ID查询有核销权限的员工
     */
    List<CereStoreStaff> findVerifyEnabledByStoreId(@Param("storeId") Long storeId);

    /**
     * 验证员工是否有指定门店的核销权限
     */
    int checkVerifyPermission(@Param("staffId") Long staffId, @Param("storeId") Long storeId);

    /**
     * 统计门店员工数量
     */
    int countByStoreId(@Param("storeId") Long storeId);
}
