/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.service.redis.impl;

import com.shop.cereshop.business.dao.redis.CereRedisKeyDAO;
import com.shop.cereshop.business.redis.service.api.StringRedisService;
import com.shop.cereshop.business.service.redis.CereRedisKeyServcice;
import com.shop.cereshop.business.task.RedisKeyTask;
import com.shop.cereshop.commons.domain.redis.CereRedisKey;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.utils.EmptyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CereRedisKeyServciceImpl implements CereRedisKeyServcice {

    @Autowired
    private CereRedisKeyDAO cereRedisKeyDAO;

    @Autowired
    private StringRedisService stringRedisService;

    @Override
    public void add(String key, String endTime) throws CoBusinessException {
        CereRedisKey cereRedisKey=new CereRedisKey();
        cereRedisKey.setRedisKey(key);
        cereRedisKey.setEndTime(endTime);
        cereRedisKeyDAO.insert(cereRedisKey);
    }

    @Override
    public void updateByKey(String key, String endTime) throws CoBusinessException {
        CereRedisKey cereRedisKey=new CereRedisKey();
        //查询是否存在数据
        String time = cereRedisKeyDAO.findByKey(key);
        if(!EmptyUtils.isEmpty(time)){
            //更新
            cereRedisKey.setRedisKey(key);
            cereRedisKey.setEndTime(endTime);
            cereRedisKeyDAO.updateByKey(cereRedisKey);
        }else {
            //新增
            cereRedisKey.setRedisKey(key);
            cereRedisKey.setEndTime(endTime);
            cereRedisKeyDAO.insert(cereRedisKey);
        }
    }

    @Override
    public void deleteByKey(String key) throws CoBusinessException {
        cereRedisKeyDAO.deleteByKey(key);
    }

    @Override
    public String findByKey(String key) {
        return cereRedisKeyDAO.findByKey(key);
    }

    @Override
    @Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,rollbackFor = {CoBusinessException.class, Exception.class})
    public void initializationRedis() throws Exception {
        //查询所有任务
        List<CereRedisKey> list=cereRedisKeyDAO.findAll();
        if(!EmptyUtils.isEmpty(list)){
            //删除所有任务redis缓存
            list.forEach(a -> {
                stringRedisService.delete(a.getRedisKey());
            });
            //将数据拆分成多个小的集合，开启多线程设置
            List<List<CereRedisKey>> split = EmptyUtils.split(list, 50);
            if(!EmptyUtils.isEmpty(split)){
                split.forEach(keys -> {
                    new Thread(new RedisKeyTask(keys,stringRedisService)).start();
                });
            }
        }
    }
}
