/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.param.tool;

import com.shop.cereshop.commons.domain.common.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 营销活动选择商品规格请求参数
 */
@Data
@ApiModel(value = "ToolSkuParam", description = "营销活动选择商品规格请求参数")
public class ToolSkuParam extends PageParam {

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long productId;
}
