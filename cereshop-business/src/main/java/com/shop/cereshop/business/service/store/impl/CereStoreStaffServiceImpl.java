/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.service.store.impl;

import com.shop.cereshop.business.dao.store.CereStoreStaffDAO;
import com.shop.cereshop.business.service.store.CereStoreStaffService;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.constant.IntegerEnum;
import com.shop.cereshop.commons.domain.store.CereStoreStaff;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.utils.EmptyUtils;
import com.shop.cereshop.commons.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 门店员工服务实现类
 * <AUTHOR>
@Service
@Slf4j
public class CereStoreStaffServiceImpl implements CereStoreStaffService {

    @Autowired
    private CereStoreStaffDAO cereStoreStaffDAO;

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
    public CereStoreStaff createStaff(CereStoreStaff staff) throws CoBusinessException {
        // 参数验证
        if (staff == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }
        if (staff.getStoreId() == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店ID不能为空");
        }
        if (staff.getMerchantId() == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "商户ID不能为空");
        }
        if (EmptyUtils.isEmpty(staff.getStaffName())) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工姓名不能为空");
        }
        if (EmptyUtils.isEmpty(staff.getStaffPhone())) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工手机号不能为空");
        }

        // 验证手机号唯一性（同一门店内）
        if (!isPhoneUniqueInStore(staff.getStoreId(), staff.getStaffPhone(), null)) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "该手机号已存在");
        }

        // 验证员工工号唯一性
        if (!EmptyUtils.isEmpty(staff.getStaffCode())) {
            if (!isStaffCodeUnique(staff.getStaffCode(), null)) {
                throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工工号已存在");
            }
        }

        // 设置默认值
        String currentTime = TimeUtils.yyMMddHHmmss();
        staff.setCreateTime(currentTime);
        staff.setUpdateTime(currentTime);
        
        if (staff.getRoleType() == null) {
            staff.setRoleType(1); // 默认普通员工
        }
        if (staff.getCanPickup() == null) {
            staff.setCanPickup(IntegerEnum.YES.getCode());
        }
        if (staff.getCanVerify() == null) {
            staff.setCanVerify(IntegerEnum.YES.getCode());
        }
        if (staff.getCanRefund() == null) {
            staff.setCanRefund(IntegerEnum.NO.getCode());
        }
        if (staff.getStatus() == null) {
            staff.setStatus(IntegerEnum.YES.getCode());
        }

        // 插入数据库
        int result = cereStoreStaffDAO.insert(staff);
        if (result <= 0) {
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "创建员工失败");
        }

        return staff;
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
    public CereStoreStaff updateStaff(CereStoreStaff staff) throws CoBusinessException {
        // 参数验证
        if (staff == null || staff.getStaffId() == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        // 检查员工是否存在
        CereStoreStaff existingStaff = cereStoreStaffDAO.selectByPrimaryKey(staff.getStaffId());
        if (existingStaff == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工不存在");
        }

        // 验证手机号唯一性（同一门店内）
        if (!EmptyUtils.isEmpty(staff.getStaffPhone())) {
            if (!isPhoneUniqueInStore(existingStaff.getStoreId(), staff.getStaffPhone(), staff.getStaffId())) {
                throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "该手机号已存在");
            }
        }

        // 验证员工工号唯一性
        if (!EmptyUtils.isEmpty(staff.getStaffCode())) {
            if (!isStaffCodeUnique(staff.getStaffCode(), staff.getStaffId())) {
                throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工工号已存在");
            }
        }

        // 设置更新时间
        staff.setUpdateTime(TimeUtils.yyMMddHHmmss());

        // 更新数据库
        int result = cereStoreStaffDAO.updateByPrimaryKeySelective(staff);
        if (result <= 0) {
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "更新员工失败");
        }

        return cereStoreStaffDAO.selectByPrimaryKey(staff.getStaffId());
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
    public void deleteStaff(Long staffId) throws CoBusinessException {
        if (staffId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        // 检查员工是否存在
        CereStoreStaff staff = cereStoreStaffDAO.selectByPrimaryKey(staffId);
        if (staff == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工不存在");
        }

        // TODO: 检查是否有关联的核销记录，如果有则不允许删除

        // 删除员工
        int result = cereStoreStaffDAO.deleteByPrimaryKey(staffId);
        if (result <= 0) {
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "删除员工失败");
        }
    }

    @Override
    public CereStoreStaff getStaffById(Long staffId) throws CoBusinessException {
        if (staffId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereStoreStaff staff = cereStoreStaffDAO.selectByPrimaryKey(staffId);
        if (staff == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工不存在");
        }

        return staff;
    }

    @Override
    public List<CereStoreStaff> getStaffByStoreId(Long storeId) {
        if (storeId == null) {
            return null;
        }
        return cereStoreStaffDAO.findByStoreId(storeId);
    }

    @Override
    public List<CereStoreStaff> getStaffByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return null;
        }
        return cereStoreStaffDAO.findByMerchantId(merchantId);
    }

    @Override
    public List<CereStoreStaff> getEnabledStaffByStoreId(Long storeId) {
        if (storeId == null) {
            return null;
        }
        return cereStoreStaffDAO.findEnabledByStoreId(storeId);
    }

    @Override
    public CereStoreStaff getStaffByPhone(String staffPhone) {
        if (EmptyUtils.isEmpty(staffPhone)) {
            return null;
        }
        return cereStoreStaffDAO.findByPhone(staffPhone);
    }

    @Override
    public CereStoreStaff getStaffByWechatOpenid(String wechatOpenid) {
        if (EmptyUtils.isEmpty(wechatOpenid)) {
            return null;
        }
        return cereStoreStaffDAO.findByWechatOpenid(wechatOpenid);
    }

    @Override
    public CereStoreStaff getStaffByStaffCode(String staffCode) {
        if (EmptyUtils.isEmpty(staffCode)) {
            return null;
        }
        return cereStoreStaffDAO.findByStaffCode(staffCode);
    }

    @Override
    public List<CereStoreStaff> getVerifyEnabledStaffByStoreId(Long storeId) {
        if (storeId == null) {
            return null;
        }
        return cereStoreStaffDAO.findVerifyEnabledByStoreId(storeId);
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
    public void updateStaffStatus(Long staffId, Integer status) throws CoBusinessException {
        if (staffId == null || status == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereStoreStaff staff = new CereStoreStaff();
        staff.setStaffId(staffId);
        staff.setStatus(status);
        staff.setUpdateTime(TimeUtils.yyMMddHHmmss());

        int result = cereStoreStaffDAO.updateByPrimaryKeySelective(staff);
        if (result <= 0) {
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "更新员工状态失败");
        }
    }

    @Override
    public boolean isPhoneUniqueInStore(Long storeId, String staffPhone, Long excludeStaffId) {
        if (storeId == null || EmptyUtils.isEmpty(staffPhone)) {
            return true;
        }
        int count = cereStoreStaffDAO.countByStoreIdAndPhone(storeId, staffPhone, excludeStaffId);
        return count == 0;
    }

    @Override
    public boolean isStaffCodeUnique(String staffCode, Long excludeStaffId) {
        if (EmptyUtils.isEmpty(staffCode)) {
            return true;
        }
        int count = cereStoreStaffDAO.countByStaffCode(staffCode, excludeStaffId);
        return count == 0;
    }

    @Override
    public boolean hasVerifyPermission(Long staffId, Long storeId) throws CoBusinessException {
        if (staffId == null || storeId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        int count = cereStoreStaffDAO.checkVerifyPermission(staffId, storeId);
        return count > 0;
    }

    @Override
    public boolean validateStaffOwnership(Long staffId, Long merchantId) throws CoBusinessException {
        if (staffId == null || merchantId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereStoreStaff staff = cereStoreStaffDAO.selectByPrimaryKey(staffId);
        if (staff == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "员工不存在");
        }

        return merchantId.equals(staff.getMerchantId());
    }
}
