/*
* Copyright (C) 2017-2021
* All rights reserved, Designed By 深圳中科鑫智科技有限公司
* Copyright authorization contact ***********
*/
package com.shop.cereshop.business.dao.customer_service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.commons.domain.customer_service.CereCustomerServiceReceptionist;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * Mapper 接口
 * 客服接待员表
 * </p>
 *
 * <AUTHOR>
 * @date 2021-12-08
 */
@Mapper
public interface CereCustomerServiceReceptionistDAO extends BaseMapper<CereCustomerServiceReceptionist> {

}
