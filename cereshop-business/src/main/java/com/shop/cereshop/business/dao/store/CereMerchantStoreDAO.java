/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.dao.store;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.business.param.store.StoreListParam;
import com.shop.cereshop.business.vo.store.StoreStatistics;
import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商户门店DAO接口
 * <AUTHOR>
@Mapper
public interface CereMerchantStoreDAO extends BaseMapper<CereMerchantStore> {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long storeId);

    /**
     * 根据主键查询
     */
    CereMerchantStore selectByPrimaryKey(Long storeId);

    /**
     * 选择性更新
     */
    int updateByPrimaryKeySelective(CereMerchantStore record);

    /**
     * 根据商户ID查询门店列表
     */
    List<CereMerchantStore> findByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 根据商户ID查询启用的门店列表
     */
    List<CereMerchantStore> findEnabledByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 根据商户ID查询支持自提的门店列表
     */
    List<CereMerchantStore> findPickupEnabledByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 根据商户ID查询支持核销的门店列表
     */
    List<CereMerchantStore> findVerifyEnabledByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 根据门店编码查询
     */
    CereMerchantStore findByStoreCode(@Param("storeCode") String storeCode);

    /**
     * 检查门店编码是否存在
     */
    int countByStoreCode(@Param("storeCode") String storeCode, @Param("excludeStoreId") Long excludeStoreId);

    /**
     * 根据坐标范围查询附近门店
     */
    List<CereMerchantStore> findNearbyStores(@Param("merchantId") Long merchantId,
                                           @Param("longitude") Double longitude,
                                           @Param("latitude") Double latitude,
                                           @Param("distance") Double distance);

    /**
     * 统计商户门店数量
     */
    int countByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 批量更新门店状态
     */
    int batchUpdateStatus(@Param("storeIds") List<Long> storeIds, @Param("status") Integer status);

    /**
     * 分页查询门店列表（支持筛选）
     */
    List<CereMerchantStore> getStoreListWithFilter(StoreListParam param);

    /**
     * 获取门店统计信息
     */
    StoreStatistics getStoreStatistics(@Param("merchantId") Long merchantId);

    /**
     * 根据商户ID查找默认门店
     */
    CereMerchantStore findDefaultByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 清除商户的默认门店设置
     */
    int clearDefaultStore(@Param("merchantId") Long merchantId);

    /**
     * 通过存储过程设置默认门店（确保唯一性）
     */
    void setDefaultStoreByProcedure(@Param("storeId") Long storeId, @Param("merchantId") Long merchantId);
}
