/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.service.store.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.shop.cereshop.business.dao.store.CereMerchantStoreDAO;
import com.shop.cereshop.business.page.order.ShopOrder;
import com.shop.cereshop.business.param.store.StoreListParam;
import com.shop.cereshop.business.service.store.CereMerchantStoreService;
import com.shop.cereshop.business.vo.store.StoreStatistics;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.constant.IntegerEnum;
import com.shop.cereshop.commons.domain.common.Page;
import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.utils.EmptyUtils;
import com.shop.cereshop.commons.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 商户门店服务实现类
 * <AUTHOR>
@Service
@Slf4j
public class CereMerchantStoreServiceImpl implements CereMerchantStoreService {

    @Autowired
    private CereMerchantStoreDAO cereMerchantStoreDAO;

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
    public CereMerchantStore createStore(CereMerchantStore store) throws CoBusinessException {
        // 参数验证
        if (store == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }
        if (EmptyUtils.isEmpty(store.getStoreName())) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店名称不能为空");
        }
        if (EmptyUtils.isEmpty(store.getContactName())) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "联系人姓名不能为空");
        }
        if (EmptyUtils.isEmpty(store.getContactPhone())) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "联系电话不能为空");
        }
        if (EmptyUtils.isEmpty(store.getAddress())) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店地址不能为空");
        }

        // 验证门店编码唯一性
        if (!EmptyUtils.isEmpty(store.getStoreCode())) {
            if (!isStoreCodeUnique(store.getStoreCode(), null)) {
                throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店编码已存在");
            }
        }

        // 设置默认值
        String currentTime = TimeUtils.yyMMddHHmmss();
        store.setCreateTime(currentTime);
        store.setUpdateTime(currentTime);
        
        if (store.getIsPickupEnabled() == null) {
            store.setIsPickupEnabled(IntegerEnum.YES.getCode());
        }
        if (store.getIsVerifyEnabled() == null) {
            store.setIsVerifyEnabled(IntegerEnum.YES.getCode());
        }
        if (store.getStatus() == null) {
            store.setStatus(IntegerEnum.YES.getCode());
        }
        if (store.getSortOrder() == null) {
            store.setSortOrder(0);
        }

        // 插入数据库
        int result = cereMerchantStoreDAO.insert(store);
        if (result <= 0) {
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "创建门店失败");
        }

        return store;
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
    public CereMerchantStore updateStore(CereMerchantStore store) throws CoBusinessException {
        // 参数验证
        if (store == null || store.getStoreId() == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        // 检查门店是否存在
        CereMerchantStore existingStore = cereMerchantStoreDAO.selectByPrimaryKey(store.getStoreId());
        if (existingStore == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店不存在");
        }

        // 验证门店编码唯一性
        if (!EmptyUtils.isEmpty(store.getStoreCode())) {
            if (!isStoreCodeUnique(store.getStoreCode(), store.getStoreId())) {
                throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店编码已存在");
            }
        }

        // 设置更新时间
        store.setUpdateTime(TimeUtils.yyMMddHHmmss());

        // 更新数据库
        int result = cereMerchantStoreDAO.updateByPrimaryKeySelective(store);
        if (result <= 0) {
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "更新门店失败");
        }

        return cereMerchantStoreDAO.selectByPrimaryKey(store.getStoreId());
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
    public void deleteStore(Long storeId) throws CoBusinessException {
        if (storeId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        // 检查门店是否存在
        CereMerchantStore store = cereMerchantStoreDAO.selectByPrimaryKey(storeId);
        if (store == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店不存在");
        }

        // TODO: 检查是否有关联的员工或订单，如果有则不允许删除

        // 删除门店
        int result = cereMerchantStoreDAO.deleteByPrimaryKey(storeId);
        if (result <= 0) {
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "删除门店失败");
        }
    }

    @Override
    public CereMerchantStore getStoreById(Long storeId) throws CoBusinessException {
        if (storeId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereMerchantStore store = cereMerchantStoreDAO.selectByPrimaryKey(storeId);
        if (store == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店不存在");
        }

        return store;
    }

    @Override
    public List<CereMerchantStore> getStoresByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return null;
        }
        return cereMerchantStoreDAO.findByMerchantId(merchantId);
    }

    @Override
    public List<CereMerchantStore> getEnabledStoresByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return null;
        }
        return cereMerchantStoreDAO.findEnabledByMerchantId(merchantId);
    }

    @Override
    public List<CereMerchantStore> getPickupEnabledStoresByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return null;
        }
        return cereMerchantStoreDAO.findPickupEnabledByMerchantId(merchantId);
    }

    @Override
    public List<CereMerchantStore> getVerifyEnabledStoresByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return null;
        }
        return cereMerchantStoreDAO.findVerifyEnabledByMerchantId(merchantId);
    }

    @Override
    public List<CereMerchantStore> getNearbyStores(Long merchantId, Double longitude, Double latitude, Double distance) {
        if (merchantId == null || longitude == null || latitude == null) {
            return null;
        }
        if (distance == null) {
            distance = 50.0; // 默认50公里
        }
        return cereMerchantStoreDAO.findNearbyStores(merchantId, longitude, latitude, distance);
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, rollbackFor = {CoBusinessException.class, Exception.class})
    public void updateStoreStatus(Long storeId, Integer status) throws CoBusinessException {
        if (storeId == null || status == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereMerchantStore store = new CereMerchantStore();
        store.setStoreId(storeId);
        store.setStatus(status);
        store.setUpdateTime(TimeUtils.yyMMddHHmmss());

        int result = cereMerchantStoreDAO.updateByPrimaryKeySelective(store);
        if (result <= 0) {
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "更新门店状态失败");
        }
    }



    @Override
    public boolean isStoreCodeUnique(String storeCode, Long excludeStoreId) {
        if (EmptyUtils.isEmpty(storeCode)) {
            return true;
        }
        int count = cereMerchantStoreDAO.countByStoreCode(storeCode, excludeStoreId);
        return count == 0;
    }

    @Override
    public int countStoresByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return 0;
        }
        return cereMerchantStoreDAO.countByMerchantId(merchantId);
    }

    @Override
    public boolean validateStoreOwnership(Long storeId, Long merchantId) throws CoBusinessException {
        if (storeId == null || merchantId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereMerchantStore store = cereMerchantStoreDAO.selectByPrimaryKey(storeId);
        if (store == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店不存在");
        }

        return merchantId.equals(store.getMerchantId());
    }

    @Override
    public boolean isPickupEnabled(Long storeId) throws CoBusinessException {
        CereMerchantStore store = getStoreById(storeId);
        return IntegerEnum.YES.getCode().equals(store.getIsPickupEnabled()) 
               && IntegerEnum.YES.getCode().equals(store.getStatus());
    }

    @Override
    public boolean isVerifyEnabled(Long storeId) throws CoBusinessException {
        CereMerchantStore store = getStoreById(storeId);
        return IntegerEnum.YES.getCode().equals(store.getIsVerifyEnabled())
               && IntegerEnum.YES.getCode().equals(store.getStatus());
    }

    @Override
    public Page getStoreListWithPage(StoreListParam param) {
        PageHelper.startPage(param.getPage(), param.getSize());
        List<CereMerchantStore> stores = cereMerchantStoreDAO.getStoreListWithFilter(param);

        PageInfo<CereMerchantStore> pageInfo=new PageInfo<>(stores);
        Page page=new Page(pageInfo.getList(),pageInfo.getTotal());
        return page;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStoreStatus(List<Long> storeIds, Integer status) throws CoBusinessException {
        String updateTime = TimeUtils.yyMMddHHmmss();
        for (Long storeId : storeIds) {
            CereMerchantStore store = getStoreById(storeId);
            store.setStatus(status);
            store.setUpdateTime(updateTime);
            cereMerchantStoreDAO.updateByPrimaryKeySelective(store);
        }
    }

    @Override
    public StoreStatistics getStoreStatistics(Long merchantId) {
        return cereMerchantStoreDAO.getStoreStatistics(merchantId);
    }

    @Override
    public CereMerchantStore getDefaultStore(Long merchantId) throws CoBusinessException {
        if (merchantId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        CereMerchantStore defaultStore = cereMerchantStoreDAO.findDefaultByMerchantId(merchantId);
        if (defaultStore == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "未找到默认门店");
        }

        return defaultStore;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultStore(Long storeId, Long merchantId) throws CoBusinessException {
        if (storeId == null || merchantId == null) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID);
        }

        // 验证门店是否属于当前商户
        if (!validateStoreOwnership(storeId, merchantId)) {
            throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "门店不属于当前商户");
        }

        try {
            // 使用存储过程确保默认门店唯一性
            cereMerchantStoreDAO.setDefaultStoreByProcedure(storeId, merchantId);
        } catch (Exception e) {
            log.error("设置默认门店失败", e);
            throw new CoBusinessException(CoReturnFormat.SYS_ERROR, "设置默认门店失败");
        }
    }
}
