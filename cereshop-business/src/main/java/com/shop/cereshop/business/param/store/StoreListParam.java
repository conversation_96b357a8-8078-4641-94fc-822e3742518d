/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.param.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 门店列表查询参数
 * <AUTHOR>
@Data
@ApiModel("门店列表查询参数")
public class StoreListParam {

    @ApiModelProperty("商户ID")
    private Long merchantId;

    @ApiModelProperty("页码")
    private Integer page = 1;

    @ApiModelProperty("每页大小")
    private Integer size = 20;

    @ApiModelProperty("门店状态：0=禁用，1=启用")
    private Integer status;

    @ApiModelProperty("是否支持自提：0=否，1=是")
    private Integer isPickupEnabled;

    @ApiModelProperty("是否支持核销：0=否，1=是")
    private Integer isVerifyEnabled;

    @ApiModelProperty("搜索关键词（门店名称、编码、地址）")
    private String keyword;

    @ApiModelProperty("排序字段")
    private String orderBy = "sort_order";

    @ApiModelProperty("排序方向：ASC/DESC")
    private String orderDirection = "ASC";
}
