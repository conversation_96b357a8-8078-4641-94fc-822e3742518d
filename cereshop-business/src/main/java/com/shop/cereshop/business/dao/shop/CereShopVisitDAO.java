/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.dao.shop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.business.page.index.UserVisitDTO;
import com.shop.cereshop.commons.domain.shop.CereShopVisit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CereShopVisitDAO extends BaseMapper<CereShopVisit> {

    int insertSelective(CereShopVisit record);

    List<UserVisitDTO> selectDistinctVisitUser(@Param("shopId")Long shopId,
                                               @Param("startTime") String startTime,
                                               @Param("endTime") String endTime);
}
