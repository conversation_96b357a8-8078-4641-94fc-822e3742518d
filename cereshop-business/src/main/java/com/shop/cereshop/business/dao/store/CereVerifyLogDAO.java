/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.dao.store;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.commons.domain.store.CereVerifyLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 核销记录DAO接口
 * <AUTHOR>
@Mapper
public interface CereVerifyLogDAO extends BaseMapper<CereVerifyLog> {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long logId);

    /**
     * 根据主键查询
     */
    CereVerifyLog selectByPrimaryKey(Long logId);

    /**
     * 选择性更新
     */
    int updateByPrimaryKeySelective(CereVerifyLog record);

    /**
     * 根据订单ID查询核销记录
     */
    List<CereVerifyLog> findByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单号查询核销记录
     */
    List<CereVerifyLog> findByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 根据核销码查询核销记录
     */
    List<CereVerifyLog> findByVerifyCode(@Param("verifyCode") String verifyCode);

    /**
     * 根据商户ID查询核销记录
     */
    List<CereVerifyLog> findByMerchantId(@Param("merchantId") Long merchantId, 
                                        @Param("startTime") Date startTime, 
                                        @Param("endTime") Date endTime);

    /**
     * 根据门店ID查询核销记录
     */
    List<CereVerifyLog> findByStoreId(@Param("storeId") Long storeId, 
                                     @Param("startTime") Date startTime, 
                                     @Param("endTime") Date endTime);

    /**
     * 根据员工ID查询核销记录
     */
    List<CereVerifyLog> findByStaffId(@Param("staffId") Long staffId, 
                                     @Param("startTime") Date startTime, 
                                     @Param("endTime") Date endTime);

    /**
     * 根据订单商品ID查询核销记录
     */
    List<CereVerifyLog> findByOrderProductId(@Param("orderProductId") Long orderProductId);

    /**
     * 统计商户核销数量
     */
    int countByMerchantId(@Param("merchantId") Long merchantId, 
                         @Param("startTime") Date startTime, 
                         @Param("endTime") Date endTime);

    /**
     * 统计门店核销数量
     */
    int countByStoreId(@Param("storeId") Long storeId, 
                      @Param("startTime") Date startTime, 
                      @Param("endTime") Date endTime);

    /**
     * 统计员工核销数量
     */
    int countByStaffId(@Param("staffId") Long staffId, 
                      @Param("startTime") Date startTime, 
                      @Param("endTime") Date endTime);

    /**
     * 根据核销类型统计
     */
    int countByVerifyType(@Param("merchantId") Long merchantId, 
                         @Param("verifyType") Integer verifyType, 
                         @Param("startTime") Date startTime, 
                         @Param("endTime") Date endTime);

    /**
     * 查询最近的核销记录
     */
    List<CereVerifyLog> findRecentLogs(@Param("merchantId") Long merchantId, 
                                      @Param("limit") Integer limit);
}
