/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.business.dao.live;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.business.param.live.LiveProductGetAllParam;
import com.shop.cereshop.commons.domain.live.CereLiveProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CereLiveProductDAO extends BaseMapper<CereLiveProduct> {

    List<CereLiveProduct> getAll(LiveProductGetAllParam param);

    int updateByPrimaryKey(CereLiveProduct live);

    int deleteByIdAndShopId(@Param("id")Long id, @Param("shopId")Long shopId);

    CereLiveProduct getByIdAndShopId(@Param("id")Long id, @Param("shopId")Long shopId);

    int selectCountByProductIdAndId(@Param("productId")Long productId, @Param("id") Long id);
}
