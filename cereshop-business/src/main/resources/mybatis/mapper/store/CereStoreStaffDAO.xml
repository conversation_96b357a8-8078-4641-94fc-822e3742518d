<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.store.CereStoreStaffDAO">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.store.CereStoreStaff">
        <id column="staff_id" jdbcType="BIGINT" property="staffId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="staff_name" jdbcType="VARCHAR" property="staffName"/>
        <result column="staff_phone" jdbcType="VARCHAR" property="staffPhone"/>
        <result column="staff_code" jdbcType="VARCHAR" property="staffCode"/>
        <result column="wechat_openid" jdbcType="VARCHAR" property="wechatOpenid"/>
        <result column="role_type" jdbcType="TINYINT" property="roleType"/>
        <result column="can_pickup" jdbcType="TINYINT" property="canPickup"/>
        <result column="can_verify" jdbcType="TINYINT" property="canVerify"/>
        <result column="can_refund" jdbcType="TINYINT" property="canRefund"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        staff_id, store_id, merchant_id, user_id, staff_name, staff_phone, staff_code, 
        wechat_openid, role_type, can_pickup, can_verify, can_refund, status, 
        create_time, update_time
    </sql>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM cere_store_staff
        WHERE staff_id = #{staffId,jdbcType=BIGINT}
    </delete>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_store_staff
        WHERE staff_id = #{staffId,jdbcType=BIGINT}
    </select>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.store.CereStoreStaff">
        UPDATE cere_store_staff
        <set>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=BIGINT},
            </if>
            <if test="merchantId != null">
                merchant_id = #{merchantId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="staffName != null">
                staff_name = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="staffPhone != null">
                staff_phone = #{staffPhone,jdbcType=VARCHAR},
            </if>
            <if test="staffCode != null">
                staff_code = #{staffCode,jdbcType=VARCHAR},
            </if>
            <if test="wechatOpenid != null">
                wechat_openid = #{wechatOpenid,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                role_type = #{roleType,jdbcType=TINYINT},
            </if>
            <if test="canPickup != null">
                can_pickup = #{canPickup,jdbcType=TINYINT},
            </if>
            <if test="canVerify != null">
                can_verify = #{canVerify,jdbcType=TINYINT},
            </if>
            <if test="canRefund != null">
                can_refund = #{canRefund,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE staff_id = #{staffId,jdbcType=BIGINT}
    </update>

    <!-- 根据门店ID查询员工列表 -->
    <select id="findByStoreId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_store_staff
        WHERE store_id = #{storeId,jdbcType=BIGINT}
        ORDER BY create_time DESC
    </select>

    <!-- 根据商户ID查询员工列表 -->
    <select id="findByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_store_staff
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        ORDER BY create_time DESC
    </select>

    <!-- 根据门店ID查询启用的员工列表 -->
    <select id="findEnabledByStoreId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_store_staff
        WHERE store_id = #{storeId,jdbcType=BIGINT}
        AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据手机号查询员工 -->
    <select id="findByPhone" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_store_staff
        WHERE staff_phone = #{staffPhone,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <!-- 根据微信OpenID查询员工 -->
    <select id="findByWechatOpenid" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_store_staff
        WHERE wechat_openid = #{wechatOpenid,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <!-- 根据员工工号查询 -->
    <select id="findByStaffCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_store_staff
        WHERE staff_code = #{staffCode,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <!-- 检查手机号是否存在（同一门店内） -->
    <select id="countByStoreIdAndPhone" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_store_staff
        WHERE store_id = #{storeId,jdbcType=BIGINT}
        AND staff_phone = #{staffPhone,jdbcType=VARCHAR}
        <if test="excludeStaffId != null">
            AND staff_id != #{excludeStaffId,jdbcType=BIGINT}
        </if>
    </select>

    <!-- 检查员工工号是否存在 -->
    <select id="countByStaffCode" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_store_staff
        WHERE staff_code = #{staffCode,jdbcType=VARCHAR}
        <if test="excludeStaffId != null">
            AND staff_id != #{excludeStaffId,jdbcType=BIGINT}
        </if>
    </select>

    <!-- 根据门店ID查询有核销权限的员工 -->
    <select id="findVerifyEnabledByStoreId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_store_staff
        WHERE store_id = #{storeId,jdbcType=BIGINT}
        AND status = 1
        AND can_verify = 1
        ORDER BY create_time DESC
    </select>

    <!-- 验证员工是否有指定门店的核销权限 -->
    <select id="checkVerifyPermission" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_store_staff
        WHERE staff_id = #{staffId,jdbcType=BIGINT}
        AND store_id = #{storeId,jdbcType=BIGINT}
        AND status = 1
        AND can_verify = 1
    </select>

    <!-- 统计门店员工数量 -->
    <select id="countByStoreId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_store_staff
        WHERE store_id = #{storeId,jdbcType=BIGINT}
    </select>

    <!-- 批量更新员工状态 -->
    <update id="batchUpdateStatus">
        UPDATE cere_store_staff
        SET status = #{status,jdbcType=TINYINT},
            update_time = NOW()
        WHERE staff_id IN
        <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">
            #{staffId}
        </foreach>
    </update>

</mapper>
