<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.store.CereMerchantStoreDAO">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.store.CereMerchantStore">
        <id column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="store_code" jdbcType="VARCHAR" property="storeCode"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="province_id" jdbcType="INTEGER" property="provinceId"/>
        <result column="city_id" jdbcType="INTEGER" property="cityId"/>
        <result column="area_id" jdbcType="INTEGER" property="areaId"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="longitude" jdbcType="DECIMAL" property="longitude"/>
        <result column="latitude" jdbcType="DECIMAL" property="latitude"/>
        <result column="business_hours" jdbcType="VARCHAR" property="businessHours"/>
        <result column="pickup_hours" jdbcType="VARCHAR" property="pickupHours"/>
        <result column="store_image" jdbcType="VARCHAR" property="storeImage"/>
        <result column="store_desc" jdbcType="LONGVARCHAR" property="storeDesc"/>
        <result column="is_pickup_enabled" jdbcType="TINYINT" property="isPickupEnabled"/>
        <result column="is_verify_enabled" jdbcType="TINYINT" property="isVerifyEnabled"/>
        <result column="sort_order" jdbcType="INTEGER" property="sortOrder"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_default" jdbcType="TINYINT" property="isDefault"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        store_id, merchant_id, store_name, store_code, contact_name, contact_phone,
        province_id, city_id, area_id, address, longitude, latitude, business_hours,
        pickup_hours, store_image, store_desc, is_pickup_enabled, is_verify_enabled,
        sort_order, status, is_default, create_time, update_time
    </sql>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM cere_merchant_store
        WHERE store_id = #{storeId,jdbcType=BIGINT}
    </delete>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE store_id = #{storeId,jdbcType=BIGINT}
    </select>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.store.CereMerchantStore">
        UPDATE cere_merchant_store
        <set>
            <if test="merchantId != null">
                merchant_id = #{merchantId,jdbcType=BIGINT},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="storeCode != null">
                store_code = #{storeCode,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null">
                contact_name = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                contact_phone = #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=INTEGER},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null">
                longitude = #{longitude,jdbcType=DECIMAL},
            </if>
            <if test="latitude != null">
                latitude = #{latitude,jdbcType=DECIMAL},
            </if>
            <if test="businessHours != null">
                business_hours = #{businessHours,jdbcType=VARCHAR},
            </if>
            <if test="pickupHours != null">
                pickup_hours = #{pickupHours,jdbcType=VARCHAR},
            </if>
            <if test="storeImage != null">
                store_image = #{storeImage,jdbcType=VARCHAR},
            </if>
            <if test="storeDesc != null">
                store_desc = #{storeDesc,jdbcType=LONGVARCHAR},
            </if>
            <if test="isPickupEnabled != null">
                is_pickup_enabled = #{isPickupEnabled,jdbcType=TINYINT},
            </if>
            <if test="isVerifyEnabled != null">
                is_verify_enabled = #{isVerifyEnabled,jdbcType=TINYINT},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE store_id = #{storeId,jdbcType=BIGINT}
    </update>

    <!-- 根据商户ID查询门店列表 -->
    <select id="findByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        ORDER BY sort_order DESC, create_time DESC
    </select>

    <!-- 根据商户ID查询启用的门店列表 -->
    <select id="findEnabledByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND status = 1
        ORDER BY sort_order DESC, create_time DESC
    </select>

    <!-- 根据商户ID查询支持自提的门店列表 -->
    <select id="findPickupEnabledByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND status = 1
        AND is_pickup_enabled = 1
        ORDER BY sort_order DESC, create_time DESC
    </select>

    <!-- 根据商户ID查询支持核销的门店列表 -->
    <select id="findVerifyEnabledByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND status = 1
        AND is_verify_enabled = 1
        ORDER BY sort_order DESC, create_time DESC
    </select>

    <!-- 根据门店编码查询 -->
    <select id="findByStoreCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE store_code = #{storeCode,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <!-- 检查门店编码是否存在 -->
    <select id="countByStoreCode" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_merchant_store
        WHERE store_code = #{storeCode,jdbcType=VARCHAR} and merchant_id = #{shopId,jdbcType=BIGINT}
        <if test="excludeStoreId != null">
            AND store_id != #{excludeStoreId,jdbcType=BIGINT}
        </if>
    </select>

    <!-- 根据坐标范围查询附近门店 -->
    <select id="findNearbyStores" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>,
        (6371 * acos(cos(radians(#{latitude})) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(#{longitude})) + 
        sin(radians(#{latitude})) * sin(radians(latitude)))) AS distance
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND status = 1
        AND is_pickup_enabled = 1
        AND longitude IS NOT NULL
        AND latitude IS NOT NULL
        HAVING distance &lt;= #{distance}
        ORDER BY distance ASC
    </select>

    <!-- 统计商户门店数量 -->
    <select id="countByMerchantId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
    </select>

    <!-- 批量更新门店状态 -->
    <update id="batchUpdateStatus">
        UPDATE cere_merchant_store
        SET status = #{status,jdbcType=TINYINT},
            update_time = NOW()
        WHERE store_id IN
        <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
            #{storeId}
        </foreach>
    </update>

    <!-- 分页查询门店列表（支持筛选） -->
    <select id="getStoreListWithFilter" parameterType="com.shop.cereshop.business.param.store.StoreListParam" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        <if test="status != null">
            AND status = #{status,jdbcType=TINYINT}
        </if>
        <if test="isPickupEnabled != null">
            AND is_pickup_enabled = #{isPickupEnabled,jdbcType=TINYINT}
        </if>
        <if test="isVerifyEnabled != null">
            AND is_verify_enabled = #{isVerifyEnabled,jdbcType=TINYINT}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (store_name LIKE CONCAT('%', #{keyword}, '%')
                 OR store_code LIKE CONCAT('%', #{keyword}, '%')
                 OR address LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ${orderBy}
                <if test="orderDirection != null and orderDirection != ''">
                    ${orderDirection}
                </if>
            </when>
            <otherwise>
                sort_order DESC, create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 获取门店统计信息 -->
    <select id="getStoreStatistics" parameterType="java.lang.Long" resultType="com.shop.cereshop.business.vo.store.StoreStatistics">
        SELECT
            COUNT(*) as totalStores,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as enabledStores,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as disabledStores,
            SUM(CASE WHEN status = 1 AND is_pickup_enabled = 1 THEN 1 ELSE 0 END) as pickupEnabledStores,
            SUM(CASE WHEN status = 1 AND is_verify_enabled = 1 THEN 1 ELSE 0 END) as verifyEnabledStores,
            (SELECT COUNT(*) FROM cere_verify_log vl
             JOIN cere_merchant_store ms ON vl.store_id = ms.store_id
             WHERE ms.merchant_id = #{merchantId,jdbcType=BIGINT}
             AND DATE(vl.verify_time) = CURDATE()) as todayVerifyOrders,
            (SELECT COUNT(*) FROM cere_verify_log vl
             JOIN cere_merchant_store ms ON vl.store_id = ms.store_id
             WHERE ms.merchant_id = #{merchantId,jdbcType=BIGINT}
             AND DATE_FORMAT(vl.verify_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')) as monthVerifyOrders,
            (SELECT COUNT(*) FROM cere_shop_order o
             WHERE o.shop_id = #{merchantId,jdbcType=BIGINT}
             AND o.delivery_type = 2
             AND DATE(o.create_time) = CURDATE()) as todayPickupOrders,
            (SELECT COUNT(*) FROM cere_shop_order o
             WHERE o.shop_id = #{merchantId,jdbcType=BIGINT}
             AND o.delivery_type = 2
             AND DATE_FORMAT(o.create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')) as monthPickupOrders
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
    </select>

    <!-- 根据商户ID查找默认门店 -->
    <select id="findDefaultByMerchantId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cere_merchant_store
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND is_default = 1
        AND status = 1
        LIMIT 1
    </select>

    <!-- 清除商户的默认门店设置 -->
    <update id="clearDefaultStore" parameterType="java.lang.Long">
        UPDATE cere_merchant_store
        SET is_default = 0,
            update_time = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND is_default = 1
    </update>

    <!-- 通过存储过程设置默认门店 -->
    <select id="setDefaultStoreByProcedure" statementType="CALLABLE">
        {CALL sp_set_default_store(#{storeId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT})}
    </select>

</mapper>
