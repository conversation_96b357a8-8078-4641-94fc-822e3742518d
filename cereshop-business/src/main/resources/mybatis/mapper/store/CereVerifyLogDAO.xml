<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.store.CereVerifyLogDAO">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.store.CereVerifyLog">
        <id column="log_id" jdbcType="BIGINT" property="logId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_sn" jdbcType="VARCHAR" property="orderSn"/>
        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>
        <result column="order_product_id" jdbcType="BIGINT" property="orderProductId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="verify_type" jdbcType="TINYINT" property="verifyType"/>
        <result column="verify_times" jdbcType="INTEGER" property="verifyTimes"/>
        <result column="remaining_times" jdbcType="INTEGER" property="remainingTimes"/>
        <result column="verify_code" jdbcType="VARCHAR" property="verifyCode"/>
        <result column="staff_id" jdbcType="BIGINT" property="staffId"/>
        <result column="staff_name" jdbcType="VARCHAR" property="staffName"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="verify_time" jdbcType="TIMESTAMP" property="verifyTime"/>
        <result column="service_date" jdbcType="DATE" property="serviceDate"/>
        <result column="service_duration" jdbcType="INTEGER" property="serviceDuration"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        log_id, order_id, order_sn, merchant_id, order_product_id, product_id, 
        product_name, sku_name, verify_type, verify_times, remaining_times, 
        verify_code, staff_id, staff_name, store_id, store_name, verify_time, 
        service_date, service_duration, remark, create_time
    </sql>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM cere_verify_log
        WHERE log_id = #{logId,jdbcType=BIGINT}
    </delete>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE log_id = #{logId,jdbcType=BIGINT}
    </select>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.store.CereVerifyLog">
        UPDATE cere_verify_log
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderSn != null">
                order_sn = #{orderSn,jdbcType=VARCHAR},
            </if>
            <if test="merchantId != null">
                merchant_id = #{merchantId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                order_product_id = #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                product_id = #{productId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="verifyType != null">
                verify_type = #{verifyType,jdbcType=TINYINT},
            </if>
            <if test="verifyTimes != null">
                verify_times = #{verifyTimes,jdbcType=INTEGER},
            </if>
            <if test="remainingTimes != null">
                remaining_times = #{remainingTimes,jdbcType=INTEGER},
            </if>
            <if test="verifyCode != null">
                verify_code = #{verifyCode,jdbcType=VARCHAR},
            </if>
            <if test="staffId != null">
                staff_id = #{staffId,jdbcType=BIGINT},
            </if>
            <if test="staffName != null">
                staff_name = #{staffName,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=BIGINT},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="verifyTime != null">
                verify_time = #{verifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="serviceDate != null">
                service_date = #{serviceDate,jdbcType=DATE},
            </if>
            <if test="serviceDuration != null">
                service_duration = #{serviceDuration,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE log_id = #{logId,jdbcType=BIGINT}
    </update>

    <!-- 根据订单ID查询核销记录 -->
    <select id="findByOrderId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE order_id = #{orderId,jdbcType=BIGINT}
        ORDER BY verify_time DESC
    </select>

    <!-- 根据订单号查询核销记录 -->
    <select id="findByOrderSn" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE order_sn = #{orderSn,jdbcType=VARCHAR}
        ORDER BY verify_time DESC
    </select>

    <!-- 根据核销码查询核销记录 -->
    <select id="findByVerifyCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE verify_code = #{verifyCode,jdbcType=VARCHAR}
        ORDER BY verify_time DESC
    </select>

    <!-- 根据商户ID查询核销记录 -->
    <select id="findByMerchantId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        <if test="startTime != null">
            AND verify_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND verify_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY verify_time DESC
    </select>

    <!-- 根据门店ID查询核销记录 -->
    <select id="findByStoreId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE store_id = #{storeId,jdbcType=BIGINT}
        <if test="startTime != null">
            AND verify_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND verify_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY verify_time DESC
    </select>

    <!-- 根据员工ID查询核销记录 -->
    <select id="findByStaffId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE staff_id = #{staffId,jdbcType=BIGINT}
        <if test="startTime != null">
            AND verify_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND verify_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY verify_time DESC
    </select>

    <!-- 根据订单商品ID查询核销记录 -->
    <select id="findByOrderProductId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE order_product_id = #{orderProductId,jdbcType=BIGINT}
        ORDER BY verify_time DESC
    </select>

    <!-- 统计商户核销数量 -->
    <select id="countByMerchantId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_verify_log
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        <if test="startTime != null">
            AND verify_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND verify_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <!-- 统计门店核销数量 -->
    <select id="countByStoreId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_verify_log
        WHERE store_id = #{storeId,jdbcType=BIGINT}
        <if test="startTime != null">
            AND verify_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND verify_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <!-- 统计员工核销数量 -->
    <select id="countByStaffId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_verify_log
        WHERE staff_id = #{staffId,jdbcType=BIGINT}
        <if test="startTime != null">
            AND verify_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND verify_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <!-- 根据核销类型统计 -->
    <select id="countByVerifyType" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM cere_verify_log
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        AND verify_type = #{verifyType,jdbcType=TINYINT}
        <if test="startTime != null">
            AND verify_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND verify_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
    </select>

    <!-- 查询最近的核销记录 -->
    <select id="findRecentLogs" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cere_verify_log
        WHERE merchant_id = #{merchantId,jdbcType=BIGINT}
        ORDER BY verify_time DESC
        LIMIT #{limit,jdbcType=INTEGER}
    </select>

</mapper>
