<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.after.CereOrderAfterDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    <id column="after_id" jdbcType="BIGINT" property="afterId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="after_formid" jdbcType="VARCHAR" property="afterFormid" />
    <result column="after_state" jdbcType="BIT" property="afterState" />
    <result column="after_type" jdbcType="BIT" property="afterType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="explain" jdbcType="VARCHAR" property="explain" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    after_id, order_id, after_formid, after_state, after_type, price,`explain`, remark, reason,
    image, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_order_after
    where after_id = #{afterId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_order_after
    where after_id = #{afterId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="after_id" keyProperty="afterId" parameterType="com.shop.cereshop.commons.domain.after.CereOrderAfter" useGeneratedKeys="true">
    insert into cere_order_after
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="afterFormid != null and afterFormid!=''">
        after_formid,
      </if>
      <if test="afterState != null">
        after_state,
      </if>
      <if test="afterType != null">
        after_type,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="explain != null and explain!=''">
        `explain`,
      </if>
      <if test="remark != null and remark!=''">
        remark,
      </if>
      <if test="reason != null and reason!=''">
        reason,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="afterFormid != null and afterFormid!=''">
        #{afterFormid,jdbcType=VARCHAR},
      </if>
      <if test="afterState != null">
        #{afterState,jdbcType=BIT},
      </if>
      <if test="afterType != null">
        #{afterType,jdbcType=BIT},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="explain != null and explain!=''">
        #{explain,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reason != null and reason!=''">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    update cere_order_after
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="afterFormid != null and afterFormid!=''">
        after_formid = #{afterFormid,jdbcType=VARCHAR},
      </if>
      <if test="afterState != null">
        after_state = #{afterState,jdbcType=BIT},
      </if>
      <if test="afterType != null">
        after_type = #{afterType,jdbcType=BIT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="explain != null and explain!=''">
        `explain` = #{explain,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reason != null and reason!=''">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where after_id = #{afterId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    update cere_order_after
    set order_id = #{orderId,jdbcType=BIGINT},
      after_formid = #{afterFormid,jdbcType=VARCHAR},
      after_state = #{afterState,jdbcType=BIT},
      after_type = #{afterType,jdbcType=BIT},
      price = #{price,jdbcType=DECIMAL},
      `explain` = #{explain,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      image = #{image,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where after_id = #{afterId,jdbcType=BIGINT}
  </update>

  <select id="getAll" parameterType="com.shop.cereshop.business.param.after.AfterGetAllParam" resultType="com.shop.cereshop.business.page.after.After">
    SELECT a.after_id,a.after_formid,c.order_formid,a.price,a.after_state,a.order_id,
    GROUP_CONCAT(b.product_name SEPARATOR ';') afterProductNames,
    COUNT(b.product_id) number,
    c.payment_time,a.price refundMoney,
    a.update_time lastAfterTime,
    c.state orderState,
    a.remark,
    c.customer_name ,
    a.after_type,a.create_time,IF(a.after_state=1,'审核中',
    IF(a.after_state=2,'退款中',IF(a.after_state=3,'退货中',IF(a.after_state=4,'退款成功',
    IF(a.after_state=5,'退款失败',IF(a.after_state=6,'审核不通过',IF(
    a.after_state=7,'评审中',IF(a.after_state=8,'退货完成,拒绝退款',
    IF(a.after_state=9,'已关闭','审核通过'))))))))) after_state_name FROM cere_order_after a
    LEFT JOIN cere_after_product b ON a.after_id=b.after_id
    LEFT JOIN cere_shop_order c ON a.order_id=c.order_id
    LEFT JOIN cere_platform_shop d ON c.shop_id=d.shop_id
    where c.shop_id=#{shopId} and a.after_state<![CDATA[!= ]]>7
    <if test='searchType=="1" and search!=null and search!=""'>
      and a.after_id like concat('%',#{search},'%')
    </if>
    <if test='searchType=="2" and search!=null and search!=""'>
      and c.customer_name like concat('%',#{search},'%')
    </if>
    <if test='searchType=="3" and search!=null and search!=""'>
      and c.receive_name like concat('%',#{search},'%')
    </if>
    <if test='searchType=="4" and search!=null and search!=""'>
      and c.receive_phone like concat('%',#{search},'%')
    </if>
    <if test='searchType=="5" and search!=null and search!=""'>
      and b.product_id like concat('%',#{search},'%')
    </if>
    <if test="state!=null">
      and c.state=#{state}
    </if>
    <if test="afterState!=null">
      and a.after_state=#{afterState}
    </if>
    <if test="afterStateList!=null and afterStateList.size()>0">
      and a.after_state in (
      <foreach collection="afterStateList" item="afterStateL" index="index" separator=",">
        #{afterStateL}
      </foreach>
      )
    </if>
    <if test="afterType!=null">
      and a.after_type=#{afterType}
    </if>
    <if test='type=="1"'>
      and a.after_state=1
    </if>
    <if test='type=="2"'>
      and a.after_state=3
    </if>
    GROUP BY a.after_id
    ORDER BY
    <choose>
      <when test='type=="2"'>
        a.create_time
      </when>
      <otherwise>
        a.create_time DESC
      </otherwise>
    </choose>
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.after.After">
    SELECT a.after_id,b.order_id,a.after_formid,b.order_formid,b.state,a.after_type,a.after_state,b.logistics_price,a.image,
    a.price,b.customer_name,a.create_time,a.update_time,a.remark,a.`explain`,d.dict_name express,c.deliver_formid,a.reason,
    IF(a.after_state=1,'审核中',
    IF(a.after_state=2,'退款中',IF(a.after_state=3,'退货中',IF(a.after_state=4,'退款成功',
    IF(a.after_state=5,'退款失败',IF(a.after_state=6,'审核不通过',IF(
    a.after_state=7,'评审中',IF(a.after_state=8,'退货完成,拒绝退款',
    IF(a.after_state=9,'已关闭','审核通过'))))))))) after_state_name from cere_order_after a
    LEFT JOIN cere_shop_order b ON a.order_id=b.order_id
    LEFT JOIN cere_after_dilever c ON a.after_id=c.after_id
    LEFT JOIN cere_platform_dict d ON c.express=d.dict_id
    where a.after_id=#{afterId}
    GROUP BY a.after_id
  </select>

  <select id="findHistories" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.after.AfterHistory">
    SELECT operation_describtion title,a.create_time time,
    IF(a.operation='客户端操作',IF(d.wechat_name IS NULL,d.`name`,d.wechat_name),
    IF(a.operation='商户端操作',c.`name`,b.`name`)) `name`,e.reason,e.image
    from cere_platform_log a
    LEFT JOIN cere_platform_user b ON a.platform_user_id=b.platform_user_id
    LEFT JOIN cere_platform_business c ON a.platform_user_id=c.business_user_id
    LEFT JOIN cere_buyer_user d ON a.platform_user_id=d.buyer_user_id
    LEFT JOIN (SELECT b.after_id,a.reason,a.image from cere_platform_after a,cere_order_after b
    where a.order_id=b.order_id and b.after_id=#{afterId}) e ON a.only=e.after_id and a.operation_describtion='申请平台介入'
    where a.module='售后管理' and
    only=#{afterId}
    ORDER BY a.create_time DESC
  </select>

  <select id="findProducts" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.order.Product">
    SELECT after_product_id,product_id,a.sku_id,product_name,b.`value`,
    product_price,number,image,product_price*number total,a.SKU from cere_after_product a
    LEFT JOIN (SELECT GROUP_CONCAT(sku_value) `value`,sku_id from cere_sku_name GROUP BY sku_id) b ON a.sku_id=b.sku_id
    where after_id=#{afterId}
  </select>

  <select id="findSkuAttribute" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.order.SkuDetail">
    SELECT sku_name,sku_value FROM cere_after_product_attribute where after_product_id=#{afterProductId}
  </select>

  <select id="findOtherProductsByAfterId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.order.CereOrderProduct">
    SELECT product_id FROM cere_order_product where product_id NOT in (
    SELECT a.product_id FROM cere_after_product a
    LEFT JOIN cere_order_after b ON a.after_id=b.after_id and b.after_state=4
    where b.order_id=#{orderId} OR a.after_id=#{afterId})
    and order_id=#{orderId}
  </select>

  <select id="findAfterSuccessProduct" parameterType="com.shop.cereshop.business.param.after.AfterIdParam" resultType="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    SELECT a.product_id from cere_after_product a
    LEFT JOIN cere_order_after b ON a.after_id=b.after_id
    where a.product_id NOT in (SELECT product_id from cere_after_product where after_id=#{afterId})
    and b.order_id=#{orderId} and b.after_state=2
  </select>

  <select id="findOrderPay" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.after.Refund">
    SELECT a.price,d.price orderPrice,c.transaction_id,c.out_refund_no,c.after,c.id,
    c.order_formid,c.out_trade_no from cere_order_after a
    LEFT JOIN cere_shop_order b ON a.order_id=b.order_id
    LEFT JOIN cere_pay_log c ON b.order_formid=c.order_formid and c.state = '支付'
	LEFT JOIN cere_order_parent d ON b.parent_id=d.parent_id
    where a.after_id=#{afterId}
    limit 1
  </select>

  <select id="findAfterSkus" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.product.CereProductSku">
    SELECT b.sku_id,a.number stock_number from cere_after_product a
    LEFT JOIN cere_product_sku b ON a.sku_id=b.sku_id
    where after_id=#{afterId}
  </select>

  <select id="findById" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    SELECT * FROM cere_order_after where after_id=#{afterId}
  </select>

  <select id="findByOrderId" parameterType="java.lang.Object" resultType="java.lang.Long">
    SELECT after_id FROM cere_order_after where order_id=#{orderId} LIMIT 1
  </select>
</mapper>
