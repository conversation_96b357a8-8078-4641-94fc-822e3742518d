<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.channel.ShopChannelActivityCouponDAO">

    <select id="selectChannelCouponByActivityId"
            resultType="com.shop.cereshop.commons.domain.tool.CereShopCoupon">
        select t2.* from cere_shop_channel_activity_coupon t1 join cere_shop_coupon t2
        on t2.shop_coupon_id = t1.coupon_id
        where t1.shop_id = #{shopId} and t2.shop_id = #{shopId}
        and t1.id = #{channelActivityId}
        order by t1.update_time
    </select>

</mapper>
