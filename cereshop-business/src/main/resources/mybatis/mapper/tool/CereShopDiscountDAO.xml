<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.tool.CereShopDiscountDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereShopDiscount">
        <id column="shop_discount_id" jdbcType="BIGINT" property="shopDiscountId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="discount_name" jdbcType="VARCHAR" property="discountName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="if_limit" jdbcType="BIT" property="ifLimit"/>
        <result column="limit_number" jdbcType="INTEGER" property="limitNumber"/>
        <result column="if_number" jdbcType="BIT" property="ifNumber"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="if_enable" jdbcType="BIT" property="ifEnable"/>
        <result column="enable_time" jdbcType="INTEGER" property="enableTime"/>
        <result column="if_add" jdbcType="BIT" property="ifAdd"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        shop_discount_id
        , shop_id, discount_name, remark, start_time, end_time, if_limit,
    limit_number,if_number,number, if_enable, enable_time, if_add, `state`, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cere_shop_discount
        where shop_discount_id = #{shopDiscountId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cere_shop_discount
        where shop_discount_id = #{shopDiscountId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="shop_discount_id" keyProperty="shopDiscountId"
            parameterType="com.shop.cereshop.commons.domain.tool.CereShopDiscount" useGeneratedKeys="true">
        insert into cere_shop_discount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">
                shop_id,
            </if>
            <if test="discountName != null">
                discount_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="ifLimit != null">
                if_limit,
            </if>
            <if test="limitNumber != null">
                limit_number,
            </if>
            <if test="ifNumber != null">
                if_number,
            </if>
            <if test="number != null">
                number,
            </if>
            <if test="ifEnable != null">
                if_enable,
            </if>
            <if test="enableTime != null">
                enable_time,
            </if>
            <if test="ifAdd != null">
                if_add,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">
                #{shopId,jdbcType=BIGINT},
            </if>
            <if test="discountName != null">
                #{discountName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="ifLimit != null">
                #{ifLimit,jdbcType=BIT},
            </if>
            <if test="limitNumber != null">
                #{limitNumber,jdbcType=INTEGER},
            </if>
            <if test="ifNumber != null">
                #{ifNumber,jdbcType=BIT},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="ifEnable != null">
                #{ifEnable,jdbcType=BIT},
            </if>
            <if test="enableTime != null">
                #{enableTime,jdbcType=INTEGER},
            </if>
            <if test="ifAdd != null">
                #{ifAdd,jdbcType=BIT},
            </if>
            <if test="state != null">
                #{state,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.tool.CereShopDiscount">
        update cere_shop_discount
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="discountName != null">
                discount_name = #{discountName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="ifLimit != null">
                if_limit = #{ifLimit,jdbcType=BIT},
            </if>
            <if test="limitNumber != null">
                limit_number = #{limitNumber,jdbcType=INTEGER},
            </if>
            <if test="ifNumber != null">
                if_number = #{ifNumber,jdbcType=BIT},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="ifEnable != null">
                if_enable = #{ifEnable,jdbcType=BIT},
            </if>
            <if test="enableTime != null">
                enable_time = #{enableTime,jdbcType=INTEGER},
            </if>
            <if test="ifAdd != null">
                if_add = #{ifAdd,jdbcType=BIT},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where shop_discount_id = #{shopDiscountId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.tool.CereShopDiscount">
        update cere_shop_discount
        set shop_id       = #{shopId,jdbcType=BIGINT},
            discount_name = #{discountName,jdbcType=VARCHAR},
            remark        = #{remark,jdbcType=VARCHAR},
            start_time    = #{startTime,jdbcType=VARCHAR},
            end_time      = #{endTime,jdbcType=VARCHAR},
            if_limit      = #{ifLimit,jdbcType=BIT},
            limit_number  = #{limitNumber,jdbcType=INTEGER},
            if_number     = #{ifNumber,jdbcType=BIT},
            number        = #{number,jdbcType=INTEGER},
            if_enable     = #{ifEnable,jdbcType=BIT},
            enable_time   = #{enableTime,jdbcType=INTEGER},
            if_add        = #{ifAdd,jdbcType=BIT},
            `state`       = #{state,jdbcType=BIT},
            create_time   = #{createTime,jdbcType=VARCHAR},
            update_time   = #{updateTime,jdbcType=VARCHAR}
        where shop_discount_id = #{shopDiscountId,jdbcType=BIGINT}
    </update>

    <update id="updateState" parameterType="com.shop.cereshop.commons.domain.tool.CereShopDiscount">
        UPDATE cere_shop_discount
        SET `state` = #{state}
        where shop_discount_id = #{shopDiscountId}
    </update>

    <select id="getById" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.tool.ShopDiscountDetail">
        SELECT shop_discount_id,
               shop_id,
               discount_name,
               remark,
               start_time,
               end_time,
               if_limit,
               limit_number,
               if_enable,
               enable_time,
               if_add,
               `state`,
               if_add,
               if_number,
               number
        FROM cere_shop_discount
        WHERE shop_discount_id = #{shopDiscountId}
    </select>

    <select id="getDiscounts" parameterType="com.shop.cereshop.business.param.renovation.RenovationParam"
            resultType="com.shop.cereshop.business.page.tool.ShopDiscountDetail">
        SELECT shop_discount_id, shop_id, discount_name, remark, start_time, end_time, if_limit,
        limit_number, if_enable, enable_time, if_add, `state`,if_add,if_number,number FROM cere_shop_discount
        WHERE shop_id=#{shopId} and state in (0,1)
        <if test="ids!=null and ids.size()>0">
            and shop_discount_id in (
            <foreach collection="ids" item="id" index="index" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </select>

    <select id="getAll" parameterType="com.shop.cereshop.business.param.tool.ShopDiscountGetAllParam"
            resultType="com.shop.cereshop.business.page.tool.ShopDiscount">
        SELECT a.shop_discount_id,a.discount_name,CONCAT('无门槛',b.discount,'折') content,a.start_time,a.end_time,a.state
        FROM cere_shop_discount a
        LEFT JOIN (SELECT MIN(discount) discount,shop_discount_id from cere_shop_discount_detail GROUP BY
        shop_discount_id) b ON a.shop_discount_id=b.shop_discount_id
        where a.shop_id=#{shopId}
        <if test="discountName!=null and discountName!=''">
            and a.discount_name like concat('%',#{discountName},'%')
        </if>
        <if test="state!=null">
            and a.state=#{state}
        </if>
        ORDER BY a.update_time DESC,a.create_time DESC
    </select>

    <select id="getProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.tool.ToolProduct">
        SELECT a.product_id,
               b.product_name,
               a.original_price,
               a.price,
        <if test="shopDiscountId != null">
            dd.discount,
        </if>
               c.product_image image,
               a.stock_number,
               a.sku_id,
               e.`value`
        from cere_product_sku a
        <if test="shopDiscountId != null">
            join cere_shop_discount_detail dd on dd.sku_id = a.sku_id
            and dd.shop_discount_id = #{shopDiscountId}
        </if>
                 LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) c
                           ON b.product_id = c.product_id
                 LEFT JOIN cere_product_classify d ON b.classify_id = d.classify_id
                 LEFT JOIN (SELECT GROUP_CONCAT(sku_value) `value`, sku_id FROM cere_sku_name GROUP BY sku_id) e
                           ON a.sku_id = e.sku_id
        where b.shop_id = #{shopId}
          and b.shelve_state = 1
          and a.product_id NOT in (SELECT a.product_id
                                   FROM cere_shop_group_work_detail a
                                            LEFT JOIN cere_shop_group_work b ON a.shop_group_work_id = b.shop_group_work_id
                                   where b.shop_id = #{shopId}
                                     and b.state=1)
          and a.product_id NOT in (SELECT a.product_id
                                   FROM cere_shop_seckill_detail a
                                            LEFT JOIN cere_shop_seckill b ON a.shop_seckill_id = b.shop_seckill_id
                                   where b.shop_id = #{shopId}
                                     and b.state=1)
          and a.product_id NOT in (SELECT a.product_id
                                   FROM cere_sign_product a
                                            LEFT JOIN cere_activity_sign b ON a.sign_id = b.sign_id
                                            LEFT JOIN cere_platform_seckill c
                                                      ON b.activity_id = c.seckill_id and b.sign_type = 2
                                   where b.shop_id = #{shopId}
                                     and c.state=3)
          and a.product_id NOT in (SELECT a.product_id
                                   FROM cere_sign_product a
                                            LEFT JOIN cere_activity_sign b ON a.sign_id = b.sign_id
                                            LEFT JOIN cere_platform_discount c
                                                      ON b.activity_id = c.discount_id and b.sign_type = 3
                                   where b.shop_id = #{shopId}
                                     and c.state=3)
    </select>

    <select id="findDataProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.tool.CouponProduct">
        SELECT a.product_name, SUM(a.number) number, b.users
        from cere_order_product a
                 LEFT JOIN (SELECT COUNT(a.buyer_user_id) users, a.product_id
                            FROM (SELECT b.buyer_user_id, a.product_id
                                  FROM cere_order_product a,
                                       cere_shop_order b
                                  where a.order_id = b.order_id
                                    and b.state in (2, 3, 4)
                                  GROUP BY a.product_id, b.buyer_user_id) a
                            GROUP BY a.product_id) b ON a.product_id = b.product_id
        where order_id in (SELECT order_id
                           from cere_shop_order
                           where shop_id = #{shopId}
                             and shop_discount_id = #{shopDiscountId}
                             and state in (2, 3, 4))
    </select>

    <select id="findDiscountName" parameterType="java.lang.Object" resultType="java.lang.String">
        SELECT discount_name
        from cere_shop_discount
        where shop_discount_id = #{shopDiscountId}
    </select>

    <select id="findPays" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM cere_shop_order
        where shop_id = #{shopId}
          and shop_discount_id = #{shopDiscountId}
          and state in (2, 3, 4)
    </select>

    <select id="findTotal" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(price) IS NULL, 0, SUM(price))
        FROM cere_shop_order
        where shop_id = #{shopId}
          and shop_discount_id = #{shopDiscountId}
          and state in (2, 3, 4)
    </select>

    <select id="checkTime" parameterType="com.shop.cereshop.commons.domain.tool.CereShopDiscount"
            resultType="com.shop.cereshop.commons.domain.tool.CereShopDiscount">
        SELECT *
        FROM cere_shop_discount
        where shop_id = #{shopId}
          and ((if(if_enable = 2, date_sub(start_time, interval enable_time hour), start_time) &lt;= #{startTime} and end_time &gt;= #{startTime}) OR
               (if(if_enable = 2, date_sub(start_time, interval enable_time hour), start_time) &lt;= #{endTime} and end_time &gt;= #{endTime}))
          and state <![CDATA[ != ]]> 2
    </select>

    <select id="checkUpdateTime" parameterType="com.shop.cereshop.commons.domain.tool.CereShopDiscount"
            resultType="com.shop.cereshop.commons.domain.tool.CereShopDiscount">
        SELECT *
        FROM cere_shop_discount
        where shop_id = #{shopId}
          and ((if(if_enable = 2, date_sub(start_time, interval enable_time hour), start_time) &lt;= #{startTime} and end_time &gt;= #{startTime})
            OR (if(if_enable = 2, date_sub(start_time, interval enable_time hour), start_time) &lt;= #{endTime} and end_time &gt;= #{endTime}))
          and state<![CDATA[!= ]]>2 and shop_discount_id<![CDATA[!= ]]>#{shopDiscountId}
    </select>

    <select id="checkOtherSeckill" parameterType="java.util.List" resultType="java.lang.Long">
        SELECT a.product_id FROM cere_shop_seckill_detail a
        LEFT JOIN cere_shop_seckill b ON a.shop_seckill_id=b.shop_seckill_id
        where b.shop_id=#{shopId}
        and ((if(b.if_enable = 2, date_sub(b.effective_start, interval b.enable_time hour), b.effective_start) &lt;= #{startTime} and b.effective_end &gt;= #{startTime})
        OR (if(b.if_enable = 2, date_sub(b.effective_start, interval b.enable_time hour), b.effective_start) &lt;= #{endTime} and b.effective_end &gt;= #{endTime}))
        and a.product_id in (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="checkOtherWork" parameterType="java.util.List" resultType="java.lang.Long">
        SELECT a.product_id FROM cere_shop_group_work_detail a
        LEFT JOIN cere_shop_group_work b ON a.shop_group_work_id=b.shop_group_work_id
        where b.shop_id=#{shopId}
        and ((if(b.if_enable = 2, date_sub(b.start_time, interval b.enable_time hour), b.start_time) &lt;= #{startTime} and b.end_time &gt;= #{startTime})
        OR (if(b.if_enable = 2, date_sub(b.start_time, interval b.enable_time hour), b.start_time) &lt;= #{endTime} and b.end_time &gt;= #{endTime}))
        and a.product_id in (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="findVisit" parameterType="java.lang.Object" resultType="int">
        SELECT IFNULL(COUNT(*), 0)
        from cere_buyer_discount_visit
        where shop_discount_id = #{shopDiscountId}
    </select>

    <select id="findShopDiscount" resultType="com.shop.cereshop.commons.domain.tool.CereShopDiscount">
        SELECT *
        FROM cere_shop_discount
        where end_time &lt; NOW()
          and state<![CDATA[!= ]]>2
    </select>

    <update id="updateDiscountEndState" parameterType="java.lang.Object">
        update cere_shop_discount SET state=2,update_time=#{time} where shop_discount_id in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item.shopDiscountId}
        </foreach>
        )
    </update>
</mapper>
