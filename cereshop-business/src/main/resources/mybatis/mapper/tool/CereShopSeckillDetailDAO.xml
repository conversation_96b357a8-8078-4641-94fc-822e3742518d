<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.tool.CereShopSeckillDetailDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereShopSeckillDetail">
        <result column="shop_seckill_id" jdbcType="BIGINT" property="shopSeckillId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="down_price" jdbcType="DECIMAL" property="downPrice"/>
        <result column="seckill_price" jdbcType="DECIMAL" property="seckillPrice"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="total" jdbcType="INTEGER" property="total"/>
    </resultMap>
    <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.tool.CereShopSeckillDetail">
        insert into cere_shop_seckill_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopSeckillId != null">
                shop_seckill_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="skuId != null">
                sku_id,
            </if>
            <if test="downPrice != null">
                down_price,
            </if>
            <if test="seckillPrice != null">
                seckill_price,
            </if>
            <if test="number != null">
                number,
            </if>
            <if test="total != null">
                total,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopSeckillId != null">
                #{shopSeckillId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=BIGINT},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=BIGINT},
            </if>
            <if test="downPrice != null">
                #{downPrice,jdbcType=DECIMAL},
            </if>
            <if test="seckillPrice != null">
                #{seckillPrice,jdbcType=DECIMAL},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="total != null">
                #{total,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into cere_shop_seckill_detail (shop_seckill_id, product_id,sku_id, down_price,
        seckill_price,number,total) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.shopSeckillId},
            #{item.productId},
            #{item.skuId},
            #{item.downPrice},
            #{item.seckillPrice},
            #{item.number},
            #{item.total}
            )
        </foreach>
    </insert>

    <delete id="deleteByShopSeckillId" parameterType="java.lang.Object">
        DELETE
        FROM cere_shop_seckill_detail
        where shop_seckill_id = #{shopSeckillId}
    </delete>

    <select id="findProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.tool.ToolProduct">
        SELECT b.product_id,
               b.product_name,
               c.original_price,
               e.product_image image,
               c.stock_number,
               a.down_price,
               a.seckill_price,
               a.sku_id
        from cere_shop_seckill_detail a
                 LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
                 LEFT JOIN cere_product_sku c ON a.sku_id = c.sku_id
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) e
                           ON b.product_id = e.product_id
                 LEFT JOIN cere_product_classify f ON b.classify_id = f.classify_id
        where a.shop_seckill_id = #{shopSeckillId}
    </select>

    <select id="findDistinctProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.tool.ToolProduct">
        SELECT b.product_id,
               b.product_name,
               c.original_price,
               e.product_image image,
               c.stock_number,
               a.seckill_price price,
               a.sku_id,
               c.total
        from cere_shop_seckill_detail a
                 LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
                 LEFT JOIN cere_product_sku c ON a.sku_id = c.sku_id
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) e
                           ON b.product_id = e.product_id
                 LEFT JOIN cere_product_classify f ON b.classify_id = f.classify_id
        where a.shop_seckill_id = #{shopSeckillId}
        GROUP BY a.product_id
    </select>

    <select id="findBySkuId" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.tool.CereShopSeckillDetail">
        SELECT a.*
        FROM cere_shop_seckill_detail a
                 LEFT JOIN cere_shop_seckill b ON a.shop_seckill_id = b.shop_seckill_id
        where b.shop_id = #{shopId}
          and b.state in (0, 1)
          and a.sku_id = #{skuId}
    </select>

    <update id="updateBatchSeckillPrice" parameterType="java.util.List">
        update cere_shop_seckill_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="seckill_price =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.seckillPrice!=null">
                        when sku_id=#{i.skuId} and shop_seckill_id=#{i.shopSeckillId} then #{i.seckillPrice}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="i" index="index">
            sku_id=#{i.skuId} and shop_seckill_id=#{i.shopSeckillId}
        </foreach>
    </update>

    <select id="findNumberDetails" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.tool.CereShopSeckillDetail">
        SELECT c.shop_seckill_id, (c.number + a.number) number, c.sku_id
        FROM cere_order_product a
                 LEFT JOIN cere_shop_order b ON a.order_id = b.order_id
                 LEFT JOIN cere_shop_seckill_detail c ON b.shop_seckill_id = c.shop_seckill_id and a.sku_id = c.sku_id
        where a.order_id = #{orderId}
          and c.shop_seckill_id = #{shopSeckillId}
    </select>
    <select id="findProductIdList" resultType="java.lang.Long">
        select product_id
        from cere_shop_seckill_detail
        where shop_seckill_id = #{shopSeckillId}
    </select>

    <select id="findDetailList" resultMap="BaseResultMap">
        select shop_seckill_id, product_id, sku_id, down_price, seckill_price, number, total
        from cere_shop_seckill_detail where shop_seckill_id = #{shopSeckillId}
    </select>

    <update id="updateBatch" parameterType="java.util.List">
        update cere_shop_seckill_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="number =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.number!=null">
                        when sku_id=#{i.skuId} and shop_seckill_id=#{i.shopSeckillId} then #{i.number}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="i" index="index" >
            (sku_id=#{i.skuId} and shop_seckill_id=#{i.shopSeckillId})
        </foreach>
    </update>
</mapper>
