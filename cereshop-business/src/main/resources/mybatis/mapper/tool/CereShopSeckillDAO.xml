<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.tool.CereShopSeckillDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereShopSeckill">
        <id column="shop_seckill_id" jdbcType="BIGINT" property="shopSeckillId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="seckill_name" jdbcType="VARCHAR" property="seckillName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="effective_start" jdbcType="VARCHAR" property="effectiveStart"/>
        <result column="effective_end" jdbcType="VARCHAR" property="effectiveEnd"/>
        <result column="if_limit" jdbcType="BIT" property="ifLimit"/>
        <result column="limit_number" jdbcType="INTEGER" property="limitNumber"/>
        <result column="if_number" jdbcType="BIT" property="ifNumber"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="if_enable" jdbcType="BIT" property="ifEnable"/>
        <result column="enable_time" jdbcType="INTEGER" property="enableTime"/>
        <result column="if_add" jdbcType="BIT" property="ifAdd"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        shop_seckill_id
        , shop_id, seckill_name, remark, effective_start, effective_end, if_limit,
    limit_number,if_number,number, if_enable, enable_time, if_add, `state`, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cere_shop_seckill
        where shop_seckill_id = #{shopSeckillId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cere_shop_seckill
        where shop_seckill_id = #{shopSeckillId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="shop_seckill_id" keyProperty="shopSeckillId"
            parameterType="com.shop.cereshop.commons.domain.tool.CereShopSeckill" useGeneratedKeys="true">
        insert into cere_shop_seckill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">
                shop_id,
            </if>
            <if test="seckillName != null">
                seckill_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="effectiveStart != null">
                effective_start,
            </if>
            <if test="effectiveEnd != null">
                effective_end,
            </if>
            <if test="ifLimit != null">
                if_limit,
            </if>
            <if test="limitNumber != null">
                limit_number,
            </if>
            <if test="ifNumber != null">
                if_number,
            </if>
            <if test="number != null">
                number,
            </if>
            <if test="ifEnable != null">
                if_enable,
            </if>
            <if test="enableTime != null">
                enable_time,
            </if>
            <if test="ifAdd != null">
                if_add,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">
                #{shopId,jdbcType=BIGINT},
            </if>
            <if test="seckillName != null">
                #{seckillName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="effectiveStart != null">
                #{effectiveStart,jdbcType=VARCHAR},
            </if>
            <if test="effectiveEnd != null">
                #{effectiveEnd,jdbcType=VARCHAR},
            </if>
            <if test="ifLimit != null">
                #{ifLimit,jdbcType=BIT},
            </if>
            <if test="limitNumber != null">
                #{limitNumber,jdbcType=INTEGER},
            </if>
            <if test="ifNumber != null">
                #{ifNumber,jdbcType=BIT},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="ifEnable != null">
                #{ifEnable,jdbcType=BIT},
            </if>
            <if test="enableTime != null">
                #{enableTime,jdbcType=INTEGER},
            </if>
            <if test="ifAdd != null">
                #{ifAdd,jdbcType=BIT},
            </if>
            <if test="state != null">
                #{state,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.tool.CereShopSeckill">
        update cere_shop_seckill
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="seckillName != null">
                seckill_name = #{seckillName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="effectiveStart != null">
                effective_start = #{effectiveStart,jdbcType=VARCHAR},
            </if>
            <if test="effectiveEnd != null">
                effective_end = #{effectiveEnd,jdbcType=VARCHAR},
            </if>
            <if test="ifLimit != null">
                if_limit = #{ifLimit,jdbcType=BIT},
            </if>
            <if test="limitNumber != null">
                limit_number = #{limitNumber,jdbcType=INTEGER},
            </if>
            <if test="ifNumber != null">
                if_number = #{ifNumber,jdbcType=BIT},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="ifEnable != null">
                if_enable = #{ifEnable,jdbcType=BIT},
            </if>
            <if test="enableTime != null">
                enable_time = #{enableTime,jdbcType=INTEGER},
            </if>
            <if test="ifAdd != null">
                if_add = #{ifAdd,jdbcType=BIT},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where shop_seckill_id = #{shopSeckillId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.tool.CereShopSeckill">
        update cere_shop_seckill
        set shop_id         = #{shopId,jdbcType=BIGINT},
            seckill_name    = #{seckillName,jdbcType=VARCHAR},
            remark          = #{remark,jdbcType=VARCHAR},
            effective_start = #{effectiveStart,jdbcType=VARCHAR},
            effective_end   = #{effectiveEnd,jdbcType=VARCHAR},
            if_limit        = #{ifLimit,jdbcType=BIT},
            limit_number    = #{limitNumber,jdbcType=INTEGER},
            if_number       = #{ifNumber,jdbcType=BIT},
            number          = #{number,jdbcType=INTEGER},
            if_enable       = #{ifEnable,jdbcType=BIT},
            enable_time     = #{enableTime,jdbcType=INTEGER},
            if_add          = #{ifAdd,jdbcType=BIT},
            `state`         = #{state,jdbcType=BIT},
            create_time     = #{createTime,jdbcType=VARCHAR},
            update_time     = #{updateTime,jdbcType=VARCHAR}
        where shop_seckill_id = #{shopSeckillId,jdbcType=BIGINT}
    </update>

    <update id="updateState" parameterType="com.shop.cereshop.commons.domain.tool.CereShopSeckill">
        UPDATE cere_shop_seckill
        SET `state` = #{state}
        where shop_seckill_id = #{shopSeckillId}
    </update>

    <select id="getById" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.tool.ShopSeckillDetail">
        SELECT shop_seckill_id,
               shop_id,
               seckill_name,
               remark,
               effective_start,
               effective_end,
               if_limit,
               limit_number,
               if_enable,
               enable_time,
               if_add,
               `state`,
               if_number,
               number
        FROM cere_shop_seckill
        where shop_seckill_id = #{shopSeckillId}
    </select>

    <select id="getSeckills" parameterType="com.shop.cereshop.business.param.renovation.RenovationParam"
            resultType="com.shop.cereshop.business.page.tool.ShopSeckillDetail">
        SELECT shop_seckill_id, shop_id, seckill_name, remark, effective_start, effective_end, if_limit,
        limit_number, if_enable, enable_time, if_add, `state`,if_number,number FROM cere_shop_seckill
        where shop_id=#{shopId} and state in (0,1)
        <if test="ids!=null and ids.size()>0">
            and shop_seckill_id in (
            <foreach collection="ids" item="id" index="index" separator=",">
                #{id}
            </foreach>
            )
        </if>
    </select>

    <select id="getAll" parameterType="com.shop.cereshop.business.param.tool.ShopSeckillGetAllParam"
            resultType="com.shop.cereshop.business.page.tool.ShopSeckill">
        SELECT a.shop_seckill_id,a.seckill_name,a.state,b.down_price,b.seckill_price,a.effective_start,a.effective_end
        FROM cere_shop_seckill a
        LEFT JOIN (SELECT shop_seckill_id,MAX(down_price) down_price,seckill_price from cere_shop_seckill_detail GROUP
        BY shop_seckill_id) b
        ON a.shop_seckill_id=b.shop_seckill_id
        where a.shop_id=#{shopId}
        <if test="seckillName!=null and seckillName!=''">
            and a.seckill_name like concat('%',#{seckillName},'%')
        </if>
        <if test="state!=null">
            and a.state=#{state}
        </if>
        ORDER BY a.update_time DESC,a.create_time DESC
    </select>

    <select id="getProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.tool.ToolProduct">
        SELECT a.product_id,
               b.product_name,
               a.original_price,
               a.price,
        <if test="shopSeckillId != null">
            sd.down_price as downPrice,
        </if>
               c.product_image image,
               a.stock_number,
               a.sku_id,
               e.`value`
        from cere_product_sku a
        <if test="shopSeckillId != null">
            join cere_shop_seckill_detail sd on sd.sku_id = a.sku_id
            and sd.shop_seckill_id = #{shopSeckillId}
        </if>
                 LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) c
                           ON b.product_id = c.product_id
                 LEFT JOIN cere_product_classify d ON b.classify_id = d.classify_id
                 LEFT JOIN (SELECT GROUP_CONCAT(sku_value) `value`, sku_id FROM cere_sku_name GROUP BY sku_id) e
                           ON a.sku_id = e.sku_id
        where b.shop_id = #{shopId}
          and b.shelve_state = 1
          and a.product_id NOT in (SELECT a.product_id
                                   FROM cere_shop_group_work_detail a
                                            LEFT JOIN cere_shop_group_work b ON a.shop_group_work_id = b.shop_group_work_id
                                   where b.shop_id = #{shopId}
                                     and b.state=1)
          and a.product_id NOT in (SELECT a.product_id
                                   FROM cere_shop_discount_detail a
                                            LEFT JOIN cere_shop_discount b ON a.shop_discount_id = b.shop_discount_id
                                   where b.shop_id = #{shopId}
                                     and b.state=1)
          and a.product_id NOT in (SELECT a.product_id
                                   FROM cere_sign_product a
                                            LEFT JOIN cere_activity_sign b ON a.sign_id = b.sign_id
                                            LEFT JOIN cere_platform_seckill c
                                                      ON b.activity_id = c.seckill_id and b.sign_type = 2
                                   where b.shop_id = #{shopId}
                                     and c.state=3)
          and a.product_id NOT in (SELECT a.product_id
                                   FROM cere_sign_product a
                                            LEFT JOIN cere_activity_sign b ON a.sign_id = b.sign_id
                                            LEFT JOIN cere_platform_discount c
                                                      ON b.activity_id = c.discount_id and b.sign_type = 3
                                   where b.shop_id = #{shopId}
                                     and c.state=3)
    </select>

    <select id="findDataProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.tool.CouponProduct">
        SELECT a.product_name, SUM(a.number) number, b.users
        from cere_order_product a
                 LEFT JOIN (SELECT COUNT(a.buyer_user_id) users, a.product_id
                            FROM (SELECT b.buyer_user_id, a.product_id
                                  FROM cere_order_product a,
                                       cere_shop_order b
                                  where a.order_id = b.order_id
                                    and b.state in (2, 3, 4)
                                  GROUP BY a.product_id, b.buyer_user_id) a
                            GROUP BY a.product_id) b ON a.product_id = b.product_id
        where order_id in (SELECT order_id
                           from cere_shop_order
                           where shop_id = #{shopId}
                             and shop_seckill_id = #{shopSeckillId}
                             and state in (2, 3, 4))
    </select>

    <select id="findSeckillName" parameterType="java.lang.Object" resultType="java.lang.String">
        SELECT seckill_name
        from cere_shop_seckill
        where shop_seckill_id = #{shopSeckillId}
    </select>

    <select id="findVisit" parameterType="java.lang.Object" resultType="int">
        SELECT IFNULL(COUNT(*), 0)
        from cere_buyer_seckill_visit
        where shop_seckill_id = #{shopSeckillId}
    </select>

    <select id="findPays" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from cere_shop_order
        where shop_seckill_id = #{shopSeckillId}
          and shop_id = #{shopId}
          and state in (2, 3, 4)
        GROUP BY buyer_user_id
    </select>

    <select id="findTotal" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(price) IS NULL, 0, SUM(price))
        from cere_shop_order
        where shop_seckill_id = #{shopSeckillId}
          and shop_id = #{shopId}
          and state in (2, 3, 4)
    </select>

    <select id="checkTime" parameterType="com.shop.cereshop.commons.domain.tool.CereShopSeckill"
            resultType="com.shop.cereshop.commons.domain.tool.CereShopSeckill">
        SELECT *
        FROM cere_shop_seckill
        where shop_id = #{shopId}
          and ((if(if_enable = 2, date_sub(effective_start, interval enable_time hour), effective_start) &lt;= #{effectiveStart} and effective_end &gt;= #{effectiveStart})
            OR (if(if_enable = 2, date_sub(effective_start, interval enable_time hour), effective_start) &lt;= #{effectiveEnd} and effective_end &gt;= #{effectiveEnd}))
          and state<![CDATA[!= ]]>2
    </select>

    <select id="checkUpdateTime" parameterType="com.shop.cereshop.commons.domain.tool.CereShopSeckill"
            resultType="com.shop.cereshop.commons.domain.tool.CereShopSeckill">
        SELECT *
        FROM cere_shop_seckill
        where shop_id = #{shopId}
          and ((if(if_enable = 2, date_sub(effective_start, interval enable_time hour), effective_start) &lt;= #{effectiveStart} and effective_end &gt;= #{effectiveStart})
            OR (if(if_enable = 2, date_sub(effective_start, interval enable_time hour), effective_start) &lt;= #{effectiveEnd} and effective_end &gt;= #{effectiveEnd}))
          and state<![CDATA[!= ]]>2 and shop_seckill_id<![CDATA[!= ]]>#{shopSeckillId}
    </select>

    <select id="checkOtherDiscount" parameterType="java.util.List" resultType="java.lang.Long">
        SELECT a.product_id FROM cere_shop_discount_detail a
        LEFT JOIN cere_shop_discount b ON a.shop_discount_id=b.shop_discount_id
        where b.shop_id = #{shopId}
        and ((if(b.if_enable = 2, date_sub(b.start_time, interval b.enable_time hour), b.start_time) &lt;= #{startTime} and b.end_time &gt;= #{startTime})
        OR (if(b.if_enable = 2, date_sub(b.start_time, interval b.enable_time hour), b.start_time) &lt;= #{endTime} and b.end_time &gt;= #{endTime}))
        and a.product_id in (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="checkOtherWork" parameterType="java.util.List" resultType="java.lang.Long">
        SELECT a.product_id FROM cere_shop_group_work_detail a
        LEFT JOIN cere_shop_group_work b ON a.shop_group_work_id=b.shop_group_work_id
        where b.shop_id=#{shopId}
        and ((if(b.if_enable = 2, date_sub(b.start_time, interval b.enable_time hour), b.start_time) &lt;= #{startTime} and b.end_time &gt;= #{startTime})
        OR (if(b.if_enable = 2, date_sub(b.start_time, interval b.enable_time hour), b.start_time) &lt;= #{endTime} and b.end_time &gt;= #{endTime}))
        and a.product_id in (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <update id="updateSeckillEndState" parameterType="java.lang.Object">
        update cere_shop_seckill SET state=4,update_time=#{time} where shop_seckill_id in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item.shopSeckillId}
        </foreach>
        )
    </update>
</mapper>
