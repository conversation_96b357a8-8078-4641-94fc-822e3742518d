<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.tool.CereChannelCouponDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereChannelCoupon">
        <result column="shop_coupon_id" jdbcType="BIGINT" property="shopCouponId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        shop_coupon_id, product_id, shop_id, create_time, update_time
    </sql>

    <select id="getAll" resultType="com.shop.cereshop.business.page.tool.ChannelCouponDetail">
        select a.shop_coupon_id, coupon_name, coupon_type, state, coupon_content, threshold,
        effective_start, effective_end, number, stock_number,
        c.product_id, product_name, d.product_image
        from cere_shop_coupon a join cere_channel_coupon b on b.shop_coupon_id = a.shop_coupon_id
        join cere_shop_product c on b.product_id = c.product_id
        join (select product_id, product_image from cere_product_image group by product_id) d on d.product_id = c.product_id
        where a.shop_id = #{shopId}
        <if test="search != null and search != ''">
            and a.coupon_name like concat('%', #{search}, '%')
        </if>
        order by a.update_time desc
    </select>

</mapper>
