<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.product.CereProductStatsByDayDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.product.CereProductStatsByDay">
        <id column="create_date" jdbcType="VARCHAR" property="createDate" />
        <id column="product_id" jdbcType="BIGINT" property="productId" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="add_cart_count" jdbcType="INTEGER" property="addCartCount" />
        <result column="visit_count" jdbcType="DECIMAL" property="visitCount" />
        <result column="sales_volume" jdbcType="DECIMAL" property="salesVolume" />
    </resultMap>
    <sql id="Base_Column_List">
        create_date, product_id, shop_id, add_cart_count, visit_count, sales_volume
    </sql>
    <select id="selectStatsByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cere_product_stats_by_day
        where shop_id = #{shopId}
        and create_date >= #{startTime}
        and create_date &lt;= #{endTime}
        order by create_date, sales_volume desc
    </select>
</mapper>
