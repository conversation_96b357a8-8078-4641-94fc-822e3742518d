<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.compose.CereShopComposeDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereShopCompose">
    <id column="compose_id" jdbcType="BIGINT" property="composeId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="compose_name" jdbcType="VARCHAR" property="composeName" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="compose_type" jdbcType="BIT" property="composeType" />
    <result column="promote" jdbcType="DECIMAL" property="promote" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    compose_id, shop_id, compose_name, start_time, end_time, compose_type, promote, price, `state`,
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_shop_compose
    where compose_id = #{composeId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_shop_compose
    where compose_id = #{composeId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="compose_id" keyProperty="composeId" parameterType="com.shop.cereshop.commons.domain.tool.CereShopCompose" useGeneratedKeys="true">
    insert into cere_shop_compose
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="composeName != null">
        compose_name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="composeType != null">
        compose_type,
      </if>
      <if test="promote != null">
        promote,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="composeName != null">
        #{composeName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="composeType != null">
        #{composeType,jdbcType=BIT},
      </if>
      <if test="promote != null">
        #{promote,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.tool.CereShopCompose">
    update cere_shop_compose
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="composeName != null">
        compose_name = #{composeName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="composeType != null">
        compose_type = #{composeType,jdbcType=BIT},
      </if>
      <if test="promote != null">
        promote = #{promote,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where compose_id = #{composeId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.tool.CereShopCompose">
    update cere_shop_compose
    set shop_id = #{shopId,jdbcType=BIGINT},
      compose_name = #{composeName,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=VARCHAR},
      end_time = #{endTime,jdbcType=VARCHAR},
      compose_type = #{composeType,jdbcType=BIT},
      promote = #{promote,jdbcType=DECIMAL},
      price = #{price,jdbcType=DECIMAL},
      `state` = #{state,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where compose_id = #{composeId,jdbcType=BIGINT}
  </update>

  <select id="checkTime" parameterType="com.shop.cereshop.business.param.compose.ComposeSaveParam" resultType="com.shop.cereshop.commons.domain.tool.CereShopCompose">
    SELECT compose_id
    from cere_shop_compose
    where ((start_time &lt;= #{startTime} and end_time &gt;= #{startTime})
      OR (start_time &lt;= #{endTime} and end_time &gt;= #{endTime}))
      and state NOT IN (2,3) and shop_id=#{shopId}
  </select>

  <select id="checkUpdateTime" parameterType="com.shop.cereshop.business.param.compose.ComposeUpdateParam" resultType="com.shop.cereshop.commons.domain.tool.CereShopCompose">
    SELECT compose_id
    from cere_shop_compose
    where ((start_time &lt;= #{startTime} and end_time &gt;= #{startTime})
      OR (start_time &lt;= #{endTime} and end_time &gt;= #{endTime}))
      and state NOT IN (2,3) and shop_id=#{shopId} and compose_id<![CDATA[!= ]]>#{composeId}
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.compose.ShopComposeDetail">
    SELECT * FROM cere_shop_compose where compose_id=#{composeId}
  </select>

  <select id="getAll" parameterType="com.shop.cereshop.business.param.compose.ComposeGetAllParam" resultType="com.shop.cereshop.business.page.compose.ShopCompose">
    SELECT a.compose_id, a.compose_name, CONCAT(a.start_time,'至',a.end_time) time,b.number,
    a.compose_type,a.state FROM cere_shop_compose a
    LEFT JOIN (SELECT COUNT(product_id) number,compose_id FROM cere_compose_product GROUP BY compose_id) b
    ON a.compose_id=b.compose_id
    where a.shop_id=#{shopId}
  </select>

  <select id="checkName" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.tool.CereShopCompose">
    SELECT * FROM cere_shop_compose where compose_name=#{composeName}
    <if test="composeId!=null">
      and compose_id<![CDATA[!= ]]>#{composeId}
    </if>
  </select>

  <select id="checkShopCompose" parameterType="java.lang.Object" resultType="java.lang.Long">
    SELECT a.product_id FROM cere_compose_product a
    LEFT JOIN cere_shop_compose b ON a.compose_id=b.compose_id
    where b.state=1 and ((b.start_time&lt;=#{startTime} and b.end_time&gt;=#{startTime}) OR
    (b.start_time&lt;=#{endTime} and b.end_time&gt;=#{endTime})) and b.shop_id=#{shopId}
    and a.product_id in (
    <foreach collection="ids" item="id" index="index" separator=",">
      #{id}
    </foreach>
    )
    <if test="composeId!=null">
      and b.compose_id <![CDATA[!= ]]>#{composeId}
    </if>
  </select>

  <select id="selectProduct" parameterType="com.shop.cereshop.business.param.product.ProductGetAllParam" resultType="com.shop.cereshop.business.page.product.ShopProduct">
    SELECT a.product_id,b.product_image,a.product_name,c.price,c.original_price,c.image sku_image,
    c.stock_number,a.shelve_state,concat('￥',c.price,'~￥',g.price) section,a.create_time from cere_shop_product a
    LEFT JOIN (SELECT a.product_id,a.product_image from cere_product_image a,
    cere_shop_product b where a.product_id=b.product_id GROUP BY a.product_id) b ON a.product_id=b.product_id
    LEFT JOIN (SELECT a.product_id,a.sku_id,MIN(a.price) price,MIN(a.original_price) original_price,
    SUM(a.stock_number) stock_number,c.image
    from cere_product_sku a,cere_shop_product b,cere_sku_name c where
    a.product_id=b.product_id and a.sku_id=c.sku_id and b.shop_id=#{shopId} GROUP BY a.product_id) c ON a.product_id=c.product_id
    LEFT JOIN cere_shop_group d ON a.shop_id=d.shop_id
    LEFT JOIN cere_product_classify e ON a.classify_id=e.classify_id
    LEFT JOIN (SELECT a.product_id,a.sku_id,MAX(a.price) price
    from cere_product_sku a,cere_shop_product b where
    a.product_id=b.product_id GROUP BY a.product_id) g ON a.product_id=g.product_id
    where a.shop_id=#{shopId} and a.shelve_state=1
    <if test="search!=null and search!=''">
      and a.product_name like concat('%',#{search},'%')
    </if>
    GROUP BY a.product_id
    ORDER by a.update_time desc
  </select>

  <select id="findProductPrice" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
    SELECT MIN(price) FROM cere_product_sku where product_id in (
    <foreach collection="list" item="item" index="index" separator=",">
      #{item.productId}
    </foreach>
    )
    GROUP BY product_id
    ORDER BY MIN(price)
  </select>
</mapper>
