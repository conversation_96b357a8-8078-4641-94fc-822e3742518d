<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.compose.CereComposeProductDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereComposeProduct">
        <result column="compose_id" jdbcType="BIGINT" property="composeId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
    </resultMap>
    <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.tool.CereComposeProduct">
        insert into cere_compose_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="composeId != null">
                compose_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="composeId != null">
                #{composeId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into cere_compose_product (compose_id, product_id) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.composeId},
            #{item.productId}
            )
        </foreach>
    </insert>

    <delete id="deleteByComposeId" parameterType="java.lang.Object">
        DELETE
        FROM cere_compose_product
        where compose_id = #{composeId}
    </delete>

    <select id="findProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.compose.ComposeProduct">
        SELECT a.product_id,
               c.product_name,
               b.product_image                     image,
               c.stock_number,
               CONCAT('￥', c.price, '~￥', d.price) sectionPrice
        FROM cere_compose_product a
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) b ON a.product_id = b.product_id
                 LEFT JOIN (SELECT a.product_id, b.product_name, MIN(a.price) price, SUM(a.stock_number) stock_number
                            from cere_product_sku a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) c ON a.product_id = c.product_id
                 LEFT JOIN (SELECT a.product_id, MAX(a.price) price
                            from cere_product_sku a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) d ON a.product_id = d.product_id
        where a.compose_id = #{composeId}
    </select>
</mapper>
