<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.user.CerePlatformUserRoleDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.user.CerePlatformUserRole">
    <result column="platform_user_id" jdbcType="BIGINT" property="platformUserId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.user.CerePlatformUserRole">
    insert into cere_platform_user_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="platformUserId != null">
        platform_user_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="platformUserId != null">
        #{platformUserId,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>
