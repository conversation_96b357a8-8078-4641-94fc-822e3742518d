<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.label.CereShopLabelDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.label.CereShopLabel">
    <id column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="label_name" jdbcType="VARCHAR" property="labelName" />
    <result column="label_type" jdbcType="TINYINT" property="labelType" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    label_id, shop_id, label_name, create_time, update_time,label_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_shop_label
    where label_id = #{labelId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_shop_label
    where label_id = #{labelId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="label_id" keyProperty="labelId" parameterType="com.shop.cereshop.commons.domain.label.CereShopLabel" useGeneratedKeys="true">
    insert into cere_shop_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="labelName != null and labelName!=''">
        label_name,
      </if>
      <if test="labelType != null">
        label_type,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="labelName != null and labelName!=''">
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelType != null and labelName!=''">
        #{labelType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.label.CereShopLabel">
    update cere_shop_label
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="labelName != null and labelName!=''">
        label_name = #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelType != null">
        label_type = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where label_id = #{labelId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.label.CereShopLabel">
    update cere_shop_label
    set shop_id = #{shopId,jdbcType=BIGINT},
      label_name = #{labelName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where label_id = #{labelId,jdbcType=BIGINT}
  </update>

  <select id="findByShopIdNotGroup" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.label.CereShopLabel">
    SELECT label_id FROM cere_shop_label where shop_id=#{shopId} and label_name='未分组'
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.label.CereShopLabel">
    SELECT label_id,label_name FROM cere_shop_label where label_id=#{labelId}
  </select>

  <select id="getAll" parameterType="com.shop.cereshop.business.param.label.LabelGetAllParam" resultType="com.shop.cereshop.business.page.label.ShopLabel">
    SELECT label_id,label_name FROM cere_shop_label where shop_id=#{shopId}
    and label_type=#{labelType}
    ORDER BY update_time DESC,create_time DESC
  </select>
</mapper>
