<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.customer_service.CereCustomerServiceDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.customer_service.CereCustomerService">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="head_img" jdbcType="VARCHAR" property="headImg"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, head_img, url, state, create_time,update_time
    </sql>

</mapper>
