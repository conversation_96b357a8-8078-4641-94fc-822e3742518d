<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.scene.CereShopSceneMemberCouponDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.scene.CereShopSceneMemberCoupon">
    <result column="scene_id" jdbcType="BIGINT" property="sceneId" />
    <result column="member_level_id" jdbcType="BIGINT" property="memberLevelId" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.scene.CereShopSceneMemberCoupon">
    insert into cere_shop_scene_member_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="memberLevelId != null">
        member_level_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sceneId != null">
        #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="memberLevelId != null">
        #{memberLevelId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into cere_shop_scene_member_coupon (scene_id, member_level_id, coupon_id) values
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.sceneId},
      #{item.memberLevelId},
      #{item.couponId}
      )
    </foreach>
  </insert>

  <delete id="deleteBySceneId" parameterType="java.lang.Object">
    DELETE FROM cere_shop_scene_member_coupon where scene_id=#{sceneId}
  </delete>

  <select id="findCoupons" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.tool.ShopCoupon">
    SELECT b.shop_coupon_id,b.coupon_name,b.coupon_type,
    IF(b.coupon_type=1,CONCAT('满',b.threshold,'减',b.coupon_content),
    CONCAT('满',b.threshold,'打',b.coupon_content,'折')) content from cere_shop_scene_member_coupon a
    LEFT JOIN cere_shop_coupon b ON a.coupon_id=b.shop_coupon_id
    where a.member_level_id=#{memberLevelId} and a.scene_id=#{sceneId}
  </select>
</mapper>
