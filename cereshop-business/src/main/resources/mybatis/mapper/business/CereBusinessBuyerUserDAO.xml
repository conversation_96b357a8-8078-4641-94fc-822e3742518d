<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.business.CereBusinessBuyerUserDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.business.CereBusinessBuyerUser">
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <insert id="insertOrUpdate" parameterType="com.shop.cereshop.commons.domain.business.CereBusinessBuyerUser">
    insert into cere_business_buyer_user (shop_id, buyer_user_id, create_time, update_time)
    values (#{shopId,jdbcType=BIGINT}, #{buyerUserId,jdbcType=BIGINT}, #{createTime,jdbcType=VARCHAR}, #{updateTime,jdbcType=VARCHAR})
    on duplicate key update shop_id = #{shopId}
  </insert>
</mapper>
