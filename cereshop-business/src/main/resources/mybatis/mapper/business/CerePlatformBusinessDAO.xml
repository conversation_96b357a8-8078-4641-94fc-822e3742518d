<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.business.CerePlatformBusinessDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    <id column="business_user_id" jdbcType="BIGINT" property="businessUserId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    business_user_id, username, `password`, `name`, avatar, phone, sex, email, token, `state`, create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_business
    where business_user_id = #{businessUserId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_platform_business
    where business_user_id = #{businessUserId,jdbcType=BIGINT}
  </delete>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    update cere_platform_business
    <set>
      <if test="username != null and username!=''">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password!=''">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="name != null and name!=''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null and avatar!=''">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone!=''">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="sex != null and sex!=''">
        sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="email != null and email!=''">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="token != null and token!=''">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where business_user_id = #{businessUserId,jdbcType=BIGINT}
  </update>

  <select id="findByUserName" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.shop.PlatformBusiness">
    SELECT a.business_user_id,a.`name`,a.avatar,a.phone,a.sex,a.email,IF(a.state=0,a.state,c.state) state,a.token,a.password,c.shop_id,
    c.check_state FROM cere_platform_business a
    LEFT JOIN cere_business_shop b ON a.business_user_id=b.business_user_id
    LEFT JOIN cere_platform_shop c ON b.shop_id=c.shop_id
    where a.username = #{username}
  </select>

  <select id="findByPhone" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.shop.PlatformBusiness">
    SELECT a.business_user_id,a.`name`,a.avatar,a.phone,a.sex,a.email,IF(a.state=0,a.state,c.state) state,a.token,a.password,c.shop_id,
    c.check_state FROM cere_platform_business a
    LEFT JOIN cere_business_shop b ON a.business_user_id=b.business_user_id
    LEFT JOIN cere_platform_shop c ON b.shop_id=c.shop_id
    where a.phone = #{phone}
  </select>

  <select id="findById" resultType="com.shop.cereshop.business.page.shop.PlatformBusiness">
    SELECT a.business_user_id,a.username,a.`name`,a.avatar,a.phone,a.sex,a.email,IF(a.state=0,a.state,c.state) state,a.token,a.password,c.shop_id,
           c.check_state FROM cere_platform_business a
                                LEFT JOIN cere_business_shop b ON a.business_user_id=b.business_user_id
                                LEFT JOIN cere_platform_shop c ON b.shop_id=c.shop_id
    where a.business_user_id = #{businessUserId}
  </select>
  <select id="findByToken" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    SELECT a.business_user_id,`name`,sex,email,`username`, b.shop_id FROM cere_platform_business a
    join cere_business_shop b on a.business_user_id = b.business_user_id
    where a.token=#{token}
  </select>

  <update id="updateToken" parameterType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    update cere_platform_business set
    token=#{token},update_time=#{updateTime}
    where business_user_id=#{businessUserId}
  </update>

  <select id="getAll" parameterType="com.shop.cereshop.business.param.user.BusinessGetAllUser" resultType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    SELECT a.business_user_id,a.`name`,a.sex,a.phone,a.email,a.state,a.create_time,a.username FROM cere_platform_business a
    LEFT JOIN cere_business_shop b ON a.business_user_id=b.business_user_id
    where b.shop_id=#{shopId}
    <if test="search!=null and search!=''">
      and (a.username like concat('%',#{search},'%') OR
      a.`name` like concat('%',#{search},'%'))
    </if>
    <if test="state != null">
      and a.state = #{state}
    </if>
    ORDER BY a.update_time DESC,a.create_time DESC
  </select>

  <select id="checkUserName" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    SELECT business_user_id FROM cere_platform_business where username=#{username}
    <if test="businessUserId!=null">
      and business_user_id<![CDATA[!= ]]>#{businessUserId}
    </if>
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.user.Business">
    SELECT business_user_id,`name`,phone,sex,email,state,username,password FROM cere_platform_business where business_user_id=#{businessUserId}
  </select>

  <select id="findAdminUser" resultType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    select a.business_user_id,a.`name`,a.phone,a.sex,a.email,a.state,a.create_time,a.username from cere_platform_business a
	join cere_business_user_role b on b.business_user_id = a.business_user_id
	join cere_platform_role c on c.role_id = b.role_id and c.role_name = '管理员' and c.project = #{shopId}
	limit 1
  </select>

  <select id="checkPhone" resultType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    SELECT business_user_id FROM cere_platform_business where phone = #{phone}
    <if test="businessUserId!=null">
      and business_user_id<![CDATA[!= ]]>#{businessUserId}
    </if>
  </select>

</mapper>
