<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.order.CereShopOrderDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.order.CereShopOrder">
        <id column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="order_formid" jdbcType="VARCHAR" property="orderFormid"/>
        <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId"/>
        <result column="coupon_id" jdbcType="BIGINT" property="couponId"/>
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_seckill_id" jdbcType="BIGINT" property="shopSeckillId"/>
        <result column="shop_group_work_id" jdbcType="BIGINT" property="shopGroupWorkId"/>
        <result column="shop_discount_id" jdbcType="BIGINT" property="shopDiscountId"/>
        <result column="shop_operate_id" jdbcType="BIGINT" property="shopOperateId"/>
        <result column="order_price" jdbcType="DECIMAL" property="orderPrice"/>
        <result column="logistics_price" jdbcType="DECIMAL" property="logisticsPrice"/>
        <result column="discount_price" jdbcType="DECIMAL" property="discountPrice"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="old_price" jdbcType="DECIMAL" property="oldPrice"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="payment_state" jdbcType="BIT" property="paymentState"/>
        <result column="payment_mode" jdbcType="BIT" property="paymentMode"/>
        <result column="payment_time" jdbcType="VARCHAR" property="paymentTime"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone"/>
        <result column="receive_name" jdbcType="VARCHAR" property="receiveName"/>
        <result column="receive_phone" jdbcType="VARCHAR" property="receivePhone"/>
        <result column="receive_adress" jdbcType="VARCHAR" property="receiveAdress"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="receive_time" jdbcType="VARCHAR" property="receiveTime"/>
        <result column="postal_code" jdbcType="VARCHAR" property="postalCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="after_state" jdbcType="BIT" property="afterState"/>
        <result column="logistics_id" jdbcType="BIGINT" property="logisticsId"/>
        <result column="distributor_id" jdbcType="BIGINT" property="distributorId"/>
        <result column="direct_distributor_money" jdbcType="DECIMAL" property="directDistributorMoney"/>
        <result column="indirect_distributor_money" jdbcType="DECIMAL" property="indirectDistributorMoney"/>
        <result column="seckill_id" jdbcType="BIGINT" property="seckillId"/>
        <result column="discount_id" jdbcType="BIGINT" property="discountId"/>
        <result column="polite_id" jdbcType="BIGINT" property="politeId"/>
        <result column="scene_id" jdbcType="BIGINT" property="sceneId"/>
        <result column="delivery_type" jdbcType="TINYINT" property="deliveryType"/>
        <result column="pickup_store_id" jdbcType="BIGINT" property="pickupStoreId"/>
        <result column="pickup_store_name" jdbcType="VARCHAR" property="pickupStoreName"/>
        <result column="pickup_contact" jdbcType="VARCHAR" property="pickupContact"/>
        <result column="pickup_phone" jdbcType="VARCHAR" property="pickupPhone"/>
        <result column="verify_code" jdbcType="VARCHAR" property="verifyCode"/>
        <result column="verify_status" jdbcType="TINYINT" property="verifyStatus"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        order_id, parent_id,shop_id, order_formid, buyer_user_id, coupon_id,id, shop_seckill_id, shop_group_work_id,
        shop_discount_id, shop_operate_id, order_price, logistics_price,discount_price, price, old_price,
        `state`, payment_state, payment_mode, payment_time, customer_name, customer_phone,
        receive_name, receive_phone, receive_adress,address,receive_time, postal_code, remark, after_state, logistics_id,
        distributor_id, direct_distributor_money, indirect_distributor_money,seckill_id,discount_id,polite_id,scene_id,
        delivery_type, pickup_store_id, pickup_store_name, pickup_contact, pickup_phone, verify_code, verify_status,
        create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cere_shop_order
        where order_id = #{orderId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cere_shop_order
        where order_id = #{orderId,jdbcType=BIGINT}
    </delete>
    <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.order.CereShopOrder">
        update cere_shop_order
        <set>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="orderFormid != null and orderFormid!=''">
                order_formid = #{orderFormid,jdbcType=VARCHAR},
            </if>
            <if test="buyerUserId != null">
                buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
            </if>
            <if test="couponId != null">
                coupon_id = #{couponId,jdbcType=BIGINT},
            </if>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="shopSeckillId != null">
                shop_seckill_id = #{shopSeckillId,jdbcType=BIGINT},
            </if>
            <if test="shopGroupWorkId != null">
                shop_group_work_id = #{shopGroupWorkId,jdbcType=BIGINT},
            </if>
            <if test="shopDiscountId != null">
                shop_discount_id = #{shopDiscountId,jdbcType=BIGINT},
            </if>
            <if test="shopOperateId != null">
                shop_operate_id = #{shopOperateId,jdbcType=BIGINT},
            </if>
            <if test="orderPrice != null">
                order_price = #{orderPrice,jdbcType=DECIMAL},
            </if>
            <if test="logisticsPrice != null">
                logistics_price = #{logisticsPrice,jdbcType=DECIMAL},
            </if>
            <if test="discountPrice != null">
                discount_price = #{discountPrice,jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="oldPrice != null">
                old_price = #{oldPrice,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=BIT},
            </if>
            <if test="paymentState != null">
                payment_state = #{paymentState,jdbcType=BIT},
            </if>
            <if test="paymentMode != null">
                payment_mode = #{paymentMode,jdbcType=BIT},
            </if>
            <if test="paymentTime != null and paymentTime!=''">
                payment_time = #{paymentTime,jdbcType=VARCHAR},
            </if>
            <if test="customerName != null and customerName!=''">
                customer_name = #{customerName,jdbcType=VARCHAR},
            </if>
            <if test="customerPhone != null and customerPhone!=''">
                customer_phone = #{customerPhone,jdbcType=VARCHAR},
            </if>
            <if test="receiveName != null and receiveName!=''">
                receive_name = #{receiveName,jdbcType=VARCHAR},
            </if>
            <if test="receivePhone != null and receivePhone!=''">
                receive_phone = #{receivePhone,jdbcType=VARCHAR},
            </if>
            <if test="receiveAdress != null and receiveAdress!=''">
                receive_adress = #{receiveAdress,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address!=''">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="receiveTime != null and receiveTime!=''">
                receive_time = #{receiveTime,jdbcType=VARCHAR},
            </if>
            <if test="postalCode != null and postalCode!=''">
                postal_code = #{postalCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark!=''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="afterState != null">
                after_state = #{afterState,jdbcType=BIT},
            </if>
            <if test="logisticsId != null">
                logistics_id = #{logisticsId,jdbcType=BIGINT},
            </if>
            <if test="distributorId != null">
                distributor_id = #{distributorId,jdbcType=BIGINT},
            </if>
            <if test="directDistributorMoney != null">
                direct_distributor_money = #{directDistributorMoney,jdbcType=DECIMAL},
            </if>
            <if test="indirectDistributorMoney != null">
                indirect_distributor_money = #{indirectDistributorMoney,jdbcType=DECIMAL},
            </if>
            <if test="seckillId != null">
                seckill_id = #{seckillId,jdbcType=BIGINT},
            </if>
            <if test="discountId != null">
                discount_id = #{discountId,jdbcType=BIGINT},
            </if>
            <if test="politeId != null">
                polite_id = #{politeId,jdbcType=BIGINT},
            </if>
            <if test="sceneId != null">
                scene_id = #{sceneId,jdbcType=BIGINT},
            </if>
            <if test="deliveryType != null">
                delivery_type = #{deliveryType,jdbcType=TINYINT},
            </if>
            <if test="pickupStoreId != null">
                pickup_store_id = #{pickupStoreId,jdbcType=BIGINT},
            </if>
            <if test="pickupStoreName != null and pickupStoreName!=''">
                pickup_store_name = #{pickupStoreName,jdbcType=VARCHAR},
            </if>
            <if test="pickupContact != null and pickupContact!=''">
                pickup_contact = #{pickupContact,jdbcType=VARCHAR},
            </if>
            <if test="pickupPhone != null and pickupPhone!=''">
                pickup_phone = #{pickupPhone,jdbcType=VARCHAR},
            </if>
            <if test="verifyCode != null and verifyCode!=''">
                verify_code = #{verifyCode,jdbcType=VARCHAR},
            </if>
            <if test="verifyStatus != null">
                verify_status = #{verifyStatus,jdbcType=TINYINT},
            </if>
            <if test="createTime != null and createTime!=''">
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null and updateTime!=''">
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where order_id = #{orderId,jdbcType=BIGINT}
    </update>

    <select id="getAll" parameterType="com.shop.cereshop.business.param.order.OrderGetAllParam"
            resultType="com.shop.cereshop.business.page.order.ShopOrder">
        SELECT a.order_id,a.order_formid,a.buyer_user_id,a.price,IF(a.order_id IS NOT NULL,sum(b.number),NULL) number,a.state,a.remark,d.transaction_id,
        a.customer_name,a.payment_mode,a.payment_state,a.payment_time,a.create_time,a.receive_name,a.receive_phone,a.address,a.receive_adress,c.shop_name FROM cere_shop_order a
        LEFT JOIN cere_order_product b ON a.order_id=b.order_id
        LEFT JOIN cere_platform_shop c ON a.shop_id=c.shop_id
        LEFT JOIN cere_pay_log d ON a.order_formid=d.order_formid
        where a.shop_id=#{shopId}
        <if test='searchType=="1" and search!=null and search!=""'>
            and a.order_id like concat('%',#{search},'%')
        </if>
        <if test='searchType=="2" and search!=null and search!=""'>
            and a.customer_name like concat('%',#{search},'%')
        </if>
        <if test='searchType=="3" and search!=null and search!=""'>
            and a.receive_name like concat('%',#{search},'%')
        </if>
        <if test='searchType=="4" and search!=null and search!=""'>
            and a.receive_phone like concat('%',#{search},'%')
        </if>
        <if test='searchType=="5" and search!=null and search!=""'>
            and b.product_id like concat('%',#{search},'%')
        </if>
        <if test="state!=null">
            and a.state=#{state}
        </if>
        <if test='state==2'>
            and a.order_id NOT IN (SELECT order_id FROM cere_order_after)
        </if>
        <if test="startTime!=null and startTime!=''">
            and a.create_time&gt;=#{startTime} and a.create_time&lt;=#{endTime}
        </if>
        GROUP BY a.order_id
        ORDER BY
        <choose>
            <when test='state=="2"'>
                a.create_time
            </when>
            <otherwise>
                a.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.order.ShopOrder">
        SELECT a.order_id,
               a.order_formid,
               b.transaction_id,
               GROUP_CONCAT(c.after_formid)                                                                                                   afterFormIds,
               a.state,
               a.after_state,
               a.payment_mode,
               d.logistics_name,
               a.create_time,
               a.payment_time,
               a.customer_name,
               a.remark,
               a.receive_name,
               a.receive_phone,
               a.receive_adress,
               a.address,
               a.postal_code,
               a.order_price,
               a.logistics_price,
               a.price,
               a.buyer_user_id,
               e.deliver_formid,f.dict_name express,
               IF(c.after_state = 1, '审核中',
                  IF(c.after_state = 2, '退款中', IF(c.after_state = 3, '退货中', IF(c.after_state = 4, '退款成功',
                                                                               IF(c.after_state = 5, '退款失败',
                                                                                  IF(c.after_state = 6, '审核不通过', IF(
                                                                                              c.after_state = 7, '评审中',
                                                                                              IF(c.after_state = 8,
                                                                                                 '退货完成,拒绝退款',
                                                                                                 IF(c.after_state = 9, '已关闭', '审核通过'))))))))) after_state_name
        from cere_shop_order a
                 LEFT JOIN cere_pay_log b ON a.order_formid = b.order_formid and b.state = '支付'
                 LEFT JOIN cere_order_after c ON a.order_id = c.order_id
                 LEFT JOIN cere_order_logistics d ON a.logistics_id = d.logistics_id
                 LEFT JOIN cere_order_dilever e ON a.order_id=e.order_id
                 LEFT JOIN cere_platform_dict f ON e.express=f.dict_id
        where a.order_id = #{orderId}
        <if test="shopId != null and shopId != 0">
            and a.shop_id = #{shopId}
        </if>
    </select>

    <select id="getOrderTotals" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from cere_shop_order
        where buyer_user_id = #{buyerUserId}
    </select>

    <select id="getProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.order.Product">
        SELECT order_product_id,
               op.product_id,
               op.sku_id,
               op.product_name,
               op.product_price,
               op.number,
               op.image,
               op.product_price * op.number total,
               cb.brand_name,
               op.SKU
        from cere_order_product op
        join cere_shop_product sp on op.product_id = sp.product_id
        left join cere_brand cb on cb.id = sp.brand_id
        where order_id = #{orderId}
    </select>

    <select id="findSkuAttribute" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.order.SkuDetail">
        SELECT sku_name, sku_value
        FROM cere_order_product_attribute
        where order_product_id = #{orderProductId}
    </select>

    <update id="updateState" parameterType="com.shop.cereshop.commons.domain.order.CereShopOrder">
        update cere_shop_order
        <set>
            <if test="state != null">
                `state` = #{state,jdbcType=BIT},
            </if>
            <if test="afterState != null">
                after_state = #{afterState,jdbcType=BIT},
            </if>
            <if test="updateTime != null and updateTime!=''">
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where order_id = #{orderId,jdbcType=BIGINT}
    </update>

    <select id="getTurnover" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(price) IS NULL, 0, SUM(price))
        FROM cere_shop_order
        where shop_id = #{shopId}
          and state in (2, 3, 4)
    </select>

    <select id="getFrozenMoney" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(price) IS NULL, 0, SUM(price))
        FROM cere_shop_order
        where shop_id = #{shopId}
          and state in (2, 3)
    </select>

    <select id="getWithdrawableMoney" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(withdrawal_money) IS NULL, 0, SUM(withdrawal_money))
        FROM cere_shop_withdrawal
        where shop_id = #{shopId}
          and state = 1
    </select>

    <select id="getAllWithdrawableMoney" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(price) IS NULL, 0, SUM(price))
        FROM cere_shop_order
        where shop_id = #{shopId}
          and state = 4
    </select>

    <select id="getWithdrawableStayMoney" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(withdrawal_money) IS NULL, 0, SUM(withdrawal_money))
        FROM cere_shop_withdrawal
        where shop_id = #{shopId}
          and state = 0
    </select>

    <select id="getFinanceByDay" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.finance.Finance">
        SELECT IF(SUM(total_fee) IS NULL,0,SUM(total_fee)) income,LEFT(create_time,10) time from cere_pay_log
        where shop_id=#{shopId} and create_time like concat('%',#{time},'%')
        <if test='type=="1"'>
            and state='支付'
        </if>
        <if test='type=="2"'>
            and (state='退款' OR state='提现')
        </if>
        GROUP BY LEFT(create_time,10)
        ORDER BY create_time DESC
    </select>

    <select id="getFinanceByMonth" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.business.page.finance.Finance">
        SELECT IF(SUM(total_fee) IS NULL,0,SUM(total_fee)) income,LEFT(create_time,7) time from cere_pay_log
        where shop_id=#{shopId} and create_time like concat('%',#{time},'%')
        <if test='type=="1"'>
            and state='支付'
        </if>
        <if test='type=="2"'>
            and (state='退款' OR state='提现')
        </if>
        GROUP BY LEFT(create_time,7)
        ORDER BY create_time DESC
    </select>

    <select id="getWithdrawalDetails" parameterType="com.shop.cereshop.business.param.finance.FinanceWithdrawalParam"
            resultType="com.shop.cereshop.business.page.finance.WithdrawalDetail">
        SELECT withdrawal_money,state,bank_card,bank_name,apply_time from cere_shop_withdrawal
        where shop_id=#{shopId}
        <if test="startTime!=null and startTime!=''">
            and apply_time&gt;=#{startTime} and apply_time&lt;=#{endTime}
        </if>
    </select>

    <select id="getBank" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.finance.Bank">
        SELECT a.shop_id,
               b.shop_name,
               b.shop_code,
               c.dict_name   bankName,
               a.card_number bankCard,
               a.card_name   collectionNme
        from cere_shop_bank a
                 LEFT JOIN cere_platform_shop b ON a.shop_id = b.shop_id
                 LEFT JOIN cere_platform_dict c ON a.bank = c.dict_id
        where a.shop_id = #{shopId}
    </select>

    <select id="getDetails" parameterType="com.shop.cereshop.business.param.finance.FinanceDetailParam"
            resultType="com.shop.cereshop.business.page.finance.FlowingWater">
        SELECT IF(state='支付','订单入账',IF(state='退款','买家退款','提现')) waterType,
        order_formid,IF(state='支付','收入','支出') incomeType,total_fee money,create_time time from cere_pay_log
        where shop_id=#{shopId} and create_time like concat('%',#{time},'%')
        <if test='income=="1"'>
            and state='支付'
        </if>
        <if test='income=="2"'>
            and (state='退款' OR state='提现')
        </if>
        <if test="state!=null and state!=''">
            and state=#{state}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="getTurnoverByTime" parameterType="com.shop.cereshop.business.page.finance.FlowingWater"
            resultType="java.math.BigDecimal">
        SELECT IF(SUM(price) IS NULL, 0, SUM(price))
        FROM cere_shop_order
        where shop_id = #{shopId}
          and state in (2, 3)
          and create_time &lt;= #{time}
    </select>

    <select id="getWithdrawableStayMoneyByTime" parameterType="com.shop.cereshop.business.page.finance.FlowingWater"
            resultType="java.math.BigDecimal">
        SELECT IF(SUM(withdrawal_money) IS NULL, 0, SUM(withdrawal_money))
        FROM cere_shop_withdrawal
        where shop_id = #{shopId}
          and state = 0
          and apply_time &lt;= #{time}
    </select>

    <select id="getAllWithdrawableMoneyByTime" parameterType="com.shop.cereshop.business.page.finance.FlowingWater"
            resultType="java.math.BigDecimal">
        SELECT IF(SUM(price) IS NULL, 0, SUM(price))
        FROM cere_shop_order
        where shop_id = #{shopId}
          and state = 4
          and create_time &lt;= #{time}
    </select>

    <select id="getWithdrawableMoneyByTime" parameterType="com.shop.cereshop.business.page.finance.FlowingWater"
            resultType="java.math.BigDecimal">
        SELECT IF(SUM(withdrawal_money) IS NULL, 0, SUM(withdrawal_money))
        FROM cere_shop_withdrawal
        where shop_id = #{shopId}
          and state = 1
          and apply_time &lt;= #{time}
    </select>

    <update id="updateBatchStock" parameterType="java.util.List">
        update cere_product_sku
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_number =case" suffix="end,">
                <foreach collection="skus" item="i" index="index">
                    <if test="i.stockNumber!=null">
                        when sku_id=#{i.skuId} then stock_number+#{i.stockNumber}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="skus" separator="or" item="i" index="index">
            sku_id=#{i.skuId}
        </foreach>
    </update>

    <select id="findByIds" parameterType="java.util.List"
            resultType="com.shop.cereshop.commons.domain.order.CereShopOrder">
        SELECT
        order_id,`payment_state`,`state`,price,order_formid,shop_id,buyer_user_id,shop_seckill_id,shop_group_work_id,shop_discount_id
        FROM cere_shop_order where order_id in (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <select id="findPayLog" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.pay.CerePayLog">
        SELECT *
        FROM cere_pay_log
        where order_formid = #{orderFormid}
    </select>

    <select id="findByOrderFormid" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.order.CereShopOrder">
        SELECT a.*
        FROM cere_shop_order a
                 LEFT JOIN cere_pay_log b ON a.order_formid = b.order_formid
        where b.out_trade_no LIKE CONCAT('%', #{orderFormid}, '%')
          and b.state = '支付'
    </select>

    <select id="findUnPayBySeckillId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.order.CereShopOrder">
        SELECT * FROM cere_shop_order where shop_seckill_id=#{shopSeckillId} and state = 1 and payment_state = 0
    </select>

    <select id="findUnPayByDiscountId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.order.CereShopOrder">
        SELECT * FROM cere_shop_order where shop_discount_id=#{shopDiscountId} and state = 1 and payment_state = 0
    </select>

    <select id="findAfter"  resultType="com.shop.cereshop.business.page.order.ShopOrderAfter">
        SELECT after_id,order_id,after_state FROM cere_order_after where 1=1
        group by order_id
        order by order_id,create_time desc
    </select>
    <select id="selectWaitToPayStatsByDate" resultType="com.shop.cereshop.business.page.index.OrderConvertDTO">
        select date(create_time) as date,
		count(*) as waitToPayCount
		from cere_shop_order
		where shop_id = #{shopId}
		and create_time >= #{startTime}
		and create_time &lt;= #{endTime}
		and payment_state = 0
		group by date(create_time)
    </select>
    <select id="selectPayStatsByDate" resultType="com.shop.cereshop.business.page.index.OrderConvertDTO">
        select date(payment_time) as date,
		count(*) as payCount
		from cere_shop_order
		where shop_id = #{shopId}
		and payment_time >= #{startTime}
		and payment_time &lt;= #{endTime}
		and payment_state = 1
		group by date(payment_time)
    </select>
    <select id="selectDeliverStatsByDate" resultType="com.shop.cereshop.business.page.index.OrderConvertDTO">
        select date(b.create_time) as date,
		count(*) as deliverCount
		from cere_shop_order a
		join cere_order_dilever b on b.order_id = a.order_id
		where a.shop_id = #{shopId}
		and b.create_time >= #{startTime}
		and b.create_time &lt;= #{endTime}
		group by date(b.create_time)
    </select>
    <select id="selectAfterSaleStatsByDate" resultType="com.shop.cereshop.business.page.index.OrderConvertDTO">
        select date(b.create_time) as date,
		count(*) as afterSaleCount
		from cere_shop_order a
		join cere_order_after b on b.order_id = a.order_id
		where a.shop_id = #{shopId}
		and b.create_time >= #{startTime}
		and b.create_time &lt;= #{endTime}
		group by date(b.create_time)
    </select>

    <select id="selectSalesVolumeBySkuIdList" resultType="java.util.Map">
        select sku_id, sum(number) as sales_volume
        from cere_shop_order a join cere_order_product b on b.order_id = a.order_id
        and a.payment_state = 1 and b.sku_id in
        <foreach collection="list" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        group by b.sku_id
    </select>

    <!-- 根据核销码查询订单 -->
    <select id="findByVerifyCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cere_shop_order
        WHERE verify_code = #{verifyCode,jdbcType=VARCHAR}
        LIMIT 1
    </select>

</mapper>
