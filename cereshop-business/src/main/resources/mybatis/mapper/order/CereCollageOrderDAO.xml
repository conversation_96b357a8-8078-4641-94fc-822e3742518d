<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.order.CereCollageOrderDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.collage.CereCollageOrder">
    <id column="collage_id" jdbcType="BIGINT" property="collageId" />
    <result column="shop_group_work_id" jdbcType="BIGINT" property="shopGroupWorkId" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="collage_name" jdbcType="VARCHAR" property="collageName" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="surplus_number" jdbcType="INTEGER" property="surplusNumber" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    collage_id, shop_group_work_id, `state`, collage_name, buyer_user_id, surplus_number,
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_collage_order
    where collage_id = #{collageId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_collage_order
    where collage_id = #{collageId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="collage_id" keyProperty="collageId" parameterType="com.shop.cereshop.commons.domain.collage.CereCollageOrder" useGeneratedKeys="true">
    insert into cere_collage_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopGroupWorkId != null">
        shop_group_work_id,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="collageName != null">
        collage_name,
      </if>
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="surplusNumber != null">
        surplus_number,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopGroupWorkId != null">
        #{shopGroupWorkId,jdbcType=BIGINT},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="collageName != null">
        #{collageName,jdbcType=VARCHAR},
      </if>
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="surplusNumber != null">
        #{surplusNumber,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.collage.CereCollageOrder">
    update cere_collage_order
    <set>
      <if test="shopGroupWorkId != null">
        shop_group_work_id = #{shopGroupWorkId,jdbcType=BIGINT},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="collageName != null">
        collage_name = #{collageName,jdbcType=VARCHAR},
      </if>
      <if test="buyerUserId != null">
        buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="surplusNumber != null">
        surplus_number = #{surplusNumber,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where collage_id = #{collageId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.collage.CereCollageOrder">
    update cere_collage_order
    set shop_group_work_id = #{shopGroupWorkId,jdbcType=BIGINT},
      `state` = #{state,jdbcType=BIT},
      collage_name = #{collageName,jdbcType=VARCHAR},
      buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
      surplus_number = #{surplusNumber,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where collage_id = #{collageId,jdbcType=BIGINT}
  </update>

  <select id="findOrderIds" parameterType="java.lang.Object" resultType="java.lang.Long">
    SELECT order_id from cere_collage_order_detail where collage_id=#{collageId}
  </select>
</mapper>
