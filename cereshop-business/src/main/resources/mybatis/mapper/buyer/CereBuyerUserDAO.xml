<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.buyer.CereBuyerUserDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.buyer.CereBuyerUser">
    <id column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="wechat_open_id" jdbcType="VARCHAR" property="wechatOpenId" />
    <result column="wechat_union_id" jdbcType="VARCHAR" property="wechatUnionId" />
    <result column="wechat_name" jdbcType="VARCHAR" property="wechatName" />
    <result column="wechat_number" jdbcType="VARCHAR" property="wechatNumber" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="head_image" jdbcType="VARCHAR" property="headImage" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    buyer_user_id, `name`, sex, birthday, wechat_open_id, wechat_union_id, wechat_name,
    wechat_number, phone, `password`, head_image, `state`, remark, token, create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_buyer_user
    where buyer_user_id = #{buyerUserId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_buyer_user
    where buyer_user_id = #{buyerUserId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="buyer_user_id" keyProperty="buyerUserId" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerUser" useGeneratedKeys="true">
    insert into cere_buyer_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null and name!=''">
        `name`,
      </if>
      <if test="sex != null and sex!=''">
        sex,
      </if>
      <if test="birthday != null and birthday!=''">
        birthday,
      </if>
      <if test="wechatOpenId != null and wechatOpenId!=''">
        wechat_open_id,
      </if>
      <if test="wechatUnionId != null and wechatUnionId!=''">
        wechat_union_id,
      </if>
      <if test="wechatName != null and wechatName!=''">
        wechat_name,
      </if>
      <if test="wechatNumber != null and wechatNumber!=''">
        wechat_number,
      </if>
      <if test="phone != null and phone!=''">
        phone,
      </if>
      <if test="password != null and password!=''">
        `password`,
      </if>
      <if test="headImage != null and headImage!=''">
        head_image,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="remark != null and remark!=''">
        remark,
      </if>
      <if test="token != null and token!=''">
        token,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null and name!=''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null and sex!=''">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null and birthday!=''">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="wechatOpenId != null and wechatOpenId!=''">
        #{wechatOpenId,jdbcType=VARCHAR},
      </if>
      <if test="wechatUnionId != null and wechatUnionId!=''">
        #{wechatUnionId,jdbcType=VARCHAR},
      </if>
      <if test="wechatName != null and wechatName!=''">
        #{wechatName,jdbcType=VARCHAR},
      </if>
      <if test="wechatNumber != null and wechatNumber!=''">
        #{wechatNumber,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone!=''">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password!=''">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="headImage != null and headImage!=''">
        #{headImage,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="remark != null and remark!=''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="token != null and token!=''">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerUser">
    update cere_buyer_user
    <set>
      <if test="name != null and name!=''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null and sex!=''">
        sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null and birthday!=''">
        birthday = #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="wechatOpenId != null and wechatOpenId!=''">
        wechat_open_id = #{wechatOpenId,jdbcType=VARCHAR},
      </if>
      <if test="wechatUnionId != null and wechatUnionId!=''">
        wechat_union_id = #{wechatUnionId,jdbcType=VARCHAR},
      </if>
      <if test="wechatName != null and wechatName!=''">
        wechat_name = #{wechatName,jdbcType=VARCHAR},
      </if>
      <if test="wechatNumber != null and wechatNumber!=''">
        wechat_number = #{wechatNumber,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone!=''">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password!=''">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="headImage != null and headImage!=''">
        head_image = #{headImage,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="remark != null and remark!=''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="token != null and token!=''">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where buyer_user_id = #{buyerUserId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.buyer.CereBuyerUser">
    update cere_buyer_user
    set `name` = #{name,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=VARCHAR},
      birthday = #{birthday,jdbcType=VARCHAR},
      wechat_open_id = #{wechatOpenId,jdbcType=VARCHAR},
      wechat_union_id = #{wechatUnionId,jdbcType=VARCHAR},
      wechat_name = #{wechatName,jdbcType=VARCHAR},
      wechat_number = #{wechatNumber,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      head_image = #{headImage,jdbcType=VARCHAR},
      `state` = #{state,jdbcType=BIT},
      remark = #{remark,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where buyer_user_id = #{buyerUserId,jdbcType=BIGINT}
  </update>

  <update id="updateGrowth">
    update cere_buyer_user
    set growth = growth + #{growth}
    where buyer_user_id = #{buyerUserId}
  </update>

  <select id="getAll" parameterType="com.shop.cereshop.business.param.buyer.BuyerUserGetAllParam" resultType="com.shop.cereshop.business.page.buyer.BuyerUser">
    SELECT a.buyer_user_id,IF(a.wechat_name IS NULL,a.`name`,a.wechat_name) `name`,d.ids,
    a.phone,b.frequency,b.total,bbu.update_time as lastTime, bbu.create_time time,d.labelName from cere_buyer_user a
    JOIN cere_business_buyer_user bbu on bbu.buyer_user_id = a.buyer_user_id
    LEFT JOIN (SELECT COUNT(*) frequency,SUM(price) total,buyer_user_id from cere_shop_order where state in (2,3,4) and shop_id = #{shopId} GROUP BY buyer_user_id) b
    ON a.buyer_user_id=b.buyer_user_id
    LEFT JOIN (SELECT a.buyer_user_id,GROUP_CONCAT(b.label_name) labelName,GROUP_CONCAT(a.label_id) ids from
    cere_buyer_shop_label a,cere_shop_user_label b where a.label_id=b.label_id and b.shop_id=#{shopId} GROUP BY a.buyer_user_id) d
    ON a.buyer_user_id=d.buyer_user_id
    where bbu.shop_id = #{shopId}
    <if test="lastStartTime!=null and lastStartTime!=''">
      and bbu.update_time &gt;= #{lastStartTime} and bbu.update_time &lt;= #{lastEndTime}
    </if>
    <if test="startTime!=null and startTime!=''">
      and a.create_time&gt;=#{startTime} and a.create_time&lt;=#{endTime}
    </if>
    <if test="phone!=null and phone!=''">
      and a.phone like concat('%',#{phone},'%')
    </if>
    <if test="labelId!=null">
      and (d.ids like concat('%,',#{labelId},'%') OR d.ids=#{labelId})
    </if>
    GROUP BY a.buyer_user_id
    order by bbu.update_time desc
  </select>

  <select id="checkPhone" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.buyer.CereBuyerUser">
    SELECT phone FROM cere_buyer_user where phone=#{phone}
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.buyer.BuyerUserDetail">
    SELECT a.buyer_user_id,IF(a.wechat_name IS NULL,a.`name`,a.wechat_name) `name`,d.labelName,
    a.phone,b.frequency,b.total,c.lastTime,a.create_time time,IF(e.log_id IS NULL,'自动添加','手动添加') source,
    a.sex,a.birthday,a.remark,f.refundMoney,f.refunds from cere_buyer_user a
    LEFT JOIN (SELECT COUNT(*) frequency,SUM(price) total,buyer_user_id from cere_shop_order where state in (2,3,4) and shop_id = #{shopId} GROUP BY buyer_user_id) b
    ON a.buyer_user_id=b.buyer_user_id
    LEFT JOIN (SELECT buyer_user_id,create_time lastTime from cere_shop_order where state in (2,3,4) ORDER BY create_time DESC LIMIT 1) c
    ON a.buyer_user_id=c.buyer_user_id
    LEFT JOIN (SELECT a.buyer_user_id,GROUP_CONCAT(b.label_name) labelName,GROUP_CONCAT(a.label_id) labelIds from
    cere_buyer_shop_label a,cere_shop_user_label b where a.label_id=b.label_id GROUP BY a.buyer_user_id) d
    ON a.buyer_user_id=d.buyer_user_id
    LEFT JOIN cere_platform_log e ON a.buyer_user_id=e.only and e.operation_describtion='添加客户'
    LEFT JOIN (SELECT COUNT(*) refunds,SUM(b.money) refundMoney,a.buyer_user_id from cere_shop_order a,cere_order_reconciliation b
    where a.order_id=b.order_id and b.type=2 GROUP BY a.buyer_user_id) f ON a.buyer_user_id=f.buyer_user_id
    where a.buyer_user_id=#{buyerUserId}
    GROUP BY a.buyer_user_id
  </select>
  <select id="findVisitRecord" resultType="com.shop.cereshop.commons.domain.business.CereShopIdBuyerUserIdTimeDTO">
    select shop_id as shopId, buyer_user_id as buyerUserId, min(time) as time from
      (select shop_id,buyer_user_id,min(visit_time) time from cere_shop_visit
       group by shop_id, buyer_user_id
       union
       select shop_id,buyer_user_id,min(create_time) time from cere_shop_order
       group by shop_id, buyer_user_id) d
    group by shop_id, buyer_user_id
  </select>
  <select id="findConsumeRecord" resultType="com.shop.cereshop.commons.domain.business.CereShopIdBuyerUserIdTimeDTO">
    select a.shop_id as shopId, a.buyer_user_id as buyerUserId, b.create_time as time from cere_shop_order a
    join cere_pay_log b on a.order_formid = b.order_formid and b.state = '支付'
    group by a.shop_id, a.buyer_user_id
  </select>
</mapper>
