<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.activity.CereBuyerCouponDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.activity.CereBuyerCoupon">
    <id column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="discount_mode" jdbcType="BIT" property="discountMode" />
    <result column="discount_programme" jdbcType="BIT" property="discountProgramme" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="full_money" jdbcType="DECIMAL" property="fullMoney" />
    <result column="reduce_money" jdbcType="DECIMAL" property="reduceMoney" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.activity.CereBuyerCoupon">
    insert into cere_buyer_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="activityName != null and activityName!=''">
        activity_name,
      </if>
      <if test="startTime != null and startTime!=''">
        start_time,
      </if>
      <if test="endTime != null and endTime!=''">
        end_time,
      </if>
      <if test="discountMode != null">
        discount_mode,
      </if>
      <if test="discountProgramme != null">
        discount_programme,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="fullMoney != null">
        full_money,
      </if>
      <if test="reduceMoney != null">
        reduce_money,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="activityName != null and activityName!=''">
        #{activityName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null and startTime!=''">
        #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null and endTime!=''">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="discountMode != null">
        #{discountMode,jdbcType=BIT},
      </if>
      <if test="discountProgramme != null">
        #{discountProgramme,jdbcType=BIT},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="fullMoney != null">
        #{fullMoney,jdbcType=DECIMAL},
      </if>
      <if test="reduceMoney != null">
        #{reduceMoney,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByUserIdAndCouponId" parameterType="com.shop.cereshop.commons.domain.activity.CereBuyerCoupon" >
    UPDATE cere_buyer_coupon SET state=#{state},update_time=#{updateTime}
    where buyer_user_id=#{buyerUserId} and coupon_id=#{couponId}
  </update>

</mapper>
