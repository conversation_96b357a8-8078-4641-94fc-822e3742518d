<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.activity.CereBuyerToolDetailDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.activity.CereBuyerToolDetail">
    <result column="tool_id" jdbcType="BIGINT" property="toolId" />
    <result column="full_money" jdbcType="DECIMAL" property="fullMoney" />
    <result column="reduce_money" jdbcType="DECIMAL" property="reduceMoney" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.activity.CereBuyerToolDetail">
    insert into cere_buyer_coupon_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="toolId != null">
        tool_id,
      </if>
      <if test="fullMoney != null">
        full_money,
      </if>
      <if test="reduceMoney != null">
        reduce_money,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="toolId != null">
        #{toolId,jdbcType=BIGINT},
      </if>
      <if test="fullMoney != null">
        #{fullMoney,jdbcType=DECIMAL},
      </if>
      <if test="reduceMoney != null">
        #{reduceMoney,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
</mapper>
