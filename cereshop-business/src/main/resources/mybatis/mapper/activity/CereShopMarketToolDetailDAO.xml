<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.activity.CereShopMarketToolDetailDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.activity.CereShopMarketToolDetail">
    <result column="tool_id" jdbcType="BIGINT" property="toolId" />
    <result column="full_money" jdbcType="DECIMAL" property="fullMoney" />
    <result column="reduce_money" jdbcType="DECIMAL" property="reduceMoney" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.activity.CereShopMarketToolDetail">
    insert into cere_shop_market_tool_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="toolId != null">
        tool_id,
      </if>
      <if test="fullMoney != null">
        full_money,
      </if>
      <if test="reduceMoney != null">
        reduce_money,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="toolId != null">
        #{toolId,jdbcType=BIGINT},
      </if>
      <if test="fullMoney != null">
        #{fullMoney,jdbcType=DECIMAL},
      </if>
      <if test="reduceMoney != null">
        #{reduceMoney,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into cere_shop_market_tool_detail (tool_id, full_money, reduce_money) values
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.toolId},
      #{item.fullMoney},
      #{item.reduceMoney}
      )
    </foreach>
  </insert>

  <delete id="deleteByToolId" parameterType="java.lang.Object">
    DELETE FROM cere_shop_market_tool_detail where tool_id=#{toolId}
  </delete>

  <select id="findByToolId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.activity.CereShopMarketToolDetail">
    SELECT full_money,reduce_money FROM cere_shop_market_tool_detail where tool_id=#{toolId}
  </select>
</mapper>
