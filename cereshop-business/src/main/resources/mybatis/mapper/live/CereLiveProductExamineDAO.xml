<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.live.CereLiveProductExamineDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.live.CereLiveProductExamine">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="live_product_id" jdbcType="BIGINT" property="liveProductId"/>
        <result column="re_examine_date" jdbcType="DATE" property="reExamineDate"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, live_product_id, re_examine_date, create_time, update_time
    </sql>

</mapper>
