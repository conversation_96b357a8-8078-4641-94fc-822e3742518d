<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.shop.CereShopCommentDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.shop.CereShopComment">
    <id column="comment_id" jdbcType="BIGINT" property="commentId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="shop_code" jdbcType="VARCHAR" property="shopCode" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="add_image" jdbcType="VARCHAR" property="addImage" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="add_comment" jdbcType="VARCHAR" property="addComment" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="if_sensitive" jdbcType="BIT" property="ifSensitive" />
    <result column="add_time" jdbcType="VARCHAR" property="addTime" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <insert id="insertSelective" keyColumn="comment_id" keyProperty="commentId" parameterType="com.shop.cereshop.commons.domain.shop.CereShopComment" useGeneratedKeys="true">
    insert into cere_shop_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="shopName != null and shopName!=''">
        shop_name,
      </if>
      <if test="shopCode != null and shopCode!=''">
        shop_code,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="addImage != null and addImage!=''">
        add_image,
      </if>
      <if test="comment != null and comment!=''">
        `comment`,
      </if>
      <if test="addComment != null and addComment!=''">
        add_comment,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="ifSensitive != null">
        if_sensitive,
      </if>
      <if test="addTime != null and addTime!=''">
        add_time,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="shopName != null and shopName!=''">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="shopCode != null and shopCode!=''">
        #{shopCode,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="addImage != null and addImage!=''">
        #{addImage,jdbcType=VARCHAR},
      </if>
      <if test="comment != null and comment!=''">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="addComment != null and addComment!=''">
        #{addComment,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="ifSensitive != null">
        #{ifSensitive,jdbcType=BIT},
      </if>
      <if test="addTime != null and addTime!=''">
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
