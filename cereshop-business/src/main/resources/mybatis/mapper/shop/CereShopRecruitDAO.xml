<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.shop.CereShopRecruitDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.shop.CereShopRecruit">
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="purchase_everything" jdbcType="BIT" property="purchaseEverything" />
    <result column="order_frequency" jdbcType="BIT" property="orderFrequency" />
    <result column="consumption_money" jdbcType="BIT" property="consumptionMoney" />
    <result column="frequency" jdbcType="INTEGER" property="frequency" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="if_examine" jdbcType="BIT" property="ifExamine" />
    <result column="url" jdbcType="VARCHAR" property="url" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.shop.CereShopRecruit">
    insert into cere_shop_recruit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="purchaseEverything != null">
        purchase_everything,
      </if>
      <if test="orderFrequency != null">
        order_frequency,
      </if>
      <if test="consumptionMoney != null">
        consumption_money,
      </if>
      <if test="frequency != null">
        frequency,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="ifExamine != null">
        if_examine,
      </if>
      <if test="url != null and url!=''">
        url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="purchaseEverything != null">
        #{purchaseEverything,jdbcType=BIT},
      </if>
      <if test="orderFrequency != null">
        #{orderFrequency,jdbcType=BIT},
      </if>
      <if test="consumptionMoney != null">
        #{consumptionMoney,jdbcType=BIT},
      </if>
      <if test="frequency != null">
        #{frequency,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="ifExamine != null">
        #{ifExamine,jdbcType=BIT},
      </if>
      <if test="url != null and url!=''">
        #{url,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateData" parameterType="com.shop.cereshop.commons.domain.shop.CereShopRecruit">
    update cere_shop_recruit
    <set>
        purchase_everything = #{purchaseEverything,jdbcType=BIT},
        order_frequency = #{orderFrequency,jdbcType=BIT},
        consumption_money = #{consumptionMoney,jdbcType=BIT},
        frequency = #{frequency,jdbcType=INTEGER},
        money = #{money,jdbcType=DECIMAL},
        if_examine = #{ifExamine,jdbcType=BIT},
        url = #{url,jdbcType=VARCHAR},
    </set>
    where shop_id = #{shopId,jdbcType=BIGINT}
  </update>

  <select id="getByShopId" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.param.recruit.ShopRecruitParam">
    SELECT purchase_everything, order_frequency,
    consumption_money, frequency, money,
    if_examine, url,
    IF(purchase_everything=1,1,IF(order_frequency=1,2,IF(consumption_money=1,3,0))) `condition`
    FROM cere_shop_recruit where shop_id=#{shopId}
  </select>

  <select id="findStayDistributor" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.distributor.CereShopDistributor">
    SELECT * FROM cere_shop_distributor where `state`=0 and shop_id=#{shopId}
  </select>
</mapper>
