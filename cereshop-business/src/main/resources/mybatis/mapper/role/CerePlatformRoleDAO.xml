<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.role.CerePlatformRoleDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.role.CerePlatformRole">
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="role_describe" jdbcType="VARCHAR" property="roleDescribe" />
    <result column="project" jdbcType="BIGINT" property="project" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    role_id, role_name, role_describe,project, create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_role
    where role_id = #{roleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_platform_role
    where role_id = #{roleId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="role_id" keyProperty="roleId" parameterType="com.shop.cereshop.commons.domain.role.CerePlatformRole" useGeneratedKeys="true">
    insert into cere_platform_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleName != null and roleName!=''">
        role_name,
      </if>
      <if test="roleDescribe != null and roleDescribe!=''">
        role_describe,
      </if>
      <if test="project!=null">
        project,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleName != null and roleName!=''">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="roleDescribe != null and roleDescribe!=''">
        #{roleDescribe,jdbcType=VARCHAR},
      </if>
      <if test="project!=null">
        #{project,jdbcType=BIGINT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.role.CerePlatformRole">
    update cere_platform_role
    <set>
      <if test="roleName != null and roleName!=''">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="roleDescribe != null and roleDescribe!=''">
        role_describe = #{roleDescribe,jdbcType=VARCHAR},
      </if>
      <if test="project!=null">
        project = #{project,jdbcType=BIGINT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where role_id = #{roleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.role.CerePlatformRole">
    update cere_platform_role
    set role_name = #{roleName,jdbcType=VARCHAR},
      role_describe = #{roleDescribe,jdbcType=VARCHAR},
      project = #{project,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where role_id = #{roleId,jdbcType=BIGINT}
  </update>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.role.CerePlatformRole">
    SELECT role_id,role_name,role_describe FROM cere_platform_role where role_id=#{roleId}
  </select>

  <select id="getAll" parameterType="com.shop.cereshop.business.param.role.RoleGetAllParam" resultType="com.shop.cereshop.commons.domain.role.CerePlatformRole">
    SELECT role_id,role_name,role_describe FROM cere_platform_role
    where project=#{shopId}
    <if test="search!=null and search!=''">
      and (role_name like concat('%',#{search},'%') or
      role_describe like concat('%',#{search},'%'))
    </if>
    ORDER BY update_time DESC,create_time DESC
  </select>
</mapper>
