<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.role.CerePlatformRolePermissionDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.role.CerePlatformRolePermission">
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="permission_id" jdbcType="BIGINT" property="permissionId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.role.CerePlatformRolePermission">
    insert into cere_platform_role_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="permissionId != null">
        permission_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="permissionId != null">
        #{permissionId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <delete id="deleteByRoleId" parameterType="java.lang.Object" >
    DELETE FROM cere_platform_role_permission where role_id=#{roleId}
  </delete>

  <delete id="deleteByPermissionId" parameterType="java.lang.Object" >
    DELETE FROM cere_platform_role_permission where permission_id=#{permissionId}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into cere_platform_role_permission (role_id, permission_id) values
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.roleId},
      #{item.permissionId}
      )
    </foreach>
  </insert>

  <select id="check" parameterType="java.lang.Object" resultType="java.lang.Long">
    SELECT b.permission_id FROM cere_platform_role_permission a
    LEFT JOIN cere_platform_permission b ON a.permission_id=b.permission_id
    where a.role_id=#{roleId} and b.permission_name='营销活动'
    GROUP BY b.permission_id
  </select>
</mapper>
