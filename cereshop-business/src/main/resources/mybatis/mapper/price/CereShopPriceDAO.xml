<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.price.CereShopPriceDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereShopPrice">
    <id column="price_id" jdbcType="BIGINT" property="priceId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="compose_name" jdbcType="VARCHAR" property="composeName" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    price_id, shop_id, compose_name, start_time, end_time,`state`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_shop_price
    where price_id = #{priceId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_shop_price
    where price_id = #{priceId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="price_id" keyProperty="priceId" parameterType="com.shop.cereshop.commons.domain.tool.CereShopPrice" useGeneratedKeys="true">
    insert into cere_shop_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="composeName != null">
        compose_name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="composeName != null">
        #{composeName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.tool.CereShopPrice">
    update cere_shop_price
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="composeName != null">
        compose_name = #{composeName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where price_id = #{priceId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.tool.CereShopPrice">
    update cere_shop_price
    set shop_id = #{shopId,jdbcType=BIGINT},
      compose_name = #{composeName,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=VARCHAR},
      end_time = #{endTime,jdbcType=VARCHAR},
      `state` = #{state,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where price_id = #{priceId,jdbcType=BIGINT}
  </update>

  <select id="checkTime" parameterType="com.shop.cereshop.business.param.price.PriceSaveParam" resultType="com.shop.cereshop.commons.domain.tool.CereShopPrice">
    SELECT price_id
    from cere_shop_price
    where ((start_time &lt;= #{startTime} and end_time &gt;= #{startTime})
      OR (start_time &lt;= #{endTime} and end_time &gt;= #{endTime}))
      and state NOT IN (2,3) and shop_id=#{shopId}
  </select>

  <select id="checkUpdateTime" parameterType="com.shop.cereshop.business.param.price.PriceUpdateParam" resultType="com.shop.cereshop.commons.domain.tool.CereShopPrice">
    SELECT price_id
    from cere_shop_price
    where ((start_time &lt;= #{startTime} and end_time &gt;= #{startTime})
      OR (start_time &lt;= #{endTime} and end_time &gt;= #{endTime}))
      and state NOT IN (2,3) and shop_id=#{shopId} and price_id<![CDATA[!= ]]>#{priceId}
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.business.page.price.ShopPriceDetail">
    SELECT * FROM cere_shop_price where price_id=#{priceId}
  </select>

  <select id="getAll" parameterType="com.shop.cereshop.business.param.price.PriceGetAllParam" resultType="com.shop.cereshop.business.page.price.ShopPrice">
    SELECT a.price_id,a.compose_name,CONCAT(start_time,'至',end_time) time,
    b.number,c.rules,a.state FROM cere_shop_price a
    LEFT JOIN (SELECT COUNT(product_id) number,price_id FROM cere_price_product GROUP BY price_id) b
    ON a.price_id=b.price_id
    LEFT JOIN (SELECT GROUP_CONCAT('任选',number,'件',price,'元') rules,price_id FROM cere_price_rule GROUP BY price_id) c
    ON a.price_id=c.price_id
    where a.shop_id=#{shopId}
  </select>

  <select id="getPrices" parameterType="com.shop.cereshop.business.param.renovation.RenovationParam" resultType="com.shop.cereshop.business.page.price.ShopPriceDetail">
    SELECT price_id, shop_id, compose_name, start_time, end_time,`state` FROM cere_shop_price
    where shop_id=#{shopId} and state in (0,1)
    <if test="ids!=null and ids.size()>0">
      and price_id in (
      <foreach collection="ids" item="id" index="index" separator=",">
        #{id}
      </foreach>
      )
    </if>
  </select>

  <select id="findProductPrice" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
    SELECT MIN(price) FROM cere_product_sku where product_id in (
    <foreach collection="list" item="item" index="index" separator=",">
      #{item.productId}
    </foreach>
    )
    GROUP BY product_id
    ORDER BY MIN(price)
  </select>
</mapper>
