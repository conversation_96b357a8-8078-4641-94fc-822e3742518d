<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.business.dao.distributor.CereDistributorBuyerDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.distributor.CereDistributorBuyer">
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="if_bind" jdbcType="BIT" property="ifBind" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.distributor.CereDistributorBuyer">
    insert into cere_distributor_buyer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="ifBind != null">
        if_bind,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="ifBind != null">
        #{ifBind,jdbcType=BIT},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateData" parameterType="com.shop.cereshop.commons.domain.distributor.CereDistributorBuyer" >
    UPDATE cere_distributor_buyer set if_bind=#{ifBind} where buyer_user_id=#{buyerUserId} and distributor_id=#{distributorId} and shop_id=#{shopId}
  </update>

  <delete id="deleteData" parameterType="com.shop.cereshop.commons.domain.distributor.CereDistributorBuyer">
    DELETE FROM cere_distributor_buyer where buyer_user_id=#{buyerUserId} and distributor_id=#{distributorId} and shop_id=#{shopId}
  </delete>

  <select id="findBuyer" parameterType="com.shop.cereshop.commons.domain.distributor.CereDistributorBuyer" resultType="com.shop.cereshop.commons.domain.distributor.CereDistributorBuyer">
    SELECT shop_id, distributor_id, buyer_user_id FROM cere_distributor_buyer
    where buyer_user_id=#{buyerUserId} and distributor_id=#{distributorId} and shop_id=#{shopId}
  </select>

  <select id="findByUserIdAndDistributorId" parameterType="com.shop.cereshop.business.param.ship.ShipBindParam" resultType="com.shop.cereshop.commons.domain.distributor.CereDistributorBuyer">
    SELECT * FROM cere_distributor_buyer
    where buyer_user_id=#{buyerUserId} and distributor_id<![CDATA[!= ]]>#{distributorId} and if_bind=1
  </select>
</mapper>
