<assembly>
    <id>package</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>src/main/resources</directory>
            <includes>
                <include>*.properties</include>
                <include>*.yml</include>
                <include>*.xml</include>
            </includes>
            <filtered>true</filtered>
            <outputDirectory>config</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <includes>
                <include>startup.sh</include>
                <include>startup.bat</include>
            </includes>
            <outputDirectory></outputDirectory>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <includes>
                <include>startup.sh</include>
            </includes>
            <outputDirectory></outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <includes>
                <include>*.jar</include>
            </includes>
            <outputDirectory></outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/lib</directory>
            <includes>
                <include>*.jar</include>
            </includes>
            <outputDirectory>lib</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>
