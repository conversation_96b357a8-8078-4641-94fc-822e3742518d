<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.admin.AdminDAO">

  <select id="selectUserCount" resultType="com.shop.cereshop.admin.param.admin.StatsByDay">
    SELECT date(create_time) as statsDate, count(buyer_user_id) as totalCount
    from cere_buyer_user
    where create_time >= #{startTime} and create_time &lt; #{endTime}
    group by date(create_time)
  </select>

  <select id="selectVisitCount" resultType="com.shop.cereshop.admin.param.admin.StatsByDay">
    SELECT date(create_time) as statsDate, count(*) as totalCount
    from cere_buyer_footprint
    where create_time >= #{startTime} and create_time &lt; #{endTime}
    group by date(create_time)
  </select>

  <select id="selectVisitUserCount" resultType="com.shop.cereshop.admin.param.admin.StatsByDay">
    SELECT date(create_time) as statsDate, count(distinct buyer_user_id) as totalCount
    from cere_buyer_footprint
    where create_time >= #{startTime} and create_time &lt; #{endTime}
    group by date(create_time)
  </select>

  <select id="selectShopCount" resultType="com.shop.cereshop.admin.param.admin.StatsByDay">
    SELECT date(create_time) as statsDate, count(check_id) as totalCount
    from cere_shop_check
    where create_time >= #{startTime} and create_time &lt; #{endTime}
    group by date(create_time)
  </select>

  <select id="selectOrderAmountList" resultType="com.shop.cereshop.admin.param.admin.StatsAmountByDay">
    select date(create_time) as statsDate, sum(price) as amount
    from cere_shop_order
    where create_time >= #{startTime} and create_time &lt; #{endTime}
    and state in (2, 3, 4, 6, 7)
    group by date(create_time)
  </select>

  <select id="selectOrderCountList" resultType="com.shop.cereshop.admin.param.admin.StatsByDay">
    select date(create_time) as statsDate, count(order_id) as totalCount
    from cere_shop_order
    where create_time >= #{startTime} and create_time &lt; #{endTime}
    group by date(create_time)
  </select>

  <select id="selectOrderCountSum" resultType="java.lang.Integer">
    select count(order_id)
    from cere_shop_order
    where create_time >= #{startTime} and create_time &lt; #{endTime}
  </select>

  <select id="selectPayUserCountList" resultType="com.shop.cereshop.admin.param.admin.StatsByDay">
    select date(create_time) as statsDate, count(distinct buyer_user_id) as totalCount
    from cere_shop_order
    where create_time >= #{startTime} and create_time &lt; #{endTime}
    and state in (2, 3, 4, 6, 7)
    group by date(create_time)
  </select>

  <select id="selectPayUserCountSum" resultType="java.lang.Integer">
    select count(distinct buyer_user_id)
    from cere_shop_order
    where create_time >= #{startTime} and create_time &lt; #{endTime}
    and state in (2, 3, 4, 6, 7)
  </select>

</mapper>
