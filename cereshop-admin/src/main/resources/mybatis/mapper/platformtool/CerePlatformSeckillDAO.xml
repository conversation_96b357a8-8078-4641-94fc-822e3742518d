<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.platformtool.CerePlatformSeckillDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.platformtool.CerePlatformSeckill">
        <id column="seckill_id" jdbcType="BIGINT" property="seckillId"/>
        <result column="seckill_name" jdbcType="VARCHAR" property="seckillName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="sign_start_time" jdbcType="VARCHAR" property="signStartTime"/>
        <result column="sign_end_time" jdbcType="VARCHAR" property="signEndTime"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="if_limit" jdbcType="BIT" property="ifLimit"/>
        <result column="limit_number" jdbcType="INTEGER" property="limitNumber"/>
        <result column="if_add" jdbcType="BIT" property="ifAdd"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="if_bond" jdbcType="BIT" property="ifBond"/>
        <result column="bond_money" jdbcType="DECIMAL" property="bondMoney"/>
        <result column="seckill_money" jdbcType="DECIMAL" property="seckillMoney"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        seckill_id
        , seckill_name, remark, sign_start_time, sign_end_time, start_time, end_time,
    if_limit, limit_number, if_add, `state`, if_bond, bond_money,seckill_money, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cere_platform_seckill
        where seckill_id = #{seckillId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cere_platform_seckill
        where seckill_id = #{seckillId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="seckill_id" keyProperty="seckillId"
            parameterType="com.shop.cereshop.commons.domain.platformtool.CerePlatformSeckill" useGeneratedKeys="true">
        insert into cere_platform_seckill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seckillName != null">
                seckill_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="signStartTime != null">
                sign_start_time,
            </if>
            <if test="signEndTime != null">
                sign_end_time,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="ifLimit != null">
                if_limit,
            </if>
            <if test="limitNumber != null">
                limit_number,
            </if>
            <if test="ifAdd != null">
                if_add,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="ifBond != null">
                if_bond,
            </if>
            <if test="bondMoney != null">
                bond_money,
            </if>
            <if test="seckillMoney != null">
                seckill_money,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seckillName != null">
                #{seckillName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="signStartTime != null">
                #{signStartTime,jdbcType=VARCHAR},
            </if>
            <if test="signEndTime != null">
                #{signEndTime,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="ifLimit != null">
                #{ifLimit,jdbcType=BIT},
            </if>
            <if test="limitNumber != null">
                #{limitNumber,jdbcType=INTEGER},
            </if>
            <if test="ifAdd != null">
                #{ifAdd,jdbcType=BIT},
            </if>
            <if test="state != null">
                #{state,jdbcType=BIT},
            </if>
            <if test="ifBond != null">
                #{ifBond,jdbcType=BIT},
            </if>
            <if test="bondMoney != null">
                #{bondMoney,jdbcType=DECIMAL},
            </if>
            <if test="seckillMoney != null">
                #{seckillMoney,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.shop.cereshop.commons.domain.platformtool.CerePlatformSeckill">
        update cere_platform_seckill
        <set>
            <if test="seckillName != null">
                seckill_name = #{seckillName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="signStartTime != null">
                sign_start_time = #{signStartTime,jdbcType=VARCHAR},
            </if>
            <if test="signEndTime != null">
                sign_end_time = #{signEndTime,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="ifLimit != null">
                if_limit = #{ifLimit,jdbcType=BIT},
            </if>
            <if test="limitNumber != null">
                limit_number = #{limitNumber,jdbcType=INTEGER},
            </if>
            <if test="ifAdd != null">
                if_add = #{ifAdd,jdbcType=BIT},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=BIT},
            </if>
            <if test="ifBond != null">
                if_bond = #{ifBond,jdbcType=BIT},
            </if>
            <if test="bondMoney != null">
                bond_money = #{bondMoney,jdbcType=DECIMAL},
            </if>
            <if test="seckillMoney != null">
                seckill_money = #{seckillMoney,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where seckill_id = #{seckillId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.platformtool.CerePlatformSeckill">
        update cere_platform_seckill
        set seckill_name    = #{seckillName,jdbcType=VARCHAR},
            remark          = #{remark,jdbcType=VARCHAR},
            sign_start_time = #{signStartTime,jdbcType=VARCHAR},
            sign_end_time   = #{signEndTime,jdbcType=VARCHAR},
            start_time      = #{startTime,jdbcType=VARCHAR},
            end_time        = #{endTime,jdbcType=VARCHAR},
            if_limit        = #{ifLimit,jdbcType=BIT},
            limit_number    = #{limitNumber,jdbcType=INTEGER},
            if_add          = #{ifAdd,jdbcType=BIT},
            `state`         = #{state,jdbcType=BIT},
            if_bond         = #{ifBond,jdbcType=BIT},
            bond_money      = #{bondMoney,jdbcType=DECIMAL},
            seckill_money   = #{seckillMoney,jdbcType=DECIMAL},
            create_time     = #{createTime,jdbcType=VARCHAR},
            update_time     = #{updateTime,jdbcType=VARCHAR}
        where seckill_id = #{seckillId,jdbcType=BIGINT}
    </update>

    <select id="checkTime" parameterType="com.shop.cereshop.admin.param.seckill.SeckillSaveParam"
            resultType="com.shop.cereshop.commons.domain.platformtool.CerePlatformSeckill">
        SELECT seckill_id
        from cere_platform_seckill
        where ((start_time &lt;= #{startTime} and end_time &gt;= #{startTime})
            OR (start_time &lt;= #{endTime} and end_time &gt;= #{endTime}))
          and state<![CDATA[!= ]]>4
    </select>

    <select id="checkUpdateTime" parameterType="com.shop.cereshop.admin.param.seckill.SeckillUpdateParam"
            resultType="com.shop.cereshop.commons.domain.platformtool.CerePlatformSeckill">
        SELECT seckill_id
        from cere_platform_seckill
        where (start_time &lt;= #{startTime} and end_time &gt;= #{startTime}
            OR start_time &lt;= #{endTime} and end_time &gt;= #{endTime})
          and state<![CDATA[!= ]]>4 and seckill_id<![CDATA[!= ]]>#{seckillId}
    </select>

    <select id="findShops" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM cere_activity_sign
        where activity_id = #{seckillId}
          and state = 1
          and sign_type = 2
    </select>

    <select id="findProducts" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(a.product_id) FROM cere_sign_product a
        LEFT JOIN cere_activity_sign b ON a.sign_id=b.sign_id
        where b.state=1 and b.activity_id=#{seckillId} and b.sign_type=2
        <if test="shopId!=null">
            and b.shop_id=#{shopId}
        </if>
    </select>

    <select id="findVisit" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*) from cere_buyer_platform_seckill_visit where seckill_id=#{seckillId}
        <if test="shopId!=null">
            and shop_id=#{shopId}
        </if>
        GROUP BY seckill_id
    </select>

    <select id="findPays" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from (SELECT * FROM cere_shop_order where seckill_id = #{seckillId}
        and state in (2, 3, 4) GROUP BY buyer_user_id) a
    </select>

    <select id="findTotal" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(price) IS NULL,0,SUM(price)) from cere_shop_order
        where seckill_id=#{seckillId} and state in (2,3,4)
        <if test="shopId!=null">
            and shop_id=#{shopId}
        </if>
    </select>

    <select id="findOrders" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT count(order_id) FROM cere_shop_order where payment_state=1 and seckill_id=#{seckillId} and state in
        (2,3,4)
        <if test="shopId!=null">
            and shop_id=#{shopId}
        </if>
    </select>

    <select id="findSubmit" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT count(order_id)
        FROM cere_shop_order
        where seckill_id = #{seckillId}
          and shop_id = #{shopId}
    </select>

    <select id="findShopDatas" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.shop.ShopDataDetail">
        SELECT a.shop_id, b.shop_code,b.shop_name
        FROM cere_activity_sign a
                 LEFT JOIN cere_platform_shop b ON a.shop_id = b.shop_id
        where a.activity_id = #{seckillId}
          and a.state = 1
          and a.sign_type = 2
    </select>

    <select id="getAll" parameterType="com.shop.cereshop.admin.param.seckill.SeckillGetAllParam"
            resultType="com.shop.cereshop.admin.page.seckill.Seckill">
        SELECT seckill_id,seckill_name,CONCAT('直降',seckill_money,'元') content,
        CONCAT(sign_start_time,'~',sign_end_time) signTime,CONCAT(start_time,'~',end_time) time,
        state FROM cere_platform_seckill
        where 1=1
        <if test="seckillName!=null and seckillName!=''">
            and seckill_name like concat('%',#{seckillName},'%')
        </if>
        <if test="state!=null">
            and state=#{state}
        </if>
        ORDER BY update_time desc,create_time desc
    </select>

    <select id="getShops" parameterType="com.shop.cereshop.admin.param.seckill.SeckillShopParam"
            resultType="com.shop.cereshop.admin.page.seckill.SeckillShop">
        SELECT a.sign_id,a.shop_id,b.shop_name,b.shop_code,c.productNumber products,a.activity_id seckillId,
        a.state from (SELECT a.* FROM (SELECT sign_id,shop_id,state,activity_id,sign_type from
        cere_activity_sign ORDER BY create_time DESC) a GROUP BY a.shop_id,a.activity_id) a
        LEFT JOIN cere_platform_shop b ON a.shop_id=b.shop_id
        LEFT JOIN (SELECT COUNT(product_id) productNumber,sign_id from cere_sign_product
        GROUP BY sign_id) c ON a.sign_id=c.sign_id
        where a.activity_id=#{seckillId} and a.sign_type=2
        <if test="shopName!=null and shopName!=''">
            and b.shop_name like concat('%',#{shopName},'%')
        </if>
        <if test="shopCode!=null and shopCode!=''">
            and b.shop_code like concat('%',#{shopCode},'%')
        </if>
        <if test="state!=null">
            and a.state=#{state}
        </if>
        GROUP BY a.shop_id
    </select>

    <select id="findExamines" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT count(*)
        FROM cere_activity_sign
        where activity_id = #{seckillId}
          and shop_id = #{shopId}
          and sign_type = 2
          and state<![CDATA[!= ]]>0
    </select>

    <select id="getProducts" parameterType="com.shop.cereshop.admin.param.seckill.SeckillGetProductsParam"
            resultType="com.shop.cereshop.admin.page.seckill.SeckillShopProduct">
        SELECT a.product_id,
               b.product_name,
               c.product_image                                                         image,
               d.shop_name,
               CONCAT('￥', g.price - f.seckill_money, '~￥', h.price - f.seckill_money) sectionPrice,
               CONCAT('￥', g.price, '~￥', h.price)                                     originalPrice,
               g.stock_number,
               ifnull(SUM(m.number), 0)                                                          volume,
               a.number
        FROM cere_sign_product a
                 LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) c ON a.product_id = c.product_id
                 LEFT JOIN cere_platform_shop d ON b.shop_id = d.shop_id
                 LEFT JOIN cere_activity_sign e ON a.sign_id = e.sign_id
                 LEFT JOIN cere_platform_seckill f ON e.activity_id = f.seckill_id
                 LEFT JOIN (SELECT a.product_id,
                                   a.sku_id,
                                   MIN(a.price)        price,
                                   SUM(a.stock_number) stock_number
                            from cere_product_sku a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) g ON a.product_id = g.product_id
                 LEFT JOIN (SELECT a.product_id, a.sku_id, MAX(a.price) price
                            from cere_product_sku a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) h ON a.product_id = h.product_id
                 LEFT JOIN cere_order_product m ON a.product_id = m.product_id
        where a.sign_id = #{signId}
        GROUP BY m.product_id
    </select>

    <select id="findBySeckill" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.activity.CereActivitySign">
        SELECT a.*
        FROM (SELECT * FROM cere_activity_sign ORDER BY create_time DESC) a
        where a.activity_id = #{seckIllId}
          and state = 1
          and sign_type = 2
        GROUP BY a.sign_id
    </select>

    <select id="findErrorSign" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.activity.CereActivitySign">
        SELECT a.*
        FROM (SELECT * FROM cere_activity_sign ORDER BY create_time DESC) a
        where a.activity_id = #{seckIllId}
          and state in (0, 2)
          and sign_type = 2
        GROUP BY a.sign_id
    </select>

    <select id="getPlatformSeckills" parameterType="com.shop.cereshop.admin.param.canvas.RenovationParam"
            resultType="com.shop.cereshop.admin.page.canvas.CanvasPlatformSeckill">
        SELECT * FROM cere_platform_seckill where state in (2,3)
        <if test="ids!=null and ids.size()>0">
            and seckill_id in (
            <foreach collection="ids" item="id" index="index" separator=",">
                #{id}
            </foreach>
            )
        </if>
        ORDER BY start_time
    </select>

    <select id="findSeckillProduct" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.admin.page.canvas.ToolProduct">
        SELECT b.product_id,
               b.product_name,
               c.original_price,
               e.product_image           image,
               c.stock_number,
               c.original_price - e.seckill_money price,
               c.sku_id,
               e.seckill_money           discount,
               c.total
        from cere_sign_product a
                 LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
                 LEFT JOIN (SELECT a.sku_id, a.product_id, a.stock_number, a.original_price, a.price, a.total
                            FROM cere_product_sku a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) c ON a.product_id = c.product_id
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) e
                           ON b.product_id = e.product_id
                 LEFT JOIN cere_activity_sign d ON a.sign_id = d.sign_id and sign_type = 2
                 LEFT JOIN cere_platform_seckill e ON d.activity_id = e.seckill_id
        where e.seckill_id = #{seckillId}
        GROUP BY a.product_id
    </select>
    <select id="findProductIdList" resultType="java.lang.Long">
        select c.product_id from cere_platform_seckill a
        join cere_activity_sign b on b.activity_id = a.seckill_id and b.sign_type = 2
        join cere_sign_product c on c.sign_id = b.sign_id
        where a.seckill_id = #{seckIllId}
    </select>
</mapper>
