<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.distributor.CereShopDistributorDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.distributor.CereShopDistributor">
    <id column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="distributor_name" jdbcType="VARCHAR" property="distributorName" />
    <result column="distributor_phone" jdbcType="VARCHAR" property="distributorPhone" />
    <result column="distributor_level_id" jdbcType="BIGINT" property="distributorLevelId" />
    <result column="invitees" jdbcType="BIGINT" property="invitees" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="if_Liquidation" jdbcType="BIT" property="ifLiquidation" />
    <result column="join_time" jdbcType="VARCHAR" property="joinTime" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    distributor_id, shop_id, distributor_name, distributor_phone, distributor_level_id,
    invitees, `state`, if_Liquidation, join_time, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_shop_distributor
    where distributor_id = #{distributorId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_shop_distributor
    where distributor_id = #{distributorId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="distributor_id" keyProperty="distributorId" parameterType="com.shop.cereshop.commons.domain.distributor.CereShopDistributor" useGeneratedKeys="true">
    insert into cere_shop_distributor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="distributorName != null">
        distributor_name,
      </if>
      <if test="distributorPhone != null">
        distributor_phone,
      </if>
      <if test="distributorLevelId != null">
        distributor_level_id,
      </if>
      <if test="invitees != null">
        invitees,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="ifLiquidation != null">
        if_Liquidation,
      </if>
      <if test="joinTime != null and joinTime!=''">
        join_time,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="distributorName != null">
        #{distributorName,jdbcType=VARCHAR},
      </if>
      <if test="distributorPhone != null">
        #{distributorPhone,jdbcType=VARCHAR},
      </if>
      <if test="distributorLevelId != null">
        #{distributorLevelId,jdbcType=BIGINT},
      </if>
      <if test="invitees != null">
        #{invitees,jdbcType=BIGINT},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="ifLiquidation != null">
        #{ifLiquidation,jdbcType=BIT},
      </if>
      <if test="joinTime != null and joinTime!=''">
        #{joinTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.distributor.CereShopDistributor">
    update cere_shop_distributor
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="distributorName != null">
        distributor_name = #{distributorName,jdbcType=VARCHAR},
      </if>
      <if test="distributorPhone != null">
        distributor_phone = #{distributorPhone,jdbcType=VARCHAR},
      </if>
      <if test="distributorLevelId != null">
        distributor_level_id = #{distributorLevelId,jdbcType=BIGINT},
      </if>
      <if test="invitees != null">
        invitees = #{invitees,jdbcType=BIGINT},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="ifLiquidation != null">
        if_Liquidation = #{ifLiquidation,jdbcType=BIT},
      </if>
      <if test="joinTime != null and joinTime!=''">
        join_time = #{joinTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where distributor_id = #{distributorId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.distributor.CereShopDistributor">
    update cere_shop_distributor
    set shop_id = #{shopId,jdbcType=BIGINT},
      distributor_name = #{distributorName,jdbcType=VARCHAR},
      distributor_phone = #{distributorPhone,jdbcType=VARCHAR},
      distributor_level_id = #{distributorLevelId,jdbcType=BIGINT},
      invitees = #{invitees,jdbcType=BIGINT},
      `state` = #{state,jdbcType=BIT},
      if_Liquidation = #{ifLiquidation,jdbcType=BIT},
      join_time = #{joinTime,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where distributor_id = #{distributorId,jdbcType=BIGINT}
  </update>
</mapper>
