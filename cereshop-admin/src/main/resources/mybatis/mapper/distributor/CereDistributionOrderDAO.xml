<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.distributor.CereDistributionOrderDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.distributor.CereDistributionOrder">
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_formid" jdbcType="VARCHAR" property="orderFormid" />
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="distributor_name" jdbcType="VARCHAR" property="distributorName" />
    <result column="distributor_phone" jdbcType="VARCHAR" property="distributorPhone" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="transaction_time" jdbcType="VARCHAR" property="transactionTime" />
    <result column="type" jdbcType="BIT" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    order_id, order_formid, distributor_id, distributor_name, distributor_phone, price,
    commission, `state`, transaction_time, `type`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_distribution_order
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_distribution_order
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.distributor.CereDistributionOrder">
    insert into cere_distribution_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderFormid != null and orderFormid!=''">
        order_formid,
      </if>
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="distributorName != null">
        distributor_name,
      </if>
      <if test="distributorPhone != null">
        distributor_phone,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="commission != null">
        commission,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="transactionTime != null">
        transaction_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderFormid != null and orderFormid!=''">
        #{orderFormid,jdbcType=VARCHAR},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="distributorName != null">
        #{distributorName,jdbcType=VARCHAR},
      </if>
      <if test="distributorPhone != null">
        #{distributorPhone,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        #{commission,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="transactionTime != null">
        #{transactionTime,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.distributor.CereDistributionOrder">
    update cere_distribution_order
    <set>
      <if test="orderFormid != null and orderFormid!=''">
        order_formid = #{orderFormid,jdbcType=VARCHAR},
      </if>
      <if test="distributorId != null">
        distributor_id = #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="distributorName != null">
        distributor_name = #{distributorName,jdbcType=VARCHAR},
      </if>
      <if test="distributorPhone != null">
        distributor_phone = #{distributorPhone,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="commission != null">
        commission = #{commission,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="transactionTime != null">
        transaction_time = #{transactionTime,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BIT},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.distributor.CereDistributionOrder">
    update cere_distribution_order
    set order_formid = #{orderFormid,jdbcType=VARCHAR},
      distributor_id = #{distributorId,jdbcType=BIGINT},
      distributor_name = #{distributorName,jdbcType=VARCHAR},
      distributor_phone = #{distributorPhone,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      commission = #{commission,jdbcType=DECIMAL},
      `state` = #{state,jdbcType=BIT},
      transaction_time = #{transactionTime,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=BIT}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>

</mapper>
