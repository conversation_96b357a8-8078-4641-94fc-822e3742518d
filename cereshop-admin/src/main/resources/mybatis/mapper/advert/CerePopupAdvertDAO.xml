<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.advert.CerePopupAdvertDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.advert.CerePopupAdvert">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="popup_img" jdbcType="VARCHAR" property="popupImg"/>
        <result column="close_img" jdbcType="VARCHAR" property="closeImg"/>
        <result column="jump_type" jdbcType="INTEGER" property="jumpType"/>
        <result column="jump_content" jdbcType="VARCHAR" property="jumpContent"/>
        <result column="trigger_condition" jdbcType="INTEGER" property="triggerCondition"/>
        <result column="apply_group" jdbcType="INTEGER" property="applyGroup"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, start_time, end_time, popup_img, close_img, jump_type,
        jump_content, trigger_condition, apply_group, state, create_time, update_time
    </sql>

    <select id="getAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from cere_popup_advert
        <where>
            <if test="name != null">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="checkConflict" resultType="java.lang.Integer">
        select count(id) from cere_popup_advert
        where state = 1
        and trigger_condition = #{triggerCondition}
        and ((start_time &lt;= #{startTime} and end_time &gt;= #{startTime}) or (start_time &lt;= #{endTime} and end_time &gt;= #{endTime}))
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
</mapper>
