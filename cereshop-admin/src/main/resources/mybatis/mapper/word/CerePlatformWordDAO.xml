<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.word.CerePlatformWordDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.word.CerePlatformWord">
    <id column="word_id" jdbcType="BIGINT" property="wordId" />
    <result column="key_word" jdbcType="VARCHAR" property="keyWord" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    word_id, key_word, `state`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_word
    where word_id = #{wordId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_platform_word
    where word_id = #{wordId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="word_id" keyProperty="wordId" parameterType="com.shop.cereshop.commons.domain.word.CerePlatformWord" useGeneratedKeys="true">
    insert into cere_platform_word
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="keyWord != null and keyWord!=''">
        key_word,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="keyWord != null and keyWord!=''">
        #{keyWord,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.word.CerePlatformWord">
    update cere_platform_word
    <set>
      <if test="keyWord != null and keyWord!=''">
        key_word = #{keyWord,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where word_id = #{wordId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.word.CerePlatformWord">
    update cere_platform_word
    set key_word = #{keyWord,jdbcType=VARCHAR},
      `state` = #{state,jdbcType=BIT},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where word_id = #{wordId,jdbcType=BIGINT}
  </update>

  <select id="checkWord" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.word.CerePlatformWord">
    SELECT * FROM cere_platform_word where key_word=#{keyWord}
    <if test="wordId!=null">
      and word_id<![CDATA[!= ]]>#{wordId}
    </if>
  </select>

  <select id="getAll" parameterType="com.shop.cereshop.admin.param.word.WordParam" resultType="com.shop.cereshop.commons.domain.word.CerePlatformWord">
    SELECT * FROM cere_platform_word
    where 1=1
    <if test="search!=null and search!=''">
      and key_word like concat('%',#{search},'%')
    </if>
  </select>

  <update id="updateState" parameterType="java.lang.Object">
    UPDATE cere_platform_word SET state=#{state}
  </update>
</mapper>
