<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.customer_service.CereCustomerServiceConfigDAO">

    <update id="updatePermanentCode">
        update cere_customer_service_config
        set permanent_code = #{permanentCode},
        auth_corp_id = #{authCorpId}
        where shop_id = #{shopId}
    </update>

    <update id="clearAuthInfo">
        update cere_customer_service_config
        set permanent_code = null, auth_corp_id = null, update_time = #{updateTime}
        where auth_corp_id = #{authCorpId}
    </update>

</mapper>
