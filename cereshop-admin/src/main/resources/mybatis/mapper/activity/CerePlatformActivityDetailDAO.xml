<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.activity.CerePlatformActivityDetailDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.activity.CerePlatformActivityDetail">
    <id column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="full_money" jdbcType="DECIMAL" property="fullMoney" />
    <result column="reduce_money" jdbcType="DECIMAL" property="reduceMoney" />
  </resultMap>
  <insert id="insertSelective" keyColumn="coupon_id" keyProperty="couponId" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivityDetail" useGeneratedKeys="true">
    insert into cere_platform_activity_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="fullMoney != null">
        full_money,
      </if>
      <if test="reduceMoney != null">
        reduce_money,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="fullMoney != null">
        #{fullMoney,jdbcType=DECIMAL},
      </if>
      <if test="reduceMoney != null">
        #{reduceMoney,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <delete id="deleteByActivityId" parameterType="java.lang.Object">
    DELETE FROM cere_platform_activity_detail where activity_id=#{activityId}
  </delete>

  <select id="findByActivityId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.activity.CerePlatformActivityDetail">
    SELECT full_money, reduce_money FROM cere_platform_activity_detail where activity_id=#{activityId}
  </select>
</mapper>
