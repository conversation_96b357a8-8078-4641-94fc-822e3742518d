<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.activity.CereSignProductDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.activity.CereSignProduct">
        <result column="sign_id" jdbcType="BIGINT" property="signId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="total" jdbcType="INTEGER" property="total"/>
    </resultMap>
    <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.activity.CereSignProduct">
        insert into cere_sign_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="signId != null">
                sign_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="number != null">
                `number`,
            </if>
            <if test="total != null">
                `total`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="signId != null">
                #{signId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=BIGINT},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="total != null">
                #{total,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <delete id="deleteBySignId" parameterType="java.lang.Object">
        DELETE FROM cere_sign_product where sign_id=#{signId}
    </delete>

    <select id="selectSignProductList" resultType="com.shop.cereshop.commons.domain.activity.CereSignProduct">
        select b.product_id, b.number, b.total from cere_activity_sign a join cere_sign_product b on b.sign_id = a.sign_id
        where a.sign_type = #{signType} and a.activity_id = #{activityId}
    </select>
</mapper>
