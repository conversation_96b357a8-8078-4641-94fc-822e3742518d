<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.activity.CerePlatformActivityDAO">
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.activity.CerePlatformActivity">
        <id column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="activity_introduce" jdbcType="VARCHAR" property="activityIntroduce"/>
        <result column="sign_start_time" jdbcType="VARCHAR" property="signStartTime"/>
        <result column="sign_end_time" jdbcType="VARCHAR" property="signEndTime"/>
        <result column="activity_start_time" jdbcType="VARCHAR" property="activityStartTime"/>
        <result column="activity_end_time" jdbcType="VARCHAR" property="activityEndTime"/>
        <result column="if_bond" jdbcType="BIT" property="ifBond"/>
        <result column="bond_money" jdbcType="DECIMAL" property="bondMoney"/>
        <result column="threshold" jdbcType="DECIMAL" property="threshold"/>
        <result column="discount_mode" jdbcType="BIT" property="discountMode"/>
        <result column="coupon_content" jdbcType="DECIMAL" property="couponContent"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="stock_number" jdbcType="INTEGER" property="stockNumber"/>
        <result column="receive_type" jdbcType="INTEGER" property="receiveType"/>
        <result column="frequency" jdbcType="INTEGER" property="frequency"/>
        <result column="image" jdbcType="VARCHAR" property="image"/>
        <result column="if_credit" jdbcType="BIT" property="ifCredit"/>
        <result column="credit" jdbcType="INTEGER" property="credit"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="apply_type" jdbcType="INTEGER" property="applyType"/>
        <result column="apply_category" jdbcType="BIGINT" property="applyCategory"/>
        <result column="sync_card" jdbcType="BIT" property="syncCard"/>
        <result column="card_title" jdbcType="VARCHAR" property="cardTitle"/>
        <result column="card_color" jdbcType="VARCHAR" property="cardColor"/>
        <result column="card_notice" jdbcType="VARCHAR" property="cardNotice"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        activity_id, activity_name, activity_introduce, sign_start_time, sign_end_time, activity_start_time,
        activity_end_time, if_bond, bond_money,threshold, discount_mode, coupon_content,`number`,stock_number,
        receive_type,frequency, image, if_credit, credit, apply_type, apply_category, sync_card,
        card_title, card_color, card_notice
        `state`, create_time, update_time
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cere_platform_activity
        where activity_id = #{activityId,jdbcType=BIGINT}
    </delete>
    <select id="getById" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.admin.page.activity.ActivityDetail">
        SELECT activity_name,
               activity_introduce,
               sign_start_time,
               sign_end_time,
               activity_start_time,
               activity_end_time,
               if_bond,
               bond_money,
               threshold,
               discount_mode,
               coupon_content,
               `number`,
               receive_type,
               frequency,
               `state`,
               image,
               if_credit,
               credit,
               apply_type,
               apply_category,
               sync_card,
               card_title,
               card_color,
               card_notice
        FROM cere_platform_activity
        where activity_id = #{activityId}
    </select>

    <select id="getAll" parameterType="com.shop.cereshop.admin.param.activity.ActivityGetAllParam"
            resultType="com.shop.cereshop.admin.page.activity.Activity">
        SELECT a.activity_id,a.activity_name,a.state,a.discount_mode,
        IF(b.shopNumber IS NULL,0,b.shopNumber) shopNumber, if_credit, credit
        from cere_platform_activity a
        LEFT JOIN (SELECT COUNT(sign_id) shopNumber,activity_id from cere_activity_sign where state=1 and sign_type=1
        GROUP BY
        activity_id) b ON a.activity_id=b.activity_id
        where 1=1
        <if test="activityName!=null and activityName!=''">
            and a.activity_name like concat('%',#{activityName},'%')
        </if>
        <if test="state!=null">
            and a.state=#{state}
        </if>
        <if test="discountMode!=null">
            and a.discount_mode=#{discountMode}
        </if>
        GROUP BY a.activity_id
        ORDER BY a.update_time DESC,a.create_time DESC
    </select>

    <select id="findProductNumber" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM cere_sign_product a
                 LEFT JOIN cere_activity_sign b ON a.sign_id = b.sign_id
        where b.activity_id = #{activityId}
          and b.sign_type = 1
    </select>

    <select id="getShops" parameterType="com.shop.cereshop.admin.param.activity.ActivityGetShopsParam"
            resultType="com.shop.cereshop.admin.page.activity.ShopActivity">
        SELECT a.sign_id,a.shop_id,b.shop_name,b.shop_code,c.productNumber,a.activity_id,
        a.state from (SELECT a.* FROM (SELECT sign_id,shop_id,state,activity_id,sign_type from
        cere_activity_sign ORDER BY create_time DESC) a GROUP BY a.shop_id,a.activity_id) a
        LEFT JOIN cere_platform_shop b ON a.shop_id=b.shop_id
        LEFT JOIN (SELECT COUNT(product_id) productNumber,sign_id from cere_sign_product
        GROUP BY sign_id) c ON a.sign_id=c.sign_id
        where a.activity_id=#{activityId} and a.sign_type=#{signType}
        <if test="shopName!=null and shopName!=''">
            and b.shop_name like concat('%',#{shopName},'%')
        </if>
        <if test="shopCode!=null and shopCode!=''">
            and b.shop_code like concat('%',#{shopCode},'%')
        </if>
        <if test="state!=null">
            and a.state=#{state}
        </if>
        GROUP BY a.shop_id
    </select>

    <select id="findExamine" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from cere_activity_sign
        where state in (1, 2)
          and shop_id = #{shopId}
          and activity_id = #{activityId}
          and sign_type = 1
    </select>

    <select id="getProducts" parameterType="com.shop.cereshop.admin.param.activity.ActivityGetProductsParam"
            resultType="com.shop.cereshop.admin.page.product.ShopProduct">
        SELECT IF(f.image IS NULL OR f.image = '', c.product_image, f.image) image,
               b.product_name,
               a.product_id,
               d.price,
               d.original_price,
               d.stock_number,
               ifnull(SUM(e.number), 0) volume,
               b.shelve_state
        from cere_sign_product a
                 LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) c ON a.product_id = c.product_id
                 LEFT JOIN (SELECT a.product_id, a.price, a.original_price, a.stock_number, a.sku_image, a.sku_id
                            from cere_product_sku a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) d ON a.product_id = d.product_id
                 LEFT JOIN cere_order_product e ON a.product_id = e.product_id
                 LEFT JOIN cere_sku_name f ON d.sku_id = f.sku_id
        where a.sign_id = #{signId}
        GROUP BY e.product_id
    </select>

    <select id="getExamines" parameterType="com.shop.cereshop.admin.param.activity.ActivityGetExaminesParam"
            resultType="com.shop.cereshop.admin.page.activity.SignExamineLog">
        SELECT operation_describtion
        , IF(b.`name` IS NULL, c.`name`, b.`name`) `name`
        , a.create_time
        from cere_platform_log a
        LEFT JOIN cere_platform_user b ON a.platform_user_id = b.platform_user_id
        LEFT JOIN cere_platform_business c ON a.platform_user_id = c.business_user_id
        where module ='营销活动管理' and only =#{only}
        <if test='signType=="1"'>
            and operation_describtion in ('同意平台优惠券申请', '拒绝平台优惠券申请')
        </if>
        <if test='signType=="2"'>
            and operation_describtion in ('同意平台秒杀申请', '拒绝平台秒杀申请')
        </if>
        <if test='signType=="3"'>
            and operation_describtion in ('同意平台限时折扣申请', '拒绝平台限时折扣申请')
        </if>
    </select>

    <select id="getAdminBonds" parameterType="com.shop.cereshop.admin.param.activity.ActivityGetAdminBondsParam"
            resultType="com.shop.cereshop.admin.page.activity.ActivityBond">
        SELECT '营销活动保证金' bondType,b.activity_name,a.sign_code,a.bond_money,a.bond_state,
        IF(a.bond_state=1,a.payment_time,IF(a.bond_state=2,a.return_time,a.create_time)) time from
        (SELECT sign_id,sign_code,shop_id,activity_id,bond_money,payment_mode,
        qr_image,state,bond_state,payment_time,return_time,create_time
        from cere_activity_sign ORDER BY create_time DESC) a
        LEFT JOIN cere_platform_activity b ON a.activity_id=b.activity_id
        LEFT JOIN cere_platform_shop c ON a.shop_id=c.shop_id
        where a.bond_state in (1,2) and b.if_bond=1
        <if test="shopName!=null and shopName!=''">
            and c.shop_name like concat('%',#{shopName},'%')
        </if>
        <if test="state!=null">
            and a.bond_state=#{state}
        </if>
        <if test="startTime!=null and startTime!=''">
            and a.payment_time&gt;=#{startTime} and a.payment_time&lt;=#{endTime}
        </if>
        GROUP BY a.shop_id
    </select>

    <select id="getBondTotal" resultType="java.math.BigDecimal">
        SELECT SUM(a.bond_money)
        from (SELECT sign_id,
                     sign_code,
                     shop_id,
                     activity_id,
                     bond_money,
                     payment_mode,
                     qr_image,
                     state,
                     bond_state,
                     payment_time,
                     return_time
              from cere_activity_sign
              ORDER BY create_time DESC) a
                 LEFT JOIN cere_platform_activity b ON a.activity_id = b.activity_id
        where a.bond_state in (1, 2)
          and a.sign_type = 1
        GROUP BY a.shop_id
    </select>

    <select id="findById" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity">
        SELECT activity_id, activity_start_time, activity_end_time, sign_end_time
        FROM cere_platform_activity
        where activity_id = #{activityId}
    </select>

    <select id="findActivityData" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.ActivityData">
        SELECT IF(SUM(a.price) IS NULL, 0, SUM(a.price)) money, COUNT(*) orders, COUNT(a.buyer_user_id) users
        FROM cere_shop_order a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
        where a.payment_state = 1
          and a.state in (2, 3, 4)
          and b.activity_id = #{activityId}
          and b.state = 1
          and a.create_time &gt;= #{activityStartTime}
          and a.create_time &lt;= #{activityEndTime}
          and b.sign_type = 1
    </select>

    <select id="findBusiness" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM cere_activity_sign
        where activity_id = #{activityId}
          and state = 1
          and sign_type = 1
    </select>

    <select id="findProducts" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="java.lang.Integer">
        SELECT COUNT(b.product_id)
        FROM cere_activity_sign a
                 LEFT JOIN cere_sign_product b ON a.sign_id = b.sign_id
        where a.activity_id = #{activityId}
          and a.state = 1
          and a.sign_type = 1
    </select>

    <select id="findCities" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.Proportion">
        SELECT a.city name, COUNT(a.city) value
        from cere_shop_visit a
            LEFT JOIN cere_activity_sign b
        ON a.shop_id=b.shop_id
        where b.activity_id=#{activityId}
          and b.state=1
          and a.visit_time&gt;=#{activityStartTime}
          and a.visit_time&lt;=#{activityEndTime}
          and b.sign_type=1
        GROUP BY a.city
    </select>

    <select id="findCityPeople" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.Proportion">
        SELECT a.city name, COUNT(a.city) value
        from cere_shop_visit a
            LEFT JOIN cere_activity_sign b
        ON a.shop_id=b.shop_id
        where b.activity_id=#{activityId}
          and b.state=1
          and a.visit_time&gt;=#{activityStartTime}
          and a.visit_time&lt;=#{activityEndTime}
          and b.sign_type=1
        GROUP BY a.city
        ORDER BY COUNT(a.city) DESC
            LIMIT 5
    </select>

    <select id="findNews" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.Proportion">
        SELECT COUNT(*) value,'新客户' as name
        from cere_shop_visit a
            LEFT JOIN cere_buyer_user b
        ON a.buyer_user_id=b.buyer_user_id
            LEFT JOIN cere_activity_sign c ON a.shop_id= c.shop_id
        where c.activity_id=#{activityId}
          and c.state=1
          and a.visit_time&gt;=#{activityStartTime}
          and a.visit_time&lt;=#{activityEndTime}
          and b.create_time&gt;=#{activityStartTime}
          and b.create_time&lt;=#{activityEndTime}
          and c.sign_type=1
    </select>

    <select id="findOlds" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.Proportion">
        SELECT COUNT(*) value,'老客户' as name
        from cere_shop_visit a
            LEFT JOIN cere_buyer_user b
        ON a.buyer_user_id=b.buyer_user_id
            LEFT JOIN cere_activity_sign c ON a.shop_id= c.shop_id
        where c.activity_id=#{activityId}
          and c.state=1
          and a.visit_time&gt;=#{activityStartTime}
          and a.visit_time&lt;=#{activityEndTime}
          and b.create_time&lt;=#{activityStartTime}
          and c.sign_type=1
    </select>

    <select id="findAPP" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.Proportion">
        SELECT COUNT(*) value,'APP' as name
        from cere_shop_visit a
            LEFT JOIN cere_activity_sign c
        ON a.shop_id= c.shop_id
        where c.activity_id=#{activityId}
          and c.state=1
          and a.visit_time&gt;=#{activityStartTime}
          and a.visit_time&lt;=#{activityEndTime}
          and terminal=1
          and c.sign_type=1
    </select>

    <select id="findApplets" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.Proportion">
        SELECT COUNT(*) value,'小程序' as name
        from cere_shop_visit a
            LEFT JOIN cere_activity_sign c
        ON a.shop_id= c.shop_id
        where c.activity_id=#{activityId}
          and c.state=1
          and a.visit_time&gt;=#{activityStartTime}
          and a.visit_time&lt;=#{activityEndTime}
          and terminal=2
          and c.sign_type=1
    </select>

    <select id="findAdroid" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.Proportion">
        SELECT COUNT(*) value,'Android' as name
        from cere_shop_visit a
            LEFT JOIN cere_activity_sign c
        ON a.shop_id= c.shop_id
        where c.activity_id=#{activityId}
          and c.state=1
          and a.visit_time&gt;=#{activityStartTime}
          and a.visit_time&lt;=#{activityEndTime}
          and a.system=1
          and c.sign_type=1
    </select>

    <select id="findIos" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.Proportion">
        SELECT COUNT(*) value,'IOS' as name
        from cere_shop_visit a
            LEFT JOIN cere_activity_sign c
        ON a.shop_id= c.shop_id
        where c.activity_id=#{activityId}
          and c.state=1
          and a.visit_time&gt;=#{activityStartTime}
          and a.visit_time&lt;=#{activityEndTime}
          and a.system=2
          and c.sign_type=1
    </select>

    <select id="findTrendMoney" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(a.price) IS NULL, 0, SUM(a.price))
        from cere_shop_order a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
        where a.payment_state = 1
          and a.state in (2, 3, 4)
          and b.activity_id = #{activityId}
          and b.state = 1
          and a.create_time LIKE CONCAT('%', #{time}, '%')
          and b.sign_type = 1
    </select>

    <select id="findTrendTotal" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from cere_shop_visit a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
        where b.activity_id = #{activityId}
          and b.state = 1
          and a.visit_time LIKE CONCAT('%', #{time}, '%')
          and b.sign_type = 1
    </select>

    <select id="findShopRankings" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.ShopRanking">
        SELECT c.shop_name, IF(SUM(a.price) IS NULL, 0, SUM(a.price)) money
        from cere_shop_order a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
                 LEFT JOIN cere_platform_shop c ON a.shop_id = c.shop_id
        where a.payment_state = 1
          and a.state in (2, 3, 4)
          and b.activity_id = #{activityId}
          and b.state = 1
          and a.create_time &gt;= #{activityStartTime}
          and a.create_time &lt;= #{activityEndTime}
          and b.sign_type = 1
        GROUP BY a.shop_id
        ORDER BY IF(SUM(a.price) IS NULL, 0, SUM(a.price)) DESC LIMIT 10
    </select>

    <select id="findClassifies" resultType="com.shop.cereshop.commons.domain.product.CereProductClassify">
        SELECT classify_id, classify_name
        FROM cere_product_classify
        where classify_level = 1
    </select>

    <select id="findTotalByClassify" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(a.product_price * a.number) IS NULL, 0, SUM(a.product_price * a.number))
        from cere_order_product a
                 LEFT JOIN cere_shop_order b ON a.order_id = b.order_id
                 LEFT JOIN cere_activity_sign c ON b.shop_id = c.shop_id
                 LEFT JOIN cere_shop_product d ON a.product_id = d.product_id
                 LEFT JOIN cere_product_classify e ON d.classify_id = e.classify_id
        where b.payment_state = 1
          and b.state in (2, 3, 4)
          and c.activity_id = #{activityId}
          and c.state = 1
          and c.sign_type = 1
          and b.create_time &gt;= #{activityStartTime}
          and b.create_time &lt;= #{activityEndTime}
          and d.classify_id1 = #{classifyId}
    </select>

    <select id="findProductRankings" parameterType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity"
            resultType="com.shop.cereshop.admin.page.activity.ProductRanking">
        SELECT IF(SUM(a.number) IS NULL, 0, SUM(a.number)) total, d.product_name
        from cere_order_product a
                 LEFT JOIN cere_shop_order b ON a.order_id = b.order_id
                 LEFT JOIN cere_activity_sign c ON b.shop_id = c.shop_id
                 LEFT JOIN cere_shop_product d ON a.product_id = d.product_id
        where b.payment_state = 1
          and b.state in (2, 3, 4)
          and c.activity_id = #{activityId}
          and c.state = 1
          and c.sign_type = 1
          and b.create_time &gt;= #{activityStartTime}
          and b.create_time &lt;= #{activityEndTime}
        GROUP BY a.product_id
        ORDER BY IF(SUM(a.number) IS NULL, 0, SUM(a.number)) DESC LIMIT 10
    </select>

    <select id="findShops" parameterType="com.shop.cereshop.admin.param.activity.ActivityGetDatasParam"
            resultType="com.shop.cereshop.commons.domain.shop.ShopDetail">
        SELECT a.shop_id, a.shop_name, a.shop_code
        from cere_platform_shop a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
        where b.activity_id = #{activityId}
          and b.state = 1
          and b.sign_type = 1
    </select>

    <select id="findShopProducts" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT IF(SUM(a.number) IS NULL, 0, SUM(a.number))
        from cere_order_product a
                 LEFT JOIN cere_shop_order b ON a.order_id = b.order_id
                 LEFT JOIN cere_activity_sign c ON b.shop_id = c.shop_id
        where b.payment_state = 1
          and b.state in (2, 3, 4)
          and c.activity_id = #{activityId}
          and c.state = 1
          and c.sign_type = 1
          and b.create_time &gt;= #{activityStartTime}
          and b.create_time &lt;= #{activityEndTime}
          and b.shop_id = #{shopId}
    </select>

    <select id="findShopPersons" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from cere_shop_visit a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
        where b.activity_id = #{activityId}
          and b.state = 1
          and b.sign_type = 1
          and a.shop_id = #{shopId}
          and a.visit_time &gt;= #{activityStartTime}
          and a.visit_time &lt;= #{activityEndTime}
    </select>

    <select id="findShopOrders" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from cere_shop_order a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
        where a.payment_state = 1
          and a.state in (2, 3, 4)
          and b.activity_id = #{activityId}
          and b.state = 1
          and b.sign_type = 1
          and a.shop_id = #{shopId}
          and a.create_time &gt;= #{activityStartTime}
          and a.create_time &lt;= #{activityEndTime}
    </select>

    <select id="findShopCustomers" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT COUNT(a.buyer_user_id)
        from (SELECT shop_id, buyer_user_id
              FROM cere_shop_order
              where payment_state = 1
                and state in (2, 3, 4)
                and shop_id = #{shopId}
                and create_time &gt;= #{activityStartTime}
                and create_time &lt;= #{activityEndTime}
              GROUP BY buyer_user_id) a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
        where b.activity_id = #{activityId}
          and b.state = 1
          and b.sign_type = 1
    </select>

    <select id="findShopTotal" parameterType="java.lang.Object" resultType="java.math.BigDecimal">
        SELECT IF(SUM(a.price) IS NULL, 0, SUM(a.price))
        from cere_shop_order a
                 LEFT JOIN cere_activity_sign b ON a.shop_id = b.shop_id
        where a.payment_state = 1
          and a.state in (2, 3, 4)
          and b.activity_id = #{activityId}
          and b.state = 1
          and b.sign_type = 1
          and a.shop_id = #{shopId}
          and a.create_time &gt;= #{activityStartTime}
          and a.create_time &lt;= #{activityEndTime}
    </select>

    <select id="checkTime" parameterType="com.shop.cereshop.admin.param.activity.ActivitySaveParam"
            resultType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity">
        SELECT activity_id
        from cere_platform_activity
        where ((activity_start_time &lt;= #{activityStartTime} and activity_end_time &gt;= #{activityStartTime})
            OR (activity_start_time &lt;= #{activityEndTime} and activity_end_time &gt;= #{activityEndTime}))
          and state<![CDATA[!= ]]>4
    </select>

    <select id="checkUpdateTime" parameterType="com.shop.cereshop.admin.param.activity.ActivityUpdateParam"
            resultType="com.shop.cereshop.commons.domain.activity.CerePlatformActivity">
        SELECT activity_id
        from cere_platform_activity
        where (activity_start_time &lt;= #{activityStartTime} and activity_end_time &gt;= #{activityStartTime}
            OR activity_start_time &lt;= #{activityEndTime} and activity_end_time &gt;= #{activityEndTime})
          and state<![CDATA[!= ]]>4 and activity_id<![CDATA[!= ]]>#{activityId}
    </select>

    <select id="findIfBondBySignId" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT if_bond
        FROM cere_platform_activity
        where activity_id = (SELECT activity_id FROM cere_activity_sign where sign_id = #{signId})
    </select>

    <select id="findSeckillIfBondBySignId" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT if_bond
        FROM cere_platform_seckill
        where seckill_id = (SELECT activity_id FROM cere_activity_sign where sign_id = #{signId})
    </select>

    <select id="findDiscountIfBondBySignId" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT if_bond
        FROM cere_platform_discount
        where discount_id = (SELECT activity_id FROM cere_activity_sign where sign_id = #{signId})
    </select>

    <select id="checkSignError" parameterType="com.shop.cereshop.commons.domain.activity.CereActivitySign"
            resultType="com.shop.cereshop.commons.domain.activity.CereActivitySign">
        SELECT *
        FROM cere_activity_sign
        where sign_id<![CDATA[!= ]]>#{signId}
          and shop_id=#{shopId}
          and sign_type=1
          and activity_id=#{activityId}
          and state in (0
            , 1)
    </select>

    <select id="findSign" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.activity.CereActivitySign">
        SELECT *
        FROM cere_activity_sign
        where sign_id = #{signId}
    </select>

    <select id="findBondPayLog" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.pay.CerePayLog">
        SELECT *
        FROM cere_pay_log
        WHERE order_formid = #{formid}
    </select>

    <select id="findSignIdsByShopIdAndActivityId" parameterType="java.lang.Object" resultType="java.lang.Long">
        SELECT sign_id
        FROM cere_activity_sign
        where shop_id = #{shopId}
          and sign_type = #{signType}
          and activity_id = #{activityId}
    </select>

    <select id="findByActivity" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.activity.CereActivitySign">
        SELECT a.*
        FROM (SELECT * FROM cere_activity_sign ORDER BY create_time DESC) a
        where a.activity_id = #{activityId}
          and state = 1
          and sign_type = 1
        GROUP BY a.sign_id
    </select>

    <select id="findErrorSign" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.commons.domain.activity.CereActivitySign">
        SELECT a.*
        FROM (SELECT * FROM cere_activity_sign ORDER BY create_time DESC) a
        where a.activity_id = #{activityId}
          and state in (0, 2)
          and sign_type = 1
        GROUP BY a.sign_id
    </select>

    <select id="findIfBond" parameterType="java.lang.Object" resultType="java.lang.Integer">
        SELECT if_bond
        FROM cere_platform_activity
        WHERE activity_id = #{activityId}
    </select>

    <update id="updateSignStateError" parameterType="java.util.List">
        UPDATE cere_activity_sign SET state=2
        where sign_id in (
        <foreach collection="list" item="item" index="index" separator=",">
            #{item.signId}
        </foreach>
        )
    </update>

    <select id="getCoupons" parameterType="com.shop.cereshop.admin.param.canvas.CanvasCouponParam"
            resultType="com.shop.cereshop.admin.page.canvas.CanvasCoupon">
        SELECT activity_id coupon_id, discount_mode as coupon_type, activity_id, activity_name,
        activity_start_time, activity_end_time,
        threshold full_money, coupon_content reduce_money FROM cere_platform_activity
        where state in (2,3)
        <if test="search!=null and search!=''">
            and activity_name like concat('%',#{search},'%')
        </if>
        <if test="ids!=null and ids.size()>0">
            and activity_id in (
            <foreach collection="ids" item="id" index="index" separator=",">
                #{id}
            </foreach>
            )
        </if>
        order by create_time desc
    </select>

    <select id="findDetai" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.admin.page.canvas.CanvasCouponDetail">
        SELECT activity_id coupon_id, threshold full_money, coupon_content reduce_money
        FROM cere_platform_activity
        WHERE activity_id = #{activityId}
    </select>

    <select id="getGroupWorks" parameterType="com.shop.cereshop.admin.param.canvas.RenovationParam"
            resultType="com.shop.cereshop.admin.page.canvas.ShopGroupWorkUDetail">
        SELECT a.shop_group_work_id, shop_id, group_name, remark, start_time, end_time, person, effective_time,
        if_limit, limit_number, if_enable, enable_time, if_add, `state` FROM cere_shop_group_work a
        LEFT JOIN (SELECT COUNT(*) total,shop_group_work_id FROM cere_collage_order GROUP BY shop_group_work_id) b
        ON a.shop_group_work_id=b.shop_group_work_id
        where state in (0,1)
        <if test="ids!=null and ids.size()>0">
            and shop_group_work_id in (
            <foreach collection="ids" item="id" index="index" separator=",">
                #{id}
            </foreach>
            )
        </if>
        ORDER BY b.total DESC
    </select>

    <select id="findDistinctProducts" parameterType="java.lang.Object"
            resultType="com.shop.cereshop.admin.page.canvas.ToolProduct">
        SELECT b.product_id,
               b.product_name,
               c.original_price,
               e.product_image image,
               c.stock_number,
               a.price,
               a.sku_id,
               c.total,
               b.shop_id,
               g.workUsers
        from cere_shop_group_work_detail a
                 LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
                 LEFT JOIN cere_product_sku c ON a.sku_id = c.sku_id
                 LEFT JOIN (SELECT a.product_id, a.product_image
                            from cere_product_image a,
                                 cere_shop_product b
                            where a.product_id = b.product_id
                            GROUP BY a.product_id) e
                           ON b.product_id = e.product_id
                 LEFT JOIN cere_product_classify f ON b.classify_id = f.classify_id
                 LEFT JOIN (SELECT a.product_id, COUNT(*) workUsers
                            FROM (SELECT a.product_id, d.buyer_user_id
                                  FROM cere_order_product a
                                           LEFT JOIN cere_collage_order_detail b ON a.order_id = b.order_id and b.state = 1
                                           LEFT JOIN cere_collage_order c ON b.collage_id = c.collage_id and c.state = 1
                                           LEFT JOIN cere_shop_order d ON a.order_id = d.order_id
                                  where c.shop_group_work_id = #{shopGroupWorkId}
                                  GROUP BY a.product_id, d.buyer_user_id) a
                            GROUP BY a.product_id) g ON a.product_id = g.product_id
        where a.shop_group_work_id = #{shopGroupWorkId}
        GROUP BY a.product_id LIMIT 20
    </select>

    <select id="getGroupWorkProducts" parameterType="com.shop.cereshop.admin.param.canvas.RenovationParam" resultType="com.shop.cereshop.admin.page.canvas.ToolProduct">
        SELECT b.product_id,
        b.product_name,
        c.original_price,
        d.product_image image,
        c.stock_number,
        a.price,
        a.sku_id,
        c.total,
        b.shop_id,
        c.original_price,
        SUM(f.number) number
        from cere_shop_group_work_detail a
        LEFT JOIN cere_shop_product b ON a.product_id = b.product_id
        LEFT JOIN cere_product_sku c ON a.sku_id = c.sku_id
        LEFT JOIN (SELECT a.product_id, a.product_image
        from cere_product_image a,
        cere_shop_product b
        where a.product_id = b.product_id
        GROUP BY a.product_id) d
        ON b.product_id = d.product_id
        LEFT JOIN cere_shop_group_work e ON a.shop_group_work_id=e.shop_group_work_id
        LEFT JOIN cere_order_product f ON a.product_id=f.product_id
        LEFT JOIN cere_collage_order_detail h ON f.order_id=h.order_id
        LEFT JOIN cere_collage_order g ON h.collage_id=g.collage_id and g.state=1
        where e.state=1
        <if test="ids!=null and ids.size()>0">
            and a.shop_group_work_id in (
            <foreach collection="ids" item="id" index="index" separator=",">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY f.product_id
        ORDER BY SUM(f.number) DESC
        LIMIT 20
    </select>
</mapper>
