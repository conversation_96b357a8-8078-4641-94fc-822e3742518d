<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.business.CerePlatformBusinessDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    <id column="business_user_id" jdbcType="BIGINT" property="businessUserId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    business_user_id, username, `password`, `name`, sex, email,token, `state`, create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_business
    where business_user_id = #{businessUserId,jdbcType=BIGINT}
  </select>
  <select id="findProjectByUsername" resultType="java.lang.Long">
    SELECT
      cbs.shop_id
    FROM
      cere_business_shop cbs
        JOIN cere_platform_business cpb ON cpb.business_user_id = cbs.business_user_id
    where
      cpb.username = #{username}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_platform_business
    where business_user_id = #{businessUserId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="business_user_id" keyProperty="businessUserId" parameterType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness" useGeneratedKeys="true">
    insert into cere_platform_business
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null and username!=''">
        username,
      </if>
      <if test="password != null and password!=''">
        `password`,
      </if>
      <if test="name != null and name!=''">
        `name`,
      </if>
      <if test="sex != null and sex!=''">
        sex,
      </if>
      <if test="email != null and email!=''">
        email,
      </if>
      <if test="token != null and token!=''">
        token,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null and username!=''">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password!=''">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="name != null and name!=''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null and sex!=''">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="email != null and email!=''">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="token != null and token!=''">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    update cere_platform_business
    <set>
      <if test="username != null and username!=''">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password!=''">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="name != null and name!=''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null and sex!=''">
        sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="email != null and email!=''">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="token != null and token!=''">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone != ''">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
    </set>
    where business_user_id = #{businessUserId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    update cere_platform_business
    set username = #{username,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      `state` = #{state,jdbcType=BIT},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where business_user_id = #{businessUserId,jdbcType=BIGINT}
  </update>

  <select id="findByPhone" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_business
    where phone = #{phone}
  </select>

  <select id="findListByUsername" resultType="com.shop.cereshop.commons.domain.business.CerePlatformBusiness">
    SELECT
      cbs.shop_id as shopId,
      cbs.business_user_id as businessUserId
    FROM cere_business_shop cbs
    JOIN cere_platform_business cpb ON cpb.business_user_id = cbs.business_user_id
    where cpb.username = #{username}
  </select>
</mapper>
