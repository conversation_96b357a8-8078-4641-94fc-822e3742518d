<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.user.CerePlatformUserDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.user.CerePlatformUser">
    <id column="platform_user_id" jdbcType="BIGINT" property="platformUserId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    platform_user_id, username, `name`, sex, phone, `password`, email, token, `state`,
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_platform_user
    where platform_user_id = #{platformUserId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_platform_user
    where platform_user_id = #{platformUserId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="platform_user_id" keyProperty="platformUserId" parameterType="com.shop.cereshop.commons.domain.user.CerePlatformUser" useGeneratedKeys="true">
    insert into cere_platform_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null and username!=''">
        username,
      </if>
      <if test="name != null and name!=''">
        `name`,
      </if>
      <if test="sex != null and sex!=''">
        sex,
      </if>
      <if test="phone != null and phone!=''">
        phone,
      </if>
      <if test="password != null and password!=''">
        `password`,
      </if>
      <if test="email != null and email!=''">
        email,
      </if>
      <if test="token != null and token!=''">
        token,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null and username!=''">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="name != null and name!=''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null and sex!=''">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone!=''">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password!=''">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="email != null and email!=''">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="token != null and token!=''">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.user.CerePlatformUser">
    update cere_platform_user
    <set>
      <if test="username != null and username!=''">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="name != null and name!=''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null and sex!=''">
        sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone!=''">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="password != null and password!=''">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="email != null and email!=''">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="token != null and token!=''">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where platform_user_id = #{platformUserId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.user.CerePlatformUser">
    update cere_platform_user
    set username = #{username,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      `state` = #{state,jdbcType=BIT},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where platform_user_id = #{platformUserId,jdbcType=BIGINT}
  </update>

  <update id="updateToken" parameterType="com.shop.cereshop.commons.domain.user.CerePlatformUser">
    UPDATE cere_platform_user set token=#{token} where platform_user_id=#{platformUserId}
  </update>

  <select id="findByUserName" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.user.CerePlatformUser">
    SELECT platform_user_id,`name`,sex,email,phone,`password`,avatar,token,state from cere_platform_user where username=#{userName}
  </select>

  <select id="findByPhone" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.user.CerePlatformUser">
    SELECT platform_user_id,`name`,phone,`password`,avatar,token,state from cere_platform_user where phone=#{phone}
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.user.PlatformUser">
    SELECT platform_user_id,username,password,`name`,sex,phone,state,email from cere_platform_user where platform_user_id=#{platformUserId}
  </select>

  <select id="findByToken" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.user.CerePlatformUser">
    SELECT platform_user_id,`name`,phone,`username` from cere_platform_user where token=#{token}
  </select>

  <select id="getAll" parameterType="com.shop.cereshop.admin.param.user.UserGetAllParam" resultType="com.shop.cereshop.admin.page.user.PlatformUser">
    SELECT platform_user_id,username,`name`,sex,phone,state,email,create_time from cere_platform_user
    where username<![CDATA[!= ]]>'admin' and username<![CDATA[!= ]]>'root'
    <if test="search!=null and search!=''">
      and (username like concat('%',#{search},'%') or
      email like concat('%',#{search},'%'))
    </if>
    <if test="state!=null">
      and state=#{state}
    </if>
    ORDER BY update_time DESC,create_time DESC
  </select>

  <select id="checkUserNameOrPhone" parameterType="com.shop.cereshop.admin.param.user.UserSaveParam" resultType="com.shop.cereshop.commons.domain.user.CerePlatformUser">
    SELECT platform_user_id FROM cere_platform_user where (username=#{username} OR phone=#{phone})
    <if test="platformUserId!=null">
      and platform_user_id<![CDATA[!= ]]>#{platformUserId}
    </if>
  </select>
</mapper>
