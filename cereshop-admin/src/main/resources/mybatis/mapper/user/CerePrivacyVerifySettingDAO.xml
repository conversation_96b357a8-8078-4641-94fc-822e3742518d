<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.user.CerePrivacyVerifySettingDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.user.CerePrivacyVerifySetting">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project" jdbcType="BIGINT" property="project" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project, phone
  </sql>

  <select id="findVerifyPhone" resultType="java.lang.String">
    select phone
    from cere_privacy_verify_setting
    where project = 1
  </select>

  <select id="selectAdminSetting" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from cere_privacy_verify_setting
    where project = 1
  </select>

</mapper>
