<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.after.CerePlatformAfterDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.after.CerePlatformAfter">
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="handle_time" jdbcType="VARCHAR" property="handleTime" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.after.CerePlatformAfter">
    insert into cere_platform_after
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="reason != null and reason!=''">
        reason,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="remark != null and remark!=''">
        remark,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="handleTime != null and handleTime!=''">
        handle_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="reason != null and reason!=''">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="handleTime != null and handleTime!=''">
        #{handleTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateAfter" parameterType="com.shop.cereshop.commons.domain.after.CerePlatformAfter">
    UPDATE cere_platform_after set handle_time=#{handleTime},remark=#{remark},state=#{state} where order_id=#{orderId}
  </update>
</mapper>
