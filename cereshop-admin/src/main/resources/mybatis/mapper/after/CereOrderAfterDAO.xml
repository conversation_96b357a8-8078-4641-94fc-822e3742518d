<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.after.CereOrderAfterDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    <id column="after_id" jdbcType="BIGINT" property="afterId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="after_formid" jdbcType="VARCHAR" property="afterFormid" />
    <result column="after_state" jdbcType="BIT" property="afterState" />
    <result column="after_type" jdbcType="BIT" property="afterType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="explain" jdbcType="VARCHAR" property="explain" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    after_id, order_id, after_formid, after_state, after_type, price,`explain`, remark, reason,
    image, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_order_after
    where after_id = #{afterId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_order_after
    where after_id = #{afterId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="after_id" keyProperty="afterId" parameterType="com.shop.cereshop.commons.domain.after.CereOrderAfter" useGeneratedKeys="true">
    insert into cere_order_after
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="afterFormid != null and afterFormid!=''">
        after_formid,
      </if>
      <if test="afterState != null">
        after_state,
      </if>
      <if test="afterType != null">
        after_type,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="explain != null and explain!=''">
        `explain`,
      </if>
      <if test="remark != null and remark!=''">
        remark,
      </if>
      <if test="reason != null and reason!=''">
        reason,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="afterFormid != null and afterFormid!=''">
        #{afterFormid,jdbcType=VARCHAR},
      </if>
      <if test="afterState != null">
        #{afterState,jdbcType=BIT},
      </if>
      <if test="afterType != null">
        #{afterType,jdbcType=BIT},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="explain != null and explain!=''">
        #{explain,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reason != null and reason!=''">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    update cere_order_after
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="afterFormid != null and afterFormid!=''">
        after_formid = #{afterFormid,jdbcType=VARCHAR},
      </if>
      <if test="afterState != null">
        after_state = #{afterState,jdbcType=BIT},
      </if>
      <if test="afterType != null">
        after_type = #{afterType,jdbcType=BIT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="explain != null and explain!=''">
        `explain` = #{explain,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="reason != null and reason!=''">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="image != null and image!=''">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where after_id = #{afterId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    update cere_order_after
    set order_id = #{orderId,jdbcType=BIGINT},
      after_formid = #{afterFormid,jdbcType=VARCHAR},
      after_state = #{afterState,jdbcType=BIT},
      after_type = #{afterType,jdbcType=BIT},
      price = #{price,jdbcType=DECIMAL},
      `explain` = #{explain,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      image = #{image,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where after_id = #{afterId,jdbcType=BIGINT}
  </update>

  <select id="getAll" parameterType="com.shop.cereshop.admin.param.after.AfterGetAllParam" resultType="com.shop.cereshop.admin.page.after.After">
    select
    a.after_id,
    a.order_id,
    a.price refundMoney,
    IF(a.after_type = 1,'仅退款','退货退款') after_type,
    a.after_state,
    b.order_formid,
    b.payment_time,
    b.customer_name,
    b.state orderState,
    c.shop_name,
    c.shop_code,
    h.create_time platformAfterTime
    from cere_order_after a join cere_platform_after h on h.order_id = a.order_id
    join cere_shop_order b on b.order_id = a.order_id
    join cere_platform_shop c on c.shop_id = b.shop_id
    where a.after_state in (7, 9, 10)
    <if test="shopName!=null and shopName!=''">
      and (c.shop_name like concat('%',#{shopName},'%')
      or c.shop_code like concat('%',#{shopName},'%'))
    </if>
    <if test="orderFormid!=null and orderFormid!=''">
      and b.order_formid like concat('%',#{orderFormid},'%')
    </if>
    <if test="startTime!=null and startTime!=''">
      and h.create_time&gt;=#{startTime} and h.create_time&lt;=#{endTime}
    </if>
    <if test='state=="0"'>
      and h.state=0
    </if>
    <if test='state=="1"'>
      and h.state in (1,2)
    </if>
    GROUP BY a.after_id
    ORDER BY a.update_time DESC, a.create_time DESC
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.after.AfterDetail">
    SELECT a.after_id,a.order_id,b.order_formid,a.after_formid,b.payment_time,b.logistics_price,
    IF(b.payment_mode=1,'微信','其他') payment_mode,b.order_price,c.shop_id,c.shop_name,g.dict_name express,e.deliver_formid,
    IF(a.after_type=1,'仅退款','退货退款') after_type,b.customer_name,d.buyer_user_id,c.charge_person_name,c.charge_person_phone,
    c.shop_phone,c.shop_adress,f.transaction_id,b.state,b.after_state,
    b.logistics_price,b.discount_price,b.price,a.price refundPrice,
    c.effective_date,c.effective_year,a.explain,a.reason from cere_order_after a
    LEFT JOIN cere_shop_order b ON a.order_id=b.order_id
    LEFT JOIN cere_platform_shop c ON b.shop_id=c.shop_id
    LEFT JOIN cere_buyer_user d ON b.buyer_user_id=d.buyer_user_id
    LEFT JOIN cere_order_dilever e ON b.order_id=e.order_id
    LEFT JOIN cere_pay_log f ON b.order_formid=f.order_formid
    LEFT JOIN cere_platform_dict g ON e.express=g.dict_id
    where a.after_id=#{afterId}
  </select>

  <select id="findProducts" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.after.AfterProduct">
    SELECT a.image,a.product_name,GROUP_CONCAT(b.sku_value) skuValue,
    a.product_price,a.number,a.product_price*a.number total,a.SKU from cere_after_product a
    LEFT JOIN cere_after_product_attribute b ON a.after_product_id=b.after_product_id
    where a.after_id=#{afterId}
  </select>

  <select id="findHistories" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.after.AfterHistory">
    SELECT operation_describtion title,a.create_time time,
    IF(a.operation='客户端操作',IF(d.wechat_name IS NULL,d.`name`,d.wechat_name),
    IF(a.operation='商户端操作',c.`name`,b.`name`)) `name`,e.image,e.reason
    from cere_platform_log a
    LEFT JOIN cere_platform_user b ON a.platform_user_id=b.platform_user_id
    LEFT JOIN cere_platform_business c ON a.platform_user_id=c.business_user_id
    LEFT JOIN cere_buyer_user d ON a.platform_user_id=d.buyer_user_id
    LEFT JOIN (SELECT b.after_id,a.reason,a.image from cere_platform_after a,cere_order_after b
    where a.order_id=b.order_id and b.after_id=#{afterId}) e ON a.only=e.after_id and a.operation_describtion='申请平台介入'
    where a.module='售后管理' and
    only=#{afterId}
    ORDER BY a.create_time DESC
  </select>

  <select id="findAdminHistory" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.after.AfterHistory">
    SELECT '申请平台介入' as title,b.reason,b.handle_time time,b.image,c.customer_name `name` from cere_order_after a
    LEFT JOIN cere_platform_after b ON a.order_id=b.order_id
    LEFT JOIN cere_shop_order c ON a.order_id=c.order_id
    where a.after_id=#{afterId}
  </select>

  <select id="getBuyer" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.after.AfterBuyer">
    SELECT IF(a.wechat_name IS NULL,a.`name`,a.wechat_name) `name`,
    a.create_time registerTime,a.phone,b.orders,c.afters from cere_buyer_user a
    LEFT JOIN (SELECT buyer_user_id,COUNT(*) orders from cere_shop_order where buyer_user_id=#{buyerUserId}) b ON a.buyer_user_id=b.buyer_user_id
    LEFT JOIN (SELECT b.buyer_user_id,COUNT(a.after_id) afters from cere_order_after a,cere_shop_order b
    where a.order_id=b.order_id and b.buyer_user_id=#{buyerUserId}) c ON a.buyer_user_id=c.buyer_user_id
    where a.buyer_user_id=#{buyerUserId}
  </select>

  <select id="findSuccessAfter" parameterType="java.lang.Object" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM cere_order_after a
    LEFT JOIN cere_shop_order b ON a.order_id=b.order_id
    where b.buyer_user_id=#{buyerUserId} and a.after_state=2
  </select>

  <select id="findByOrderId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    SELECT after_id,after_type,price FROM cere_order_after where order_id=#{orderId}
    ORDER BY create_time DESC limit 1
  </select>

  <select id="findExpress"  resultType="java.lang.Integer">
    SELECT express_type FROM cere_platform_express
  </select>

  <select id="findOrderPay" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.after.AfterRefund">
    SELECT a.price,b.price orderPrice,c.transaction_id,c.out_refund_no,c.after,c.id,
    c.order_formid,c.out_trade_no from cere_order_after a
    LEFT JOIN cere_shop_order b ON a.order_id=b.order_id
    LEFT JOIN cere_pay_log c ON b.order_formid=c.order_formid
    where a.after_id=#{afterId}
  </select>

  <select id="findOtherProductsByAfterId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.order.CereOrderProduct">
    SELECT product_id FROM cere_order_product where product_id NOT in (SELECT product_id FROM cere_after_product where after_id=#{afterId})
    and order_id=#{orderId}
  </select>

  <select id="findAfterSkus" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.product.CereProductSku">
    SELECT b.sku_id,a.number stock_number from cere_after_product a
    LEFT JOIN cere_product_sku b ON a.sku_id=b.sku_id
    where after_id=#{afterId}
  </select>

  <select id="findById" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.after.CereOrderAfter">
    SELECT * FROM cere_order_after where after_id=#{afterId}
  </select>
</mapper>
