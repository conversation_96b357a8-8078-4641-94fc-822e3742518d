<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.after.CereAfterProductDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.after.CereAfterProduct">
    <id column="after_product_id" jdbcType="BIGINT" property="afterProductId" />
    <result column="after_id" jdbcType="BIGINT" property="afterId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="product_price" jdbcType="DECIMAL" property="productPrice" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="SKU" jdbcType="VARCHAR" property="SKU" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
  </resultMap>
  <insert id="insertSelective" keyColumn="after_product_id" keyProperty="afterProductId" parameterType="com.shop.cereshop.commons.domain.after.CereAfterProduct" useGeneratedKeys="true">
    insert into cere_after_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterId != null">
        after_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="productName != null and productName!=''">
        product_name,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="productPrice != null">
        product_price,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="SKU != null and SKU!=''">
        SKU,
      </if>
      <if test="weight != null">
        weight,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterId != null">
        #{afterId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="productName != null and productName!=''">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="productPrice != null">
        #{productPrice,jdbcType=DECIMAL},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="SKU != null and SKU!=''">
        #{SKU,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <insert id="insertBatchAttibutes" parameterType="java.util.List">
    insert into cere_after_product_attribute (after_product_id, sku_name, sku_value,name_code,value_code) values
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.afterProductId},
      #{item.skuName},
      #{item.skuValue},
      #{item.nameCode},
      #{item.valueCode}
      )
    </foreach>
  </insert>
</mapper>
