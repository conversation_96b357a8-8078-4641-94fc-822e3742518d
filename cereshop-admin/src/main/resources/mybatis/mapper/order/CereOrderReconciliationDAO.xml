<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.order.CereOrderReconciliationDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.order.CereOrderReconciliation">
    <id column="reconciliation_id" jdbcType="BIGINT" property="reconciliationId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="money" jdbcType="DECIMAL" property="money" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="type" jdbcType="BIT" property="type" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    reconciliation_id, order_id, money, `state`, `type`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_order_reconciliation
    where reconciliation_id = #{reconciliationId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_order_reconciliation
    where reconciliation_id = #{reconciliationId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="reconciliation_id" keyProperty="reconciliationId" parameterType="com.shop.cereshop.commons.domain.order.CereOrderReconciliation" useGeneratedKeys="true">
    insert into cere_order_reconciliation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="money != null">
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="type != null">
        #{type,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.order.CereOrderReconciliation">
    update cere_order_reconciliation
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where reconciliation_id = #{reconciliationId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.order.CereOrderReconciliation">
    update cere_order_reconciliation
    set order_id = #{orderId,jdbcType=BIGINT},
      money = #{money,jdbcType=DECIMAL},
      `state` = #{state,jdbcType=BIT},
      `type` = #{type,jdbcType=BIT},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where reconciliation_id = #{reconciliationId,jdbcType=BIGINT}
  </update>
</mapper>
