<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.order.CereShopOrderDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.order.CereShopOrder">
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="order_formid" jdbcType="VARCHAR" property="orderFormid" />
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_seckill_id" jdbcType="BIGINT" property="shopSeckillId" />
    <result column="shop_group_work_id" jdbcType="BIGINT" property="shopGroupWorkId" />
    <result column="shop_discount_id" jdbcType="BIGINT" property="shopDiscountId" />
    <result column="shop_operate_id" jdbcType="BIGINT" property="shopOperateId" />
    <result column="order_price" jdbcType="DECIMAL" property="orderPrice" />
    <result column="logistics_price" jdbcType="DECIMAL" property="logisticsPrice" />
    <result column="discount_price" jdbcType="DECIMAL" property="discountPrice" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="state" jdbcType="BIT" property="state" />
    <result column="payment_state" jdbcType="BIT" property="paymentState" />
    <result column="payment_mode" jdbcType="BIT" property="paymentMode" />
    <result column="payment_time" jdbcType="VARCHAR" property="paymentTime" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
    <result column="receive_name" jdbcType="VARCHAR" property="receiveName" />
    <result column="receive_phone" jdbcType="VARCHAR" property="receivePhone" />
    <result column="receive_adress" jdbcType="VARCHAR" property="receiveAdress" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="receive_time" jdbcType="VARCHAR" property="receiveTime" />
    <result column="postal_code" jdbcType="VARCHAR" property="postalCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="after_state" jdbcType="BIT" property="afterState" />
    <result column="logistics_id" jdbcType="BIGINT" property="logisticsId" />
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="direct_distributor_money" jdbcType="DECIMAL" property="directDistributorMoney" />
    <result column="indirect_distributor_money" jdbcType="DECIMAL" property="indirectDistributorMoney" />
    <result column="seckill_id" jdbcType="BIGINT" property="seckillId" />
    <result column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="polite_id" jdbcType="BIGINT" property="politeId" />
    <result column="scene_id" jdbcType="BIGINT" property="sceneId" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    order_id, parent_id,shop_id, order_formid, buyer_user_id,coupon_id,id,shop_seckill_id,shop_group_work_id,shop_discount_id,shop_operate_id,
    order_price, logistics_price,discount_price, price,
    `state`, payment_state, payment_mode, payment_time, customer_name, customer_phone,
    receive_name, receive_phone, receive_adress,address,receive_time, postal_code, remark, after_state, logistics_id,
    distributor_id, direct_distributor_money, indirect_distributor_money,seckill_id,discount_id,polite_id,scene_id,create_time,
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_shop_order
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_shop_order
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="order_id" keyProperty="orderId" parameterType="com.shop.cereshop.commons.domain.order.CereShopOrder" useGeneratedKeys="true">
    insert into cere_shop_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="orderFormid != null and orderFormid!=''">
        order_formid,
      </if>
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="id != null">
        id,
      </if>
      <if test="shopSeckillId != null">
        shop_seckill_id,
      </if>
      <if test="shopGroupWorkId != null">
        shop_group_work_id,
      </if>
      <if test="shopDiscountId != null">
        shop_discount_id,
      </if>
      <if test="shopOperateId != null">
        shop_operate_id,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="logisticsPrice != null">
        logistics_price,
      </if>
      <if test="discountPrice != null">
        discount_price,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="paymentState != null">
        payment_state,
      </if>
      <if test="paymentMode != null">
        payment_mode,
      </if>
      <if test="paymentTime != null and paymentTime!=''">
        payment_time,
      </if>
      <if test="customerName != null and customerName!=''">
        customer_name,
      </if>
      <if test="customerPhone != null and customerPhone!=''">
        customer_phone,
      </if>
      <if test="receiveName != null and receiveName!=''">
        receive_name,
      </if>
      <if test="receivePhone != null and receivePhone!=''">
        receive_phone,
      </if>
      <if test="receiveAdress != null and receiveAdress!=''">
        receive_adress,
      </if>
      <if test="address != null and address!=''">
        address,
      </if>
      <if test="receiveTime != null and receiveTime!=''">
        receive_time,
      </if>
      <if test="postalCode != null and postalCode!=''">
        postal_code,
      </if>
      <if test="remark != null and remark!=''">
        remark,
      </if>
      <if test="afterState != null">
        after_state,
      </if>
      <if test="logisticsId != null">
        logistics_id,
      </if>
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="directDistributorMoney != null">
        direct_distributor_money,
      </if>
      <if test="indirectDistributorMoney != null">
        indirect_distributor_money,
      </if>
      <if test="seckillId != null">
        seckill_id,
      </if>
      <if test="discountId != null">
        discount_id,
      </if>
      <if test="politeId != null">
        polite_id,
      </if>
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="orderFormid != null and orderFormid!=''">
        #{orderFormid,jdbcType=VARCHAR},
      </if>
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopSeckillId != null">
        #{shopSeckillId,jdbcType=BIGINT},
      </if>
      <if test="shopGroupWorkId != null">
        #{shopGroupWorkId,jdbcType=BIGINT},
      </if>
      <if test="shopDiscountId != null">
        #{shopDiscountId,jdbcType=BIGINT},
      </if>
      <if test="shopOperateId != null">
        #{shopOperateId,jdbcType=BIGINT},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="logisticsPrice != null">
        #{logisticsPrice,jdbcType=DECIMAL},
      </if>
      <if test="discountPrice != null">
        #{discountPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=BIT},
      </if>
      <if test="paymentState != null">
        #{paymentState,jdbcType=BIT},
      </if>
      <if test="paymentMode != null">
        #{paymentMode,jdbcType=BIT},
      </if>
      <if test="paymentTime != null and paymentTime!=''">
        #{paymentTime,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null and customerName!=''">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null and customerPhone!=''">
        #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null and receiveName!=''">
        #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null and receivePhone!=''">
        #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveAdress != null and receiveAdress!=''">
        #{receiveAdress,jdbcType=VARCHAR},
      </if>
      <if test="address != null and address!=''">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null and receiveTime!=''">
        #{receiveTime,jdbcType=VARCHAR},
      </if>
      <if test="postalCode != null and postalCode!=''">
        #{postalCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="afterState != null">
        #{afterState,jdbcType=BIT},
      </if>
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=BIGINT},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="directDistributorMoney != null">
        #{directDistributorMoney,jdbcType=DECIMAL},
      </if>
      <if test="indirectDistributorMoney != null">
        #{indirectDistributorMoney,jdbcType=DECIMAL},
      </if>
      <if test="seckillId != null">
        #{seckillId,jdbcType=BIGINT},
      </if>
      <if test="discountId != null">
        #{discountId,jdbcType=BIGINT},
      </if>
      <if test="politeId != null">
        #{politeId,jdbcType=BIGINT},
      </if>
      <if test="sceneId != null">
        #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.order.CereShopOrder">
    update cere_shop_order
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="orderFormid != null and orderFormid!=''">
        order_formid = #{orderFormid,jdbcType=VARCHAR},
      </if>
      <if test="buyerUserId != null">
        buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="shopSeckillId != null">
        shop_seckill_id = #{shopSeckillId,jdbcType=BIGINT},
      </if>
      <if test="shopGroupWorkId != null">
        shop_group_work_id = #{shopGroupWorkId,jdbcType=BIGINT},
      </if>
      <if test="shopDiscountId != null">
        shop_discount_id = #{shopDiscountId,jdbcType=BIGINT},
      </if>
      <if test="shopOperateId != null">
        shop_operate_id = #{shopOperateId,jdbcType=BIGINT},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="logisticsPrice != null">
        logistics_price = #{logisticsPrice,jdbcType=DECIMAL},
      </if>
      <if test="discountPrice != null">
        discount_price = #{discountPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="paymentState != null">
        payment_state = #{paymentState,jdbcType=BIT},
      </if>
      <if test="paymentMode != null">
        payment_mode = #{paymentMode,jdbcType=BIT},
      </if>
      <if test="paymentTime != null and paymentTime!=''">
        payment_time = #{paymentTime,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null and customerName!=''">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null and customerPhone!=''">
        customer_phone = #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null and receiveName!=''">
        receive_name = #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null and receivePhone!=''">
        receive_phone = #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveAdress != null and receiveAdress!=''">
        receive_adress = #{receiveAdress,jdbcType=VARCHAR},
      </if>
      <if test="address != null and address!=''">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null and receiveTime!=''">
        receive_time = #{receiveTime,jdbcType=VARCHAR},
      </if>
      <if test="postalCode != null and postalCode!=''">
        postal_code = #{postalCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="afterState != null">
        after_state = #{afterState,jdbcType=BIT},
      </if>
      <if test="logisticsId != null">
        logistics_id = #{logisticsId,jdbcType=BIGINT},
      </if>
      <if test="distributorId != null">
        distributor_id = #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="directDistributorMoney != null">
        direct_distributor_money = #{directDistributorMoney,jdbcType=DECIMAL},
      </if>
      <if test="indirectDistributorMoney != null">
        indirect_distributor_money = #{indirectDistributorMoney,jdbcType=DECIMAL},
      </if>
      <if test="seckillId != null">
        seckill_id = #{seckillId,jdbcType=BIGINT},
      </if>
      <if test="discountId != null">
        discount_id = #{discountId,jdbcType=BIGINT},
      </if>
      <if test="politeId != null">
        polite_id = #{politeId,jdbcType=BIGINT},
      </if>
      <if test="sceneId != null">
        scene_id = #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.order.CereShopOrder">
    update cere_shop_order
    set parent_id = #{parentId,jdbcType=BIGINT},
    shop_id = #{shopId,jdbcType=BIGINT},
    order_formid = #{orderFormid,jdbcType=VARCHAR},
    buyer_user_id = #{buyerUserId,jdbcType=BIGINT},
    coupon_id = #{couponId,jdbcType=BIGINT},
    id = #{id,jdbcType=BIGINT},
    shop_seckill_id = #{shopSeckillId,jdbcType=BIGINT},
    shop_group_work_id = #{shopGroupWorkId,jdbcType=BIGINT},
    shop_discount_id = #{shopDiscountId,jdbcType=BIGINT},
    shop_operate_id = #{shopOperateId,jdbcType=BIGINT},
    order_price = #{orderPrice,jdbcType=DECIMAL},
    logistics_price = #{logisticsPrice,jdbcType=DECIMAL},
    discount_price = #{discountPrice,jdbcType=DECIMAL},
    price = #{price,jdbcType=DECIMAL},
    `state` = #{state,jdbcType=BIT},
    payment_state = #{paymentState,jdbcType=BIT},
    payment_mode = #{paymentMode,jdbcType=BIT},
    payment_time = #{paymentTime,jdbcType=VARCHAR},
    customer_name = #{customerName,jdbcType=VARCHAR},
    customer_phone = #{customerPhone,jdbcType=VARCHAR},
    receive_name = #{receiveName,jdbcType=VARCHAR},
    receive_phone = #{receivePhone,jdbcType=VARCHAR},
    receive_adress = #{receiveAdress,jdbcType=VARCHAR},
    adress = #{address,jdbcType=VARCHAR},
    receive_time = #{receiveTime,jdbcType=VARCHAR},
    postal_code = #{postalCode,jdbcType=VARCHAR},
    remark = #{remark,jdbcType=VARCHAR},
    after_state = #{afterState,jdbcType=BIT},
    logistics_id = #{logisticsId,jdbcType=BIGINT},
    distributor_id = #{distributorId,jdbcType=BIGINT},
    direct_distributor_money = #{directDistributorMoney,jdbcType=DECIMAL},
    indirect_distributor_money = #{indirectDistributorMoney,jdbcType=DECIMAL},
    seckill_id = #{seckillId,jdbcType=BIGINT},
    discount_id = #{discountId,jdbcType=BIGINT},
    polite_id = #{politeId,jdbcType=BIGINT},
    scene_id = #{sceneId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=VARCHAR}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>

  <update id="updateAfterState" parameterType="com.shop.cereshop.commons.domain.order.CereShopOrder">
    update cere_shop_order
    <set>
      <if test="afterState != null">
        after_state = #{afterState,jdbcType=BIT},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>

  <update id="updateState" parameterType="com.shop.cereshop.commons.domain.order.CereShopOrder">
    update cere_shop_order
    <set>
      <if test="state != null">
        `state` = #{state,jdbcType=BIT},
      </if>
      <if test="afterState != null">
        after_state = #{afterState,jdbcType=BIT},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>

  <update id="updateBatchStock" parameterType="java.util.List" >
    update cere_product_sku
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="stock_number =case" suffix="end,">
        <foreach collection="skus" item="i" index="index">
          <if test="i.stockNumber!=null">
            when sku_id=#{i.skuId} then stock_number+#{i.stockNumber}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    <foreach collection="skus" separator="or" item="i" index="index" >
      sku_id=#{i.skuId}
    </foreach>
  </update>

  <select id="getAll" parameterType="com.shop.cereshop.admin.param.order.OrderGetAllParam" resultType="com.shop.cereshop.admin.page.order.ShopOrder">
    SELECT a.order_id,a.order_formid,a.price,IF(a.order_id IS NOT NULL,COUNT(b.product_id),NULL) number,a.state,a.remark,d.transaction_id,
    a.customer_name,a.payment_state,a.payment_mode,a.payment_time,a.create_time,a.receive_name,a.receive_phone,a.address,a.receive_adress,c.shop_name FROM cere_shop_order a
    LEFT JOIN cere_order_product b ON a.order_id=b.order_id
    LEFT JOIN cere_platform_shop c ON a.shop_id=c.shop_id
    LEFT JOIN cere_pay_log d ON a.order_formid=d.order_formid
    where 1=1
    <if test="shopName!=null and shopName!=''">
      and c.shop_name like concat ('%',#{shopName},'%')
    </if>
    <if test="orderFormid!=null and orderFormid!=''">
      and a.order_formid like concat ('%',#{orderFormid},'%')
    </if>
    <if test="search!=null and search!=''">
      <if test="searchType == 1">
        and a.order_id like concat ('%',#{search},'%')
      </if>
      <if test="searchType == 2">
        and a.customer_name like concat ('%',#{search},'%')
      </if>
      <if test="searchType == 3">
        and a.receive_name like concat ('%',#{search},'%')
      </if>
      <if test="searchType == 4">
        and a.receive_phone like concat ('%',#{search},'%')
      </if>
    </if>
    <if test="state!=null">
      and a.state=#{state}
    </if>
    <if test="orderIds != null and orderIds.size > 0">
      and a.order_id in
      <foreach collection="orderIds" item="orderId" index="i" open="(" separator="," close=")">
        #{orderId}
      </foreach>
    </if>
    <if test="startTime!=null and startTime!=''">
      and a.create_time&gt;=#{startTime} and a.create_time&lt;=#{endTime}
    </if>
    GROUP BY a.order_id
    ORDER BY a.update_time DESC,a.create_time DESC
  </select>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.order.ShopOrder">
    SELECT a.order_id,a.order_formid,b.transaction_id,GROUP_CONCAT(c.after_formid) afterFormIds,
    a.state,a.after_state,a.payment_mode,d.logistics_name,a.create_time,a.payment_time,
    a.customer_name,a.remark,a.receive_name,a.receive_phone,a.receive_adress,a.address,a.postal_code,
    a.order_price,a.logistics_price,a.price,a.buyer_user_id,IF(c.after_state=1,'审核中',
    IF(c.after_state=2,'退款中',IF(c.after_state=3,'退货中',IF(c.after_state=4,'退款成功',
    IF(c.after_state=5,'退款失败',IF(c.after_state=6,'审核不通过',IF(
    c.after_state=7,'评审中',IF(c.after_state=8,'退货完成,拒绝退款',
    IF(c.after_state=9,'已关闭','审核通过'))))))))) after_state_name,e.shop_name from cere_shop_order a
    LEFT JOIN cere_pay_log b ON a.order_formid=b.order_formid and b.state = '支付'
    LEFT JOIN cere_order_after c ON a.order_id=c.order_id
    LEFT JOIN cere_order_logistics d ON a.logistics_id=d.logistics_id
    LEFT JOIN cere_platform_shop e ON a.shop_id=e.shop_id
    where a.order_id=#{orderId}
  </select>

  <select id="getOrderTotals" parameterType="java.lang.Object" resultType="java.lang.Integer">
    SELECT COUNT(*) from cere_shop_order where buyer_user_id=#{buyerUserId} and state in (2,3,4)
  </select>

  <select id="getProducts" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.product.Product">
        SELECT order_product_id,
               op.product_id,
               op.sku_id,
               op.product_name,
               op.product_price,
               op.number,
               op.image,
               op.product_price * op.number total,
               cb.brand_name,
               op.SKU
        from cere_order_product op
        join cere_shop_product sp on op.product_id = sp.product_id
        left join cere_brand cb on cb.id = sp.brand_id
        where order_id = #{orderId}
  </select>

  <select id="findSkuAttribute" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.product.SkuDetail">
    SELECT sku_name,sku_value FROM cere_order_product_attribute where order_product_id=#{orderProductId}
  </select>

  <select id="findAfter"  resultType="com.shop.cereshop.admin.page.order.ShopOrderAfter">
    SELECT after_id,order_id,after_state FROM cere_order_after where 1=1
    group by order_id
    order by order_id,create_time desc
  </select>

  <select id="selectChannelOrderUserCount" resultType="java.lang.Integer">
    select count(distinct a.buyer_user_id) from cere_shop_order a
    join cere_buyer_user b on b.buyer_user_id = a.buyer_user_id
    where b.channel_code = #{channelCode} and a.payment_state = 1
  </select>

  <select id="selectChannelOrderCount" resultType="java.lang.Integer">
    select count(distinct a.order_id) from cere_shop_order a
    join cere_buyer_user b on b.buyer_user_id = a.buyer_user_id
    where b.channel_code = #{channelCode} and a.payment_state = 1
  </select>

  <select id="selectChannelOrderAmount" resultType="java.math.BigDecimal">
    select ifnull(sum(price), 0) from cere_shop_order a
    join cere_buyer_user b on b.buyer_user_id = a.buyer_user_id
    where b.channel_code = #{channelCode} and a.payment_state = 1
  </select>

  <select id="selectSalesVolumeBySkuIdList" resultType="java.util.Map">
    select sku_id, sum(number) as sales_volume
    from cere_shop_order a join cere_order_product b on b.order_id = a.order_id
    and a.payment_state = 1 and b.sku_id in
    <foreach collection="list" item="skuId" open="(" separator="," close=")">
      #{skuId}
    </foreach>
    group by b.sku_id
  </select>
</mapper>
