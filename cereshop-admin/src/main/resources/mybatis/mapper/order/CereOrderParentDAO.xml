<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.order.CereOrderParentDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.order.CereOrderParent">
    <id column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="parent_formid" jdbcType="VARCHAR" property="parentFormid" />
    <result column="order_price" jdbcType="DECIMAL" property="orderPrice" />
    <result column="logistics_price" jdbcType="DECIMAL" property="logisticsPrice" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    parent_id, parent_formid, order_price, logistics_price, price, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_order_parent
    where parent_id = #{parentId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_order_parent
    where parent_id = #{parentId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="parent_id" keyProperty="parentId" parameterType="com.shop.cereshop.commons.domain.order.CereOrderParent" useGeneratedKeys="true">
    insert into cere_order_parent
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentFormid != null and parentFormid!=''">
        parent_formid,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="logisticsPrice != null">
        logistics_price,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentFormid != null and parentFormid!=''">
        #{parentFormid,jdbcType=VARCHAR},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="logisticsPrice != null">
        #{logisticsPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.order.CereOrderParent">
    update cere_order_parent
    <set>
      <if test="parentFormid != null and parentFormid!=''">
        parent_formid = #{parentFormid,jdbcType=VARCHAR},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=DECIMAL},
      </if>
      <if test="logisticsPrice != null">
        logistics_price = #{logisticsPrice,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where parent_id = #{parentId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.order.CereOrderParent">
    update cere_order_parent
    set parent_formid = #{parentFormid,jdbcType=VARCHAR},
      order_price = #{orderPrice,jdbcType=DECIMAL},
      logistics_price = #{logisticsPrice,jdbcType=DECIMAL},
      price = #{price,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where parent_id = #{parentId,jdbcType=BIGINT}
  </update>
</mapper>
