<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.shop.CerePriceProductDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CerePriceProduct">
    <result column="price_id" jdbcType="BIGINT" property="priceId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.tool.CerePriceProduct">
    insert into cere_price_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="priceId != null">
        price_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="priceId != null">
        #{priceId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>
