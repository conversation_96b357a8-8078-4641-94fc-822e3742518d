<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.shop.CereComposeProductDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.tool.CereComposeProduct">
    <result column="compose_id" jdbcType="BIGINT" property="composeId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.tool.CereComposeProduct">
    insert into cere_compose_product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="composeId != null">
        compose_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="composeId != null">
        #{composeId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>
