<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.shop.CereShopIndividualBusinessesDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.shop.CereShopIndividualBusinesses">
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="subject_name" jdbcType="VARCHAR" property="subjectName" />
    <result column="subject_code" jdbcType="VARCHAR" property="subjectCode" />
    <result column="subject_region" jdbcType="VARCHAR" property="subjectRegion" />
    <result column="subject_adress" jdbcType="VARCHAR" property="subjectAdress" />
    <result column="subject_start_time" jdbcType="VARCHAR" property="subjectStartTime" />
    <result column="subject_end_time" jdbcType="VARCHAR" property="subjectEndTime" />
    <result column="subject_license" jdbcType="VARCHAR" property="subjectLicense" />
    <result column="subject_operator" jdbcType="VARCHAR" property="subjectOperator" />
    <result column="subject_card_type" jdbcType="BIGINT" property="subjectCardType" />
    <result column="subject_id_card" jdbcType="VARCHAR" property="subjectIdCard" />
    <result column="subject_card_start_time" jdbcType="VARCHAR" property="subjectCardStartTime" />
    <result column="subject_card_end_time" jdbcType="VARCHAR" property="subjectCardEndTime" />
    <result column="subject_card_positive" jdbcType="VARCHAR" property="subjectCardPositive" />
    <result column="subject_card_side" jdbcType="VARCHAR" property="subjectCardSide" />
    <result column="administrators_phone" jdbcType="VARCHAR" property="administratorsPhone" />
    <result column="administrators_email" jdbcType="VARCHAR" property="administratorsEmail" />
    <result column="account_type" jdbcType="BIT" property="accountType" />
    <result column="account_open_bank" jdbcType="BIGINT" property="accountOpenBank" />
    <result column="account_bank_region" jdbcType="VARCHAR" property="accountBankRegion" />
    <result column="account_bank_card" jdbcType="VARCHAR" property="accountBankCard" />
    <result column="shop_abbreviation" jdbcType="VARCHAR" property="shopAbbreviation" />
    <result column="service_phone" jdbcType="VARCHAR" property="servicePhone" />
    <result column="service_providing" jdbcType="BIGINT" property="serviceProviding" />
    <result column="shop_index_image" jdbcType="VARCHAR" property="shopIndexImage" />
    <result column="shop_backstage_image" jdbcType="VARCHAR" property="shopBackstageImage" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.shop.CereShopIndividualBusinesses">
    insert into cere_shop_individual_businesses
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="subjectName != null and subjectName!=''">
        subject_name,
      </if>
      <if test="subjectCode != null and subjectCode!=''">
        subject_code,
      </if>
      <if test="subjectRegion != null and subjectRegion!=''">
        subject_region,
      </if>
      <if test="subjectAdress != null and subjectAdress!=''">
        subject_adress,
      </if>
      <if test="subjectStartTime != null and subjectStartTime!=''">
        subject_start_time,
      </if>
      <if test="subjectEndTime != null and subjectEndTime!=''">
        subject_end_time,
      </if>
      <if test="subjectLicense != null and subjectLicense!=''">
        subject_license,
      </if>
      <if test="subjectOperator != null and subjectOperator!=''">
        subject_operator,
      </if>
      <if test="subjectCardType != null">
        subject_card_type,
      </if>
      <if test="subjectIdCard != null and subjectIdCard!=''">
        subject_id_card,
      </if>
      <if test="subjectCardStartTime != null and subjectCardStartTime!=''">
        subject_card_start_time,
      </if>
      <if test="subjectCardEndTime != null and subjectCardEndTime!=''">
        subject_card_end_time,
      </if>
      <if test="subjectCardPositive != null and subjectCardPositive!=''">
        subject_card_positive,
      </if>
      <if test="subjectCardSide != null and subjectCardSide!=''">
        subject_card_side,
      </if>
      <if test="administratorsPhone != null and administratorsPhone!=''">
        administrators_phone,
      </if>
      <if test="administratorsEmail != null and administratorsEmail!=''">
        administrators_email,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="accountOpenBank != null">
        account_open_bank,
      </if>
      <if test="accountBankRegion != null and accountBankRegion!=''">
        account_bank_region,
      </if>
      <if test="accountBankCard != null and accountBankCard!=''">
        account_bank_card,
      </if>
      <if test="shopAbbreviation != null and shopAbbreviation!=''">
        shop_abbreviation,
      </if>
      <if test="servicePhone != null and servicePhone!=''">
        service_phone,
      </if>
      <if test="serviceProviding != null">
        service_providing,
      </if>
      <if test="shopIndexImage != null and shopIndexImage!=''">
        shop_index_image,
      </if>
      <if test="shopBackstageImage != null and shopBackstageImage!=''">
        shop_backstage_image,
      </if>
      <if test="remark != null and remark!=''">
        remark,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="subjectName != null and subjectName!=''">
        #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="subjectCode != null and subjectCode!=''">
        #{subjectCode,jdbcType=VARCHAR},
      </if>
      <if test="subjectRegion != null and subjectRegion!=''">
        #{subjectRegion,jdbcType=VARCHAR},
      </if>
      <if test="subjectAdress != null and subjectAdress!=''">
        #{subjectAdress,jdbcType=VARCHAR},
      </if>
      <if test="subjectStartTime != null and subjectStartTime!=''">
        #{subjectStartTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectEndTime != null and subjectEndTime!=''">
        #{subjectEndTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectLicense != null and subjectLicense!=''">
        #{subjectLicense,jdbcType=VARCHAR},
      </if>
      <if test="subjectOperator != null and subjectOperator!=''">
        #{subjectOperator,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardType != null">
        #{subjectCardType,jdbcType=BIGINT},
      </if>
      <if test="subjectIdCard != null and subjectIdCard!=''">
        #{subjectIdCard,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardStartTime != null and subjectCardStartTime!=''">
        #{subjectCardStartTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardEndTime != null and subjectCardEndTime!=''">
        #{subjectCardEndTime,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardPositive != null and subjectCardPositive!=''">
        #{subjectCardPositive,jdbcType=VARCHAR},
      </if>
      <if test="subjectCardSide != null and subjectCardSide!=''">
        #{subjectCardSide,jdbcType=VARCHAR},
      </if>
      <if test="administratorsPhone != null and administratorsPhone!=''">
        #{administratorsPhone,jdbcType=VARCHAR},
      </if>
      <if test="administratorsEmail != null and administratorsEmail!=''">
        #{administratorsEmail,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=BIT},
      </if>
      <if test="accountOpenBank != null">
        #{accountOpenBank,jdbcType=BIGINT},
      </if>
      <if test="accountBankRegion != null and accountBankRegion!=''">
        #{accountBankRegion,jdbcType=VARCHAR},
      </if>
      <if test="accountBankCard != null and accountBankCard!=''">
        #{accountBankCard,jdbcType=VARCHAR},
      </if>
      <if test="shopAbbreviation != null and shopAbbreviation!=''">
        #{shopAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="servicePhone != null and servicePhone!=''">
        #{servicePhone,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviding != null">
        #{serviceProviding,jdbcType=BIGINT},
      </if>
      <if test="shopIndexImage != null and shopIndexImage!=''">
        #{shopIndexImage,jdbcType=VARCHAR},
      </if>
      <if test="shopBackstageImage != null and shopBackstageImage!=''">
        #{shopBackstageImage,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark!=''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="findByShopId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.shop.CereShopIndividualBusinesses">
    SELECT * FROM cere_shop_individual_businesses where shop_id=#{shopId}
  </select>
</mapper>
