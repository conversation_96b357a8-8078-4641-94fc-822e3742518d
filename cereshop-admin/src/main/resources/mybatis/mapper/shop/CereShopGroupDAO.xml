<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.shop.CereShopGroupDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.shop.CereShopGroup">
    <id column="shop_group_id" jdbcType="BIGINT" property="shopGroupId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="group_image" jdbcType="VARCHAR" property="groupImage" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    shop_group_id, shop_id, group_name, group_image, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_shop_group
    where shop_group_id = #{shopGroupId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_shop_group
    where shop_group_id = #{shopGroupId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="shop_group_id" keyProperty="shopGroupId" parameterType="com.shop.cereshop.commons.domain.shop.CereShopGroup" useGeneratedKeys="true">
    insert into cere_shop_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="groupName != null and groupName!=''">
        group_name,
      </if>
      <if test="groupImage != null and groupImage!=''">
        group_image,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null and groupName!=''">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="groupImage != null and groupImage!=''">
        #{groupImage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.shop.CereShopGroup">
    update cere_shop_group
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null and groupName!=''">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="groupImage != null and groupImage!=''">
        group_image = #{groupImage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where shop_group_id = #{shopGroupId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.shop.CereShopGroup">
    update cere_shop_group
    set shop_id = #{shopId,jdbcType=BIGINT},
      group_name = #{groupName,jdbcType=VARCHAR},
      group_image = #{groupImage,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where shop_group_id = #{shopGroupId,jdbcType=BIGINT}
  </update>
</mapper>
