<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.label.CereBuyerLabelDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.label.CereBuyerLabel">
    <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId" />
    <result column="buyer_label_id" jdbcType="BIGINT" property="buyerLabelId" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.label.CereBuyerLabel">
    insert into cere_buyer_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyerUserId != null">
        buyer_user_id,
      </if>
      <if test="buyerLabelId != null">
        buyer_label_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyerUserId != null">
        #{buyerUserId,jdbcType=BIGINT},
      </if>
      <if test="buyerLabelId != null">
        #{buyerLabelId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <delete id="deleteLabelUser" parameterType="java.util.List">
    DELETE FROM cere_buyer_label where buyer_label_id in (
    <foreach collection="ids" item="id" index="index" separator=",">
      #{id}
    </foreach>
    )
  </delete>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into cere_buyer_label (buyer_user_id, buyer_label_id) values
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.buyerUserId},
      #{item.buyerLabelId}
      )
    </foreach>
  </insert>

  <select id="findAlreadyByUser" parameterType="com.shop.cereshop.admin.param.buyer.BuyerSaveUserLabelParam" resultType="java.lang.Long">
    SELECT buyer_label_id FROM cere_buyer_label where buyer_user_id=#{buyerUserId}
    and buyer_label_id in (
    <foreach collection="buyerLabelIds" item="id" index="index" separator=",">
      #{id}
    </foreach>
    )
  </select>

  <select id="findByBuyerUserId" resultType="java.lang.Long">
    SELECT buyer_label_id FROM cere_buyer_label where buyer_user_id = #{buyerUserId}
  </select>
</mapper>
