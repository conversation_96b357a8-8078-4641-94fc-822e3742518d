<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.label.CereLabelSourceDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.label.CereLabelSource">
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="link" jdbcType="VARCHAR" property="link" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.shop.cereshop.commons.domain.label.CereLabelSource">
    insert into cere_label_source
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="labelId != null">
        label_id,
      </if>
      <if test="image != null and image!=''">
        image,
      </if>
      <if test="link != null and link!=''">
        link,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="labelId != null">
        #{labelId,jdbcType=BIGINT},
      </if>
      <if test="image != null and image!=''">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="link != null and link!=''">
        #{link,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
