<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.recommend.CereRecommendTrendsDAO">


    <select id="trendsPage" parameterType="com.shop.cereshop.admin.param.recommend.RecommendTrendPageParam"
            resultType="com.shop.cereshop.admin.page.recommend.CereRecommendTrendsPage">
        select * from cere_recommend_trends
        where 1 = 1
        <if test="recommendType != null">
            and recommend_type = #{recommendType}
        </if>
        <if test="publishStatus != null">
            and publish_status = #{publishStatus}
        </if>
        <if test="fileType != null">
            and file_type = #{fileType}
        </if>
        <if test="publishStartTime != null and publishStartTime != '' and publishEndTime != null and publishEndTime != ''">
            and publish_time between #{publishStartTime} and #{publishEndTime}
        </if>
        order by recommend_id desc
    </select>

    <select id="getTrendsDetail" resultType="com.shop.cereshop.admin.page.recommend.CereRecommendTrendsDetail">
        select crt.*, cps.shop_name
        from cere_recommend_trends as crt
                 inner join cere_platform_shop as cps on crt.shop_id = cps.shop_id
        where recommend_id = #{recommendId}
    </select>

    <update id="updateTrends" parameterType="com.shop.cereshop.commons.domain.recommend.CereRecommendTrends">
        update cere_recommend_trends
        <set>
            <if test="publishStatus != null">
                publish_status = #{publishStatus},
            </if>
            <if test="reviewContent != null and reviewContent != ''">
                review_content = #{reviewContent},
            </if>
            <if test="publishTime != null and publishTime != ''">
                publish_time = #{publishTime},
            </if>
        </set>
        where recommend_id = #{recommendId}
    </update>

    <delete id="deleteTrends">
        delete from cere_recommend_trends where recommend_id = #{recommendId}
    </delete>
</mapper>
