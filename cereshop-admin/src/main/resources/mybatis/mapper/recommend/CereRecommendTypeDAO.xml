<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.recommend.CereRecommendTypeDAO">
    <insert id="insertRecommendType" parameterType="com.shop.cereshop.commons.domain.recommend.CereRecommendType">
        INSERT INTO cere_recommend_type(name,sort) VALUES (#{name},#{sort})
    </insert>

    <select id="checkName"  resultType="com.shop.cereshop.commons.domain.recommend.CereRecommendType">
        select * from cere_recommend_type
        where name = #{name}
        <if test="recommendTypeId != null">
            and recommend_type_id != #{recommendTypeId}
        </if>
    </select>

    <select id="getRecommendTypeById"  resultType="com.shop.cereshop.admin.page.recommend.CereRecommendTypePage">
        select * from cere_recommend_type
        where recommend_type_id = #{recommendTypeId}
    </select>

    <select id="recommendTypePage"  resultType="com.shop.cereshop.admin.page.recommend.CereRecommendTypePage">
        select * from cere_recommend_type
        where 1 = 1
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
    </select>

    <update id="updateRecommendType" parameterType="com.shop.cereshop.admin.param.recommend.RecommendTypeUpdateParam">
        update cere_recommend_type
        set name = #{name},sort = #{sort}
        where recommend_type_id = #{recommendTypeId}
    </update>

    <delete id="deleteRecommendType">
        delete from cere_recommend_type where recommend_type_id = #{recommendTypeId}
    </delete>
</mapper>
