<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.credit.CereCreditRecordDAO">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.credit.CereCreditRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="buyer_user_id" jdbcType="BIGINT" property="buyerUserId"/>
        <result column="record_type" jdbcType="INTEGER" property="recordType"/>
        <result column="opt_type" jdbcType="INTEGER" property="optType"/>
        <result column="record_content" jdbcType="VARCHAR" property="recordContent"/>
        <result column="credit" jdbcType="INTEGER" property="credit"/>
        <result column="remain_credit" jdbcType="INTEGER" property="remainCredit"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, buyer_user_id, record_type, opt_type, record_content,
        credit, remain_credit, create_time, update_time
    </sql>

    <select id="getAll" resultType="com.shop.cereshop.admin.page.credit.CereCreditRecordPage">
        select a.id, a.buyer_user_id, b.name, b.phone,
        a.record_type, a.opt_type, a.record_content, a.credit, a.remain_credit,
        a.create_time
        from cere_credit_record a
        join cere_buyer_user b on a.buyer_user_id = b.buyer_user_id
        <if test="search != null and search != ''">
            and (a.buyer_user_id = #{search} or b.name like concat('%', #{search}, '%'))
        </if>
    </select>


</mapper>
