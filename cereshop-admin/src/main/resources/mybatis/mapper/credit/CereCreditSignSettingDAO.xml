<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.credit.CereCreditSignSettingDAO">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.credit.CereCreditSignSetting">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="day" jdbcType="INTEGER" property="day"/>
        <result column="credit" jdbcType="INTEGER" property="credit"/>
        <result column="display" jdbcType="INTEGER" property="display"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,create_time,update_time,
        day, credit, display, sort
    </sql>

    <select id="getAll" resultMap="BaseResultMap">
        select id, day, credit, display, sort, create_time, update_time
        from cere_credit_sign_setting
    </select>
    <select id="selectExistsDay" resultType="java.lang.Integer">
        select count(id) from
        cere_credit_sign_setting where day = #{day}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

</mapper>
