<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.product.CereProductSkuDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.product.CereProductSku">
    <id column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="SKU" jdbcType="VARCHAR" property="SKU" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
    <result column="stock_number" jdbcType="INTEGER" property="stockNumber" />
    <result column="total" jdbcType="INTEGER" property="total" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="sku_image" jdbcType="VARCHAR" property="skuImage" />
    <result column="style" jdbcType="BIT" property="style" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    sku_id, product_id,SKU, price, original_price, stock_number,total, weight,
    sku_image, `style`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_product_sku
    where sku_id = #{skuId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_product_sku
    where sku_id = #{skuId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="sku_id" keyProperty="skuId" parameterType="com.shop.cereshop.commons.domain.product.CereProductSku" useGeneratedKeys="true">
    insert into cere_product_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        product_id,
      </if>
      <if test="SKU != null and SKU!=''">
        SKU,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="stockNumber != null">
        stock_number,
      </if>
      <if test="total != null">
        total,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="skuImage != null and skuImage!=''">
        sku_image,
      </if>
      <if test="style != null">
        `style`,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="SKU != null and SKU!=''">
        #{SKU,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="stockNumber != null">
        #{stockNumber,jdbcType=INTEGER},
      </if>
      <if test="total != null">
        #{total,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DECIMAL},
      </if>
      <if test="skuImage != null and skuImage!=''">
        #{skuImage,jdbcType=VARCHAR},
      </if>
      <if test="style != null">
        #{style,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.product.CereProductSku">
    update cere_product_sku
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="SKU != null and SKU!=''">
        SKU = #{SKU,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="stockNumber != null">
        stock_number = #{stockNumber,jdbcType=INTEGER},
      </if>
      <if test="total != null">
        total = #{total,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=DECIMAL},
      </if>
      <if test="skuImage != null and skuImage!=''">
        sku_image = #{skuImage,jdbcType=VARCHAR},
      </if>
      <if test="style != null">
        `style` = #{style,jdbcType=BIT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where sku_id = #{skuId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.product.CereProductSku">
    update cere_product_sku
    set product_id = #{productId,jdbcType=BIGINT},
    SKU = #{SKU,jdbcType=VARCHAR},
    price = #{price,jdbcType=DECIMAL},
    original_price = #{originalPrice,jdbcType=DECIMAL},
    stock_number = #{stockNumber,jdbcType=INTEGER},
    total = #{total,jdbcType=INTEGER},
    weight = #{weight,jdbcType=DECIMAL},
    sku_image = #{skuImage,jdbcType=VARCHAR},
    `style` = #{style,jdbcType=BIT},
    create_time = #{createTime,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=VARCHAR}
    where sku_id = #{skuId,jdbcType=BIGINT}
  </update>

  <select id="findStockNumber" parameterType="java.lang.Object" resultType="java.lang.Integer">
    SELECT stock_number from cere_product_sku
    where sku_id=#{skuId}
  </select>

  <select id="findByProductId" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.product.Sku">
    SELECT product_id, sku_id,
           price, original_price, stock_number, total
           weight, sku_image, `style`, SKU
    FROM cere_product_sku where product_id=#{productId}
  </select>

  <select id="findNameByProductId" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.product.SkuNameParam">
    SELECT sku_name,name_code code,sku_id,need FROM cere_sku_name where sku_id in (select sku_id from cere_product_sku where product_id=#{productId})
    GROUP BY name_code
  </select>

  <select id="findByName" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.product.SkuValueParam">
    SELECT sku_value,image,value_code FROM cere_sku_name where sku_name=#{skuName}
    and sku_id in (SELECT sku_id FROM cere_product_sku where product_id=#{productId})
    GROUP BY value_code
  </select>

  <select id="findValueByProductId" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.product.CereSkuName">
    SELECT sku_value,image,need FROM cere_sku_name where sku_id in (select sku_id from cere_product_sku where product_id=#{productId})
  </select>

  <select id="findBySkuId" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.product.SkuNameValueParam">
    SELECT name_code code,value_code FROM cere_sku_name where sku_id=#{skuId}
  </select>
</mapper>
