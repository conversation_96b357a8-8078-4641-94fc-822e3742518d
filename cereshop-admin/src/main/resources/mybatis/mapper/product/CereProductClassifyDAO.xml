<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shop.cereshop.admin.dao.product.CereProductClassifyDAO">
  <resultMap id="BaseResultMap" type="com.shop.cereshop.commons.domain.product.CereProductClassify">
    <id column="classify_id" jdbcType="BIGINT" property="classifyId" />
    <result column="classify_pid" jdbcType="BIGINT" property="classifyPid" />
    <result column="classify_hierarchy" jdbcType="VARCHAR" property="classifyHierarchy" />
    <result column="classify_level" jdbcType="TINYINT" property="classifyLevel" />
    <result column="classify_level_hierarchy" jdbcType="VARCHAR" property="classifyLevelHierarchy" />
    <result column="classify_name" jdbcType="VARCHAR" property="classifyName" />
    <result column="classify_image" jdbcType="VARCHAR" property="classifyImage" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="link" jdbcType="BIGINT" property="link" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    classify_id, classify_pid, classify_hierarchy, classify_level, classify_level_hierarchy,
    classify_name, classify_image, sort, link, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cere_product_classify
    where classify_id = #{classifyId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cere_product_classify
    where classify_id = #{classifyId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="classify_id" keyProperty="classifyId" parameterType="com.shop.cereshop.commons.domain.product.CereProductClassify" useGeneratedKeys="true">
    insert into cere_product_classify
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="classifyPid != null">
        classify_pid,
      </if>
      <if test="classifyHierarchy != null and classifyHierarchy!=''">
        classify_hierarchy,
      </if>
      <if test="classifyLevel != null">
        classify_level,
      </if>
      <if test="classifyLevelHierarchy != null and classifyLevelHierarchy!=''">
        classify_level_hierarchy,
      </if>
      <if test="classifyName != null and classifyName!=''">
        classify_name,
      </if>
      <if test="classifyImage != null and classifyImage!=''">
        classify_image,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="link != null and link!=''">
        link,
      </if>
      <if test="createTime != null and createTime!=''">
        create_time,
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="classifyPid != null">
        #{classifyPid,jdbcType=BIGINT},
      </if>
      <if test="classifyHierarchy != null and classifyHierarchy!=''">
        #{classifyHierarchy,jdbcType=VARCHAR},
      </if>
      <if test="classifyLevel != null">
        #{classifyLevel,jdbcType=TINYINT},
      </if>
      <if test="classifyLevelHierarchy != null and classifyLevelHierarchy!=''">
        #{classifyLevelHierarchy,jdbcType=VARCHAR},
      </if>
      <if test="classifyName != null and classifyName!=''">
        #{classifyName,jdbcType=VARCHAR},
      </if>
      <if test="classifyImage != null and classifyImage!=''">
        #{classifyImage,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="link != null and link!=''">
        #{link,jdbcType=BIGINT},
      </if>
      <if test="createTime != null and createTime!=''">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        #{updateTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shop.cereshop.commons.domain.product.CereProductClassify">
    update cere_product_classify
    <set>
      <if test="classifyPid != null">
        classify_pid = #{classifyPid,jdbcType=BIGINT},
      </if>
      <if test="classifyHierarchy != null and classifyHierarchy!=''">
        classify_hierarchy = #{classifyHierarchy,jdbcType=VARCHAR},
      </if>
      <if test="classifyLevel != null">
        classify_level = #{classifyLevel,jdbcType=TINYINT},
      </if>
      <if test="classifyLevelHierarchy != null and classifyLevelHierarchy!=''">
        classify_level_hierarchy = #{classifyLevelHierarchy,jdbcType=VARCHAR},
      </if>
      <if test="classifyName != null and classifyName!=''">
        classify_name = #{classifyName,jdbcType=VARCHAR},
      </if>
      <if test="classifyImage != null and classifyImage!=''">
        classify_image = #{classifyImage,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="link != null and link!=''">
        link = #{link,jdbcType=BIGINT},
      </if>
      <if test="createTime != null and createTime!=''">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null and updateTime!=''">
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
    </set>
    where classify_id = #{classifyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shop.cereshop.commons.domain.product.CereProductClassify">
    update cere_product_classify
    set classify_pid = #{classifyPid,jdbcType=BIGINT},
      classify_hierarchy = #{classifyHierarchy,jdbcType=VARCHAR},
      classify_level = #{classifyLevel,jdbcType=TINYINT},
      classify_level_hierarchy = #{classifyLevelHierarchy,jdbcType=VARCHAR},
      classify_name = #{classifyName,jdbcType=VARCHAR},
      classify_image = #{classifyImage,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      link = #{link,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where classify_id = #{classifyId,jdbcType=BIGINT}
  </update>

  <select id="getById" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.product.CereProductClassify">
    SELECT classify_id,classify_name,classify_level_hierarchy,classify_image,link,classify_level FROM cere_product_classify
    where classify_id=#{classifyId}
  </select>

  <select id="getAll" resultType="com.shop.cereshop.commons.domain.product.CereProductClassify">
    SELECT classify_id,classify_name FROM cere_product_classify
    where classify_level=1 ORDER BY update_time DESC,create_time DESC
  </select>

  <select id="checkProduct" parameterType="java.lang.Object" resultType="com.shop.cereshop.commons.domain.product.CereShopProduct">
    SELECT
      a.product_id
    from cere_shop_product a
    <where>
        <if test="classifyLevel == 1">
          a.classify_id1 = #{classifyId}
        </if>
        <if test="classifyLevel == 2">
          a.classify_id2 = #{classifyId}
        </if>
        <if test="classifyLevel == 3">
          a.classify_id3 = #{classifyId}
        </if>
    </where>
  </select>

  <select id="findByPid" parameterType="java.lang.Object" resultType="com.shop.cereshop.admin.page.product.ProductClassify">
    SELECT classify_id id,classify_name categoryName,classify_image categoryImg
    ,link,classify_level depth FROM cere_product_classify where classify_pid=#{classifyId}
  </select>

  <update id="updateBatchLevelHierarchy" parameterType="java.util.List" >
    update cere_product_classify
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="classify_level_hierarchy =case" suffix="end,">
        <foreach collection="list" item="i" index="index">
          <if test="i.classifyLevelHierarchy != null">
            when classify_id = #{i.classifyId} then #{i.classifyLevelHierarchy}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    <foreach collection="list" separator="or" item="i" index="index" >
      classify_id=#{i.classifyId}
    </foreach>
  </update>

  <select id="findAll" resultType="com.shop.cereshop.commons.domain.product.Classify">
    SELECT classify_id id,classify_pid parentId,classify_name categoryName,classify_level depth,classify_image classifyImage
    FROM cere_product_classify where classify_level=1
  </select>

  <select id="findChilds" resultType="com.shop.cereshop.commons.domain.product.Classify">
    SELECT classify_id id,classify_pid parentId,classify_name categoryName,classify_level depth,classify_image classifyImage
    FROM cere_product_classify where classify_level<![CDATA[!= ]]>1
  </select>

  <select id="selectByPid" resultType="com.shop.cereshop.commons.domain.product.CereProductClassify">
    select
      <include refid="Base_Column_List"/>
    from cere_product_classify
    where classify_pid = #{classifyId,jdbcType=BIGINT}
  </select>

  <select id="selectByPidList" resultType="com.shop.cereshop.commons.domain.product.CereProductClassify">
    select
      <include refid="Base_Column_List"/>
    from cere_product_classify
    where classify_pid in
    (
      <foreach collection="ids" item="id" index="index" separator=",">
        #{id}
      </foreach>
    )
  </select>

  <select id="checkProductByIdList" resultType="com.shop.cereshop.commons.domain.product.CereShopProduct">
    select
      product_id
    from cere_shop_product
    where classify_id1 in
    (
      <foreach collection="ids" item="id" index="index" separator=",">
        #{id}
      </foreach>
    )
    or classify_id2 in
    (
      <foreach collection="ids" item="id" index="index" separator=",">
        #{id}
      </foreach>
    )
    or classify_id3 in
    (
      <foreach collection="ids" item="id" index="index" separator=",">
        #{id}
      </foreach>
    )
  </select>

  <delete id="deleteByIds" parameterType="java.util.List">
    DELETE FROM cere_product_classify
    where classify_id in
    (
      <foreach collection="ids" item="id" index="index" separator=",">
        #{id}
      </foreach>
    )
  </delete>

  <delete id="deleteByPid">
    delete from cere_product_classify
    where classify_pid = #{classifyId,jdbcType=BIGINT}
  </delete>
</mapper>
