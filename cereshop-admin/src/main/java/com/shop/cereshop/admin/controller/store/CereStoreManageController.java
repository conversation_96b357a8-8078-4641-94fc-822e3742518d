/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.admin.controller.store;

import com.shop.cereshop.business.service.store.CereMerchantStoreService;
import com.shop.cereshop.business.service.store.CereStoreStaffService;
import com.shop.cereshop.business.service.store.CereVerifyService;
import com.shop.cereshop.commons.constant.CoReturnFormat;
import com.shop.cereshop.commons.domain.store.CereMerchantStore;
import com.shop.cereshop.commons.domain.store.CereStoreStaff;
import com.shop.cereshop.commons.domain.store.CereVerifyLog;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 平台管理端门店管理Controller
 * <AUTHOR>
@RestController
@RequestMapping("/admin/store")
@Api(tags = "平台门店管理")
@Slf4j
public class CereStoreManageController {

    @Autowired
    private CereMerchantStoreService cereMerchantStoreService;

    @Autowired
    private CereStoreStaffService cereStoreStaffService;

    @Autowired
    private CereVerifyService cereVerifyService;

    /**
     * 根据商户ID获取门店列表
     */
    @GetMapping("/merchant/{merchantId}")
    @ApiOperation("根据商户ID获取门店列表")
    public Result<List<CereMerchantStore>> getStoresByMerchantId(@PathVariable Long merchantId) {
        try {
            List<CereMerchantStore> stores = cereMerchantStoreService.getStoresByMerchantId(merchantId);
            return Result.success(stores);
        } catch (Exception e) {
            log.error("获取商户门店列表失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据门店ID获取门店详情
     */
    @GetMapping("/{storeId}")
    @ApiOperation("获取门店详情")
    public Result<CereMerchantStore> getStoreById(@PathVariable Long storeId) {
        try {
            CereMerchantStore store = cereMerchantStoreService.getStoreById(storeId);
            return Result.success(store);
        } catch (CoBusinessException e) {
            return Result.failure(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("获取门店详情失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据门店ID获取员工列表
     */
    @GetMapping("/{storeId}/staff")
    @ApiOperation("根据门店ID获取员工列表")
    public Result<List<CereStoreStaff>> getStaffByStoreId(@PathVariable Long storeId) {
        try {
            List<CereStoreStaff> staffList = cereStoreStaffService.getStaffByStoreId(storeId);
            return Result.success(staffList);
        } catch (Exception e) {
            log.error("获取门店员工列表失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据商户ID获取员工列表
     */
    @GetMapping("/merchant/{merchantId}/staff")
    @ApiOperation("根据商户ID获取员工列表")
    public Result<List<CereStoreStaff>> getStaffByMerchantId(@PathVariable Long merchantId) {
        try {
            List<CereStoreStaff> staffList = cereStoreStaffService.getStaffByMerchantId(merchantId);
            return Result.success(staffList);
        } catch (Exception e) {
            log.error("获取商户员工列表失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据商户ID查询核销记录
     */
    @GetMapping("/merchant/{merchantId}/verify-logs")
    @ApiOperation("根据商户ID查询核销记录")
    public Result<List<CereVerifyLog>> getVerifyLogsByMerchantId(@PathVariable Long merchantId,
                                                                @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByMerchantId(merchantId, startTime, endTime);
            return Result.success(logs);
        } catch (Exception e) {
            log.error("查询商户核销记录失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 根据门店ID查询核销记录
     */
    @GetMapping("/{storeId}/verify-logs")
    @ApiOperation("根据门店ID查询核销记录")
    public Result<List<CereVerifyLog>> getVerifyLogsByStoreId(@PathVariable Long storeId,
                                                             @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                             @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            List<CereVerifyLog> logs = cereVerifyService.getVerifyLogsByStoreId(storeId, startTime, endTime);
            return Result.success(logs);
        } catch (Exception e) {
            log.error("查询门店核销记录失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 统计商户门店数量
     */
    @GetMapping("/merchant/{merchantId}/count")
    @ApiOperation("统计商户门店数量")
    public Result<Integer> countStoresByMerchantId(@PathVariable Long merchantId) {
        try {
            int count = cereMerchantStoreService.countStoresByMerchantId(merchantId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计商户门店数量失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 统计商户核销数量
     */
    @GetMapping("/merchant/{merchantId}/verify-count")
    @ApiOperation("统计商户核销数量")
    public Result<Integer> countVerifyByMerchantId(@PathVariable Long merchantId,
                                                  @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                  @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            int count = cereVerifyService.countVerifyByMerchantId(merchantId, startTime, endTime);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计商户核销数量失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 统计门店核销数量
     */
    @GetMapping("/{storeId}/verify-count")
    @ApiOperation("统计门店核销数量")
    public Result<Integer> countVerifyByStoreId(@PathVariable Long storeId,
                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            int count = cereVerifyService.countVerifyByStoreId(storeId, startTime, endTime);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计门店核销数量失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 强制更新门店状态（平台管理员权限）
     */
    @PutMapping("/{storeId}/force-status")
    @ApiOperation("强制更新门店状态")
    public Result<Void> forceUpdateStoreStatus(@PathVariable Long storeId, @RequestParam Integer status) {
        try {
            cereMerchantStoreService.updateStoreStatus(storeId, status);
            return Result.success();
        } catch (CoBusinessException e) {
            return Result.failure(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("强制更新门店状态失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }

    /**
     * 强制更新员工状态（平台管理员权限）
     */
    @PutMapping("/staff/{staffId}/force-status")
    @ApiOperation("强制更新员工状态")
    public Result<Void> forceUpdateStaffStatus(@PathVariable Long staffId, @RequestParam Integer status) {
        try {
            cereStoreStaffService.updateStaffStatus(staffId, status);
            return Result.success();
        } catch (CoBusinessException e) {
            return Result.failure(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("强制更新员工状态失败", e);
            return Result.failure(CoReturnFormat.SYS_ERROR);
        }
    }
}
