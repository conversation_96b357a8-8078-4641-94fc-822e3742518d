/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.admin.controller;

import com.shop.cereshop.admin.annotation.NoRepeatSubmit;
import com.shop.cereshop.admin.annotation.NoRepeatWebLog;
import com.shop.cereshop.admin.param.express.ExpressUpdateParam;
import com.shop.cereshop.admin.service.express.CerePlatformExpressService;
import com.shop.cereshop.commons.domain.user.CerePlatformUser;
import com.shop.cereshop.commons.exception.CoBusinessException;
import com.shop.cereshop.commons.result.Result;
import com.shop.cereshop.commons.utils.GsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 物流查询策略配置
 */
@RestController
@RequestMapping("express")
/**
 * 注解方式生成日志对象，指定topic生成对象类名
 */
@Slf4j(topic = "ExpressController")
@Api(value = "物流查询策略配置模块", tags = "物流查询策略配置模块")
public class ExpressController {

    @Autowired
    private CerePlatformExpressService cerePlatformExpressService;

    /**
     * 切换物流查询策略
     * @param param
     * @return
     */
    @PostMapping(value = "update")
    @NoRepeatSubmit
    @ApiOperation(value = "切换物流查询策略")
    @NoRepeatWebLog
    public Result update(@RequestBody ExpressUpdateParam param, HttpServletRequest request) throws CoBusinessException{
        //获取当前登录账户
        CerePlatformUser user = (CerePlatformUser) request.getAttribute("user");
        cerePlatformExpressService.update(param,user);
        return new Result(user.getUsername(),"切换物流查询策略", GsonUtil.objectToGson(param));
    }
}
