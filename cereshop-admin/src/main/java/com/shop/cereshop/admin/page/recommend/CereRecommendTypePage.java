package com.shop.cereshop.admin.page.recommend;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 种草分页数据
 *
 * <AUTHOR>
@Data
@ApiModel(value = "CereRecommendTypePage", description = "种草分页数据")
public class CereRecommendTypePage {
    /**
     * 种草id
     */
    @ApiModelProperty(value = "种草id")
    private Long recommendTypeId;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String name;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

}
