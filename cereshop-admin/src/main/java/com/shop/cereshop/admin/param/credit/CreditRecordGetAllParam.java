/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.admin.param.credit;

import com.shop.cereshop.commons.domain.common.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取积分记录请求
 * <AUTHOR>
@Data
@ApiModel(value = "CreditRecordGetAllParam", description = "获取积分记录请求")
public class CreditRecordGetAllParam extends PageParam {

    @ApiModelProperty("搜索关键词")
    private String search;

}
