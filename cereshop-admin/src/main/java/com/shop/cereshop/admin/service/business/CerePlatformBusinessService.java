/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact ***********
 */
package com.shop.cereshop.admin.service.business;

import com.shop.cereshop.commons.domain.business.CerePlatformBusiness;
import com.shop.cereshop.commons.exception.CoBusinessException;

public interface CerePlatformBusinessService {
    void insert(CerePlatformBusiness cerePlatformBusiness) throws CoBusinessException;

    void update(CerePlatformBusiness cerePlatformBusiness) throws CoBusinessException;

    CerePlatformBusiness findByPhone(String chargePersonPhone);
}
