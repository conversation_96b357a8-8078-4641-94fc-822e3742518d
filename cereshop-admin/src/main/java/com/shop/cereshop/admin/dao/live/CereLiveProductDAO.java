/*
 * Copyright (C) 2017-2021
 * All rights reserved, Designed By 深圳中科鑫智科技有限公司
 * Copyright authorization contact 18814114118
 */
package com.shop.cereshop.admin.dao.live;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shop.cereshop.admin.param.live.LiveProductGetAllParam;
import com.shop.cereshop.commons.domain.live.CereLiveProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CereLiveProductDAO extends BaseMapper<CereLiveProduct> {

    List<CereLiveProduct> getAll(LiveProductGetAllParam param);

    int audit(CereLiveProduct liveProduct);

    CereLiveProduct getById(@Param("id") Long id);

}
