<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.shop</groupId>
	<artifactId>cereshop-admin</artifactId>
	<version>3.2</version>
	<name>cereshop-admin</name>
	<description>Admin project for Spring Boot</description>

	<packaging>jar</packaging>

	<parent>
		<groupId>com.shop</groupId>
		<artifactId>cereshop</artifactId>
		<version>3.2</version>
	</parent>

	<dependencies>
		<!-- Spring Boot Redis依赖 -->
		<!-- 注意：1.5版本的依赖和2.0的依赖不一样，注意看哦 1.5我记得名字里面应该没有“data”, 2.0必须是“spring-boot-starter-data-redis” 这个才行-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<!-- 添加jedis客户端 -->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>

		<!--aliyunOSS-->
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
		</dependency>

		<!--阿里短信服务-->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shop</groupId>
			<artifactId>cereshop-commons</artifactId>
			<version>3.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
		</dependency>

    </dependencies>

	<profiles>
		<profile>
			<id>admin-prod</id>
			<properties>
				<package.environment>admin-prod</package.environment>
			</properties>
			<build>
				<resources>
					<resource>
						<directory>src/main/resources</directory>
						<!-- 资源根目录排除各环境的配置，使用单独的资源目录来指定 -->
					<includes>
							<include>static/**</include>
							<include>mybatis/**</include>
							<include>service/**</include>
							<include>cert/**</include>
						</includes>
						<!-- 是否替换资源中的属性 -->
						<filtering>true</filtering>
					</resource>
				</resources>
			</build>
		</profile>
		<profile>
			<id>admin-dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<package.environment>admin-dev</package.environment>
			</properties>
			<build>
				<resources>
					<resource>
						<directory>src/main/resources</directory>
						<!-- 资源根目录排除各环境的配置，使用单独的资源目录来指定 -->
						<includes>
							<include>static/**</include>
							<include>mybatis/**</include>
							<include>service/**</include>
							<include>cert/**</include>
							<include>**/*.xml</include>
							<include>**/*.yml</include>
							<include>**/*.properties</include>
						</includes>
						<!-- 是否替换资源中的属性 -->
						<filtering>true</filtering>
					</resource>
				</resources>
			</build>
		</profile>
	</profiles>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<!-- 设置编译版本 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>

			<!-- 传统jar打包配置 - 已被Spring Boot插件替代，保留用于生成分发包 -->
			<!--
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<mainClass>com.shop.cereshop.admin.CereshopAdminApplication</mainClass>
							<useUniqueVersions>false</useUniqueVersions>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
						</manifest>
						<manifestEntries>
							<Class-Path>config/</Class-Path>
						</manifestEntries>
					</archive>
					<classesDirectory>
					</classesDirectory>
				</configuration>
			</plugin>
			-->
			<!-- 拷贝依赖的jar包到lib目录 - 用于分发包，Spring Boot jar不需要 -->
			<!--
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>
								${project.build.directory}/lib
							</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			-->

			<!-- Spring Boot Maven插件 - 生成可执行jar -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.1.7.RELEASE</version>
				<configuration>
					<mainClass>com.shop.cereshop.admin.CereshopAdminApplication</mainClass>
					<layout>JAR</layout>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<!-- 解决资源文件的编码问题 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<!-- 过滤后缀文件 -->
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
						<nonFilteredFileExtension>xls</nonFilteredFileExtension>
						<nonFilteredFileExtension>p12</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
			<!-- 打包source文件为zip文件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<descriptors>
						<descriptor>src/main/assembly/assembly.xml</descriptor>
					</descriptors>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
