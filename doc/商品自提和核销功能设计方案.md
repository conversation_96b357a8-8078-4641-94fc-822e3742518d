# CereShop 多商户商品自提和核销功能设计方案

## 1. 需求背景

基于CereShop多商户电商系统，需要实现商品自提和多次核销功能，支持以下业务场景：

### 1.1 业务场景
- **实物商品自提**：用户下单后到指定门店一次性提取所有商品
- **服务类商品核销**：健身课程、美容套餐、培训课程等需要分多次消费的服务
- **多商户支持**：不同商户的门店独立管理，支持跨商户自提
- **灵活核销**：支持整单核销和单商品多次核销两种模式

### 1.2 与单商户系统的区别

| 对比项目 | 单商户系统(YSHOP) | 多商户系统(CereShop) |
|---------|------------------|---------------------|
| 门店管理 | 统一门店管理 | 按商户分别管理门店 |
| 权限控制 | 单一权限体系 | 商户级权限隔离 |
| 核销范围 | 全平台门店 | 限制在商户门店内 |
| 数据隔离 | 无需隔离 | 严格的商户数据隔离 |
| 结算方式 | 平台统一结算 | 按商户分别结算 |

## 2. 系统架构设计

### 2.1 核心设计原则

1. **商户隔离**：严格按商户隔离数据和权限
2. **向下兼容**：保持现有订单流程不变
3. **灵活扩展**：支持未来更多自提场景
4. **性能优化**：合理的索引和缓存策略
5. **安全可控**：完善的权限验证和操作日志

### 2.2 功能模块划分

```mermaid
graph TB
    A[商品自提核销系统] --> B[门店管理模块]
    A --> C[自提订单模块] 
    A --> D[核销管理模块]
    A --> E[服务商品模块]
    A --> F[权限管理模块]
    
    B --> B1[商户门店管理]
    B --> B2[门店员工管理]
    B --> B3[营业时间设置]
    
    C --> C1[自提订单创建]
    C --> C2[核销码生成]
    C --> C3[订单状态管理]
    
    D --> D1[整单核销]
    D --> D2[部分核销]
    D --> D3[核销记录]
    
    E --> E1[服务商品配置]
    E --> E2[次数管理]
    E --> E3[有效期控制]
    
    F --> F1[员工权限验证]
    F --> F2[商户数据隔离]
    F --> F3[操作日志记录]
```

## 3. 数据库设计

### 3.1 商品表扩展（cere_shop_product）

在现有商品表基础上添加自提相关字段，支持商品同时提供快递和自提两种配送方式。

```sql
-- 在现有商品表添加自提相关字段
ALTER TABLE cere_shop_product ADD COLUMN support_pickup TINYINT DEFAULT 0 COMMENT '是否支持自提：0=否，1=是';
ALTER TABLE cere_shop_product ADD COLUMN verify_total_times INT DEFAULT 1 COMMENT '核销总次数，默认1次';
ALTER TABLE cere_shop_product ADD COLUMN verify_valid_days INT DEFAULT 30 COMMENT '核销有效天数';
ALTER TABLE cere_shop_product ADD COLUMN pickup_advance_time INT DEFAULT 0 COMMENT '自提提前预约时间(小时)';
ALTER TABLE cere_shop_product ADD COLUMN store_limit TEXT COMMENT '可使用门店限制，JSON格式存储门店ID数组';
ALTER TABLE cere_shop_product ADD COLUMN time_limit TEXT COMMENT '使用时间限制，JSON格式';
ALTER TABLE cere_shop_product ADD COLUMN usage_rules TEXT COMMENT '使用规则说明';
```

#### 字段详细说明

**商品配送支持类型**：
- 商品可以同时支持快递配送（`ifLogistics=1`）和门店自提（`support_pickup=1`）
- 用户在购买时可以选择配送方式，不再复用`ifLogistics`字段

**store_limit字段示例**：
```json
{
  "type": "include",           // include=仅限指定门店，exclude=排除指定门店，all=所有门店
  "storeIds": [1, 2, 3, 5],   // 门店ID数组
  "description": "仅限旗舰店和体验店使用"
}
```

**time_limit字段示例**：
```json
{
  "validFrom": "2025-08-26",   // 开始日期
  "validTo": "2025-12-31",     // 结束日期
  "weekdays": [1, 2, 3, 4, 5], // 可用星期（1-7，1=周一）
  "timeRanges": [              // 可用时间段
    {"start": "09:00", "end": "12:00"},
    {"start": "14:00", "end": "18:00"}
  ],
  "holidays": {                // 节假日规则
    "available": false,        // 节假日是否可用
    "exceptions": ["2025-10-01"] // 例外日期
  }
}
```

**usage_rules字段示例**：
```json
{
  "description": "本商品为体验类服务，需提前2小时预约",
  "rules": [
    "每人限购1次",
    "不可退款",
    "需携带身份证",
    "儿童需成人陪同"
  ],
  "notices": [
    "请准时到店，逾期作废",
    "如需改期请提前1天联系客服"
  ]
}
```

### 3.2 门店管理相关表

#### 3.1.1 商户门店表 `cere_merchant_store`

```sql
CREATE TABLE `cere_merchant_store` (
  `store_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '门店ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `store_name` varchar(100) NOT NULL COMMENT '门店名称',
  `store_code` varchar(50) DEFAULT NULL COMMENT '门店编码',
  `contact_name` varchar(50) NOT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `province_id` int(11) DEFAULT NULL COMMENT '省份ID',
  `city_id` int(11) DEFAULT NULL COMMENT '城市ID',
  `area_id` int(11) DEFAULT NULL COMMENT '区域ID',
  `address` varchar(255) NOT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `business_hours` varchar(200) DEFAULT NULL COMMENT '营业时间，JSON格式',
  `pickup_hours` varchar(200) DEFAULT NULL COMMENT '自提时间，JSON格式',
  `store_image` varchar(500) DEFAULT NULL COMMENT '门店图片',
  `store_desc` text COMMENT '门店描述',
  `is_pickup_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持自提：0=否，1=是',
  `is_verify_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持核销：0=否，1=是',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`store_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_pickup_enabled` (`is_pickup_enabled`),
  KEY `idx_verify_enabled` (`is_verify_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户门店表';
```

#### 3.1.2 门店员工表 `cere_store_staff`

```sql
CREATE TABLE `cere_store_staff` (
  `staff_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `store_id` bigint(20) NOT NULL COMMENT '门店ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '关联用户ID（如果员工也是平台用户）',
  `staff_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `staff_phone` varchar(20) NOT NULL COMMENT '员工手机号',
  `staff_code` varchar(50) DEFAULT NULL COMMENT '员工工号',
  `wechat_openid` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `role_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '角色类型：1=普通员工，2=店长，3=区域经理',
  `can_pickup` tinyint(1) NOT NULL DEFAULT 1 COMMENT '自提权限：0=否，1=是',
  `can_verify` tinyint(1) NOT NULL DEFAULT 1 COMMENT '核销权限：0=否，1=是',
  `can_refund` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款权限：0=否，1=是',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`staff_id`),
  UNIQUE KEY `uk_store_phone` (`store_id`, `staff_phone`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_wechat_openid` (`wechat_openid`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店员工表';
```

### 3.2 订单自提相关表

#### 3.2.1 扩展订单表 `cere_order`

```sql
-- 添加自提相关字段
ALTER TABLE `cere_order` 
ADD COLUMN `delivery_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '配送方式：1=快递配送，2=门店自提' AFTER `delivery_time`,
ADD COLUMN `pickup_store_id` bigint(20) DEFAULT NULL COMMENT '自提门店ID' AFTER `delivery_type`,
ADD COLUMN `pickup_store_name` varchar(100) DEFAULT NULL COMMENT '自提门店名称' AFTER `pickup_store_id`,
ADD COLUMN `pickup_contact` varchar(50) DEFAULT NULL COMMENT '自提联系人' AFTER `pickup_store_name`,
ADD COLUMN `pickup_phone` varchar(20) DEFAULT NULL COMMENT '自提联系电话' AFTER `pickup_contact`,
ADD COLUMN `verify_code` varchar(32) DEFAULT NULL COMMENT '核销码' AFTER `pickup_phone`,
ADD COLUMN `verify_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '核销状态：0=待核销，1=部分核销，2=已核销' AFTER `verify_code`,
ADD COLUMN `verify_progress` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '核销进度百分比(0-100)' AFTER `verify_status`,
ADD COLUMN `first_verify_time` datetime DEFAULT NULL COMMENT '首次核销时间' AFTER `verify_progress`,
ADD COLUMN `last_verify_time` datetime DEFAULT NULL COMMENT '最后核销时间' AFTER `first_verify_time`,
ADD COLUMN `has_service_product` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否包含服务类商品：0=否，1=是' AFTER `last_verify_time`;

-- 添加索引
ALTER TABLE `cere_order` 
ADD INDEX `idx_delivery_type` (`delivery_type`),
ADD INDEX `idx_pickup_store_id` (`pickup_store_id`),
ADD INDEX `idx_verify_code` (`verify_code`),
ADD INDEX `idx_verify_status` (`verify_status`);
```

#### 3.2.2 扩展订单商品表 `cere_order_product`

```sql
-- 添加自提和核销相关字段
ALTER TABLE `cere_order_product`
ADD COLUMN `selected_delivery_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '用户选择的配送方式：1=快递，2=自提' AFTER `after_sale_status`,
ADD COLUMN `pickup_store_id` bigint(20) DEFAULT NULL COMMENT '自提门店ID' AFTER `selected_delivery_type`,
ADD COLUMN `pickup_store_name` varchar(100) DEFAULT NULL COMMENT '自提门店名称' AFTER `pickup_store_id`,
ADD COLUMN `is_service_product` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为服务类商品：0=否，1=是' AFTER `pickup_store_name`,
ADD COLUMN `service_type` tinyint(2) DEFAULT NULL COMMENT '服务类型：1=次数卡，2=时长卡，3=课程包' AFTER `is_service_product`,
ADD COLUMN `total_times` int(11) DEFAULT NULL COMMENT '总服务次数（仅服务类商品）' AFTER `service_type`,
ADD COLUMN `used_times` int(11) NOT NULL DEFAULT 0 COMMENT '已使用次数' AFTER `total_times`,
ADD COLUMN `remaining_times` int(11) DEFAULT NULL COMMENT '剩余可用次数' AFTER `used_times`,
ADD COLUMN `validity_days` int(11) DEFAULT NULL COMMENT '有效期天数' AFTER `remaining_times`,
ADD COLUMN `expire_date` date DEFAULT NULL COMMENT '到期日期' AFTER `validity_days`,
ADD COLUMN `service_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '服务状态：0=未开始，1=使用中，2=已完成，3=已过期' AFTER `expire_date`,
ADD COLUMN `first_use_time` datetime DEFAULT NULL COMMENT '首次使用时间' AFTER `service_status`,
ADD COLUMN `last_use_time` datetime DEFAULT NULL COMMENT '最后使用时间' AFTER `first_use_time`,
ADD COLUMN `verify_times` int(11) NOT NULL DEFAULT 0 COMMENT '已核销次数' AFTER `last_use_time`,
ADD COLUMN `verify_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '核销状态：0=未核销，1=部分核销，2=已核销' AFTER `verify_times`;

-- 添加索引
ALTER TABLE `cere_order_product`
ADD INDEX `idx_selected_delivery_type` (`selected_delivery_type`),
ADD INDEX `idx_pickup_store_id` (`pickup_store_id`),
ADD INDEX `idx_is_service_product` (`is_service_product`),
ADD INDEX `idx_service_status` (`service_status`),
ADD INDEX `idx_verify_status` (`verify_status`),
ADD INDEX `idx_expire_date` (`expire_date`);
```

### 3.3 服务商品配置表

#### 3.3.1 服务商品配置表 `cere_service_product`

```sql
CREATE TABLE `cere_service_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `service_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '服务类型：1=次数卡，2=时长卡，3=课程包',
  `total_times` int(11) NOT NULL COMMENT '总次数',
  `single_duration` int(11) DEFAULT NULL COMMENT '单次服务时长(分钟)',
  `validity_days` int(11) DEFAULT NULL COMMENT '有效期天数，NULL表示无限制',
  `booking_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要预约：0=否，1=是',
  `booking_advance_hours` int(11) DEFAULT NULL COMMENT '提前预约时间(小时)',
  `store_limit` text COMMENT '可使用门店限制，JSON格式存储门店ID数组',
  `time_limit` text COMMENT '使用时间限制，JSON格式',
  `usage_rules` text COMMENT '使用规则说明',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_merchant_product` (`merchant_id`, `product_id`),
  KEY `idx_service_type` (`service_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务商品配置表';
```

### 3.4 核销记录表

#### 3.4.1 核销记录表 `cere_verify_log`

```sql
CREATE TABLE `cere_verify_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_sn` varchar(32) NOT NULL COMMENT '订单号',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `order_product_id` bigint(20) NOT NULL COMMENT '订单商品ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `sku_name` varchar(255) DEFAULT NULL COMMENT 'SKU名称',
  `verify_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '核销类型：1=整单核销，2=单商品核销，3=服务次数核销',
  `verify_times` int(11) NOT NULL DEFAULT 1 COMMENT '本次核销次数',
  `remaining_times` int(11) DEFAULT NULL COMMENT '剩余次数（服务类商品）',
  `verify_code` varchar(32) NOT NULL COMMENT '核销码',
  `staff_id` bigint(20) NOT NULL COMMENT '核销员工ID',
  `staff_name` varchar(50) NOT NULL COMMENT '核销员工姓名',
  `store_id` bigint(20) NOT NULL COMMENT '核销门店ID',
  `store_name` varchar(100) NOT NULL COMMENT '核销门店名称',
  `verify_time` datetime NOT NULL COMMENT '核销时间',
  `service_date` date DEFAULT NULL COMMENT '服务日期（可能与核销时间不同）',
  `service_duration` int(11) DEFAULT NULL COMMENT '实际服务时长(分钟)',
  `remark` varchar(500) DEFAULT NULL COMMENT '核销备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_order_product_id` (`order_product_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_verify_code` (`verify_code`),
  KEY `idx_staff_id` (`staff_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_verify_time` (`verify_time`),
  KEY `idx_service_date` (`service_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核销记录表';
```

## 4. 业务流程设计

### 4.1 购物车和配送方式选择流程

#### 4.1.1 多商户购物车处理逻辑

```mermaid
flowchart TD
    A[用户浏览商品] --> B[检查商品配送支持]
    B --> C{配送支持类型}
    C -->|仅快递| D[显示快递配送选项]
    C -->|仅自提| E[显示自提选项]
    C -->|同时支持| F[显示配送方式选择]

    D --> G[加入购物车]
    E --> H[选择自提门店]
    F --> I{用户选择}
    I -->|选择快递| G
    I -->|选择自提| H

    H --> J[验证门店可用性]
    J --> K{验证结果}
    K -->|通过| L[加入购物车]
    K -->|失败| M[提示错误信息]

    G --> N[购物车按商户分组]
    L --> N
    N --> O[用户可修改配送方式]
    O --> P[提交订单]
```

#### 4.1.2 商品配送支持判断

**配送支持类型**：
- **仅支持快递**：`ifLogistics=1, support_pickup=0`
- **仅支持自提**：`ifLogistics=0, support_pickup=1`
- **同时支持**：`ifLogistics=1, support_pickup=1`

**门店可用性验证**：
1. 检查`store_limit`字段的门店限制规则
2. 检查`time_limit`字段的时间限制规则
3. 验证门店当前营业状态
4. 检查门店是否支持该商品自提

#### 4.1.3 购物车数据结构

```javascript
{
  "shops": [
    {
      "shopId": 100,
      "shopName": "测试商户",
      "products": [
        {
          "cartId": 1001,
          "productId": 12345,
          "supportExpress": true,        // 支持快递
          "supportPickup": true,         // 支持自提
          "selectedDeliveryType": 2,     // 用户选择：1=快递，2=自提
          "pickupStoreId": 5,            // 选择的门店ID
          "pickupStoreName": "旗舰店",    // 门店名称
          "storeLimit": {...},           // 门店使用限制
          "timeLimit": {...},            // 时间使用限制
          "usageRules": {...}            // 使用规则
        }
      ]
    }
  ]
}
```

### 4.2 订单提交数据结构

基于现有的订单提交结构，扩展支持自提相关参数：

```javascript
{
  "shops": [
    {
      "shopId": 100,
      "skus": [
        {
          "skuId": 67890,
          "number": 2,
          "ifLogistics": 1,                    // 保持原有逻辑：商品是否支持物流
          "selected": 1,

          // 新增字段：用户选择的配送方式
          "selectedDeliveryType": 2,           // 1=快递，2=自提
          "pickupStoreId": 5,                  // 自提门店ID（当selectedDeliveryType=2时必填）
          "pickupStoreName": "旗舰店",          // 自提门店名称
          "verifyTotalTimes": 1,               // 核销总次数
          "verifyValidDays": 30,               // 有效天数

          // 其他现有字段保持不变
          "shopSeckillId": 0,
          "shopDiscountId": 0
        }
      ],
      "distribution": {
        // 根据商品的selectedDeliveryType动态设置
        "logisticsId": null,                   // 有自提商品时可为null
        "distributionName": "混合配送",         // 描述配送方式
        "distributionPrice": 0,

        // 新增配送类型统计
        "hasExpressProducts": false,           // 是否有快递商品
        "hasPickupProducts": true,             // 是否有自提商品
        "deliveryType": 2                      // 主要配送方式
      }
    }
  ],

  // 收货地址（快递商品需要，自提商品不需要）
  "receiveId": 4849,

  // 新增：自提联系信息（有自提商品时必填）
  "pickupContact": "张三",
  "pickupPhone": "13800138000",

  // 其他字段保持不变
  "paymentMode": 1,
  "subPaymentMode": 1,
  "remark": ""
}
```

### 4.3 自提订单创建流程

```mermaid
flowchart TD
    A[用户选择商品] --> B[加入购物车]
    B --> C[进入结算页面]
    C --> D{选择配送方式}
    D -->|快递配送| E[填写收货地址]
    D -->|门店自提| F[选择自提门店]
    F --> G[填写自提联系信息]
    G --> H[确认订单信息]
    H --> I[提交订单]
    I --> J[订单支付]
    J --> K[生成核销码]
    K --> L{是否包含服务商品?}
    L -->|否| M[等待整单核销]
    L -->|是| N[初始化服务信息]
    N --> O[设置有效期]
    O --> P[等待分次核销]
```

### 4.2 核销业务流程

```mermaid
flowchart TD
    A[员工扫描核销码] --> B[验证员工权限]
    B --> C{权限验证通过?}
    C -->|否| D[提示权限不足]
    C -->|是| E[查询订单信息]
    E --> F{订单状态检查}
    F -->|异常| G[提示订单不可核销]
    F -->|正常| H{订单类型判断}
    H -->|普通商品| I[整单核销流程]
    H -->|服务商品| J[选择核销商品]
    I --> K[更新订单状态为已核销]
    J --> L[选择核销次数]
    L --> M[验证剩余次数]
    M --> N{次数是否足够?}
    N -->|否| O[提示次数不足]
    N -->|是| P[执行部分核销]
    P --> Q[更新商品核销状态]
    Q --> R[记录核销日志]
    K --> R
    R --> S[核销完成]
```

## 5. 核心功能设计

### 5.1 门店管理功能

#### 5.1.1 商户门店管理

**功能列表**：
- **门店信息管理**：门店基本信息、联系方式、地址坐标
- **营业时间设置**：支持不同时间段的营业时间配置
- **自提服务配置**：是否支持自提、核销等服务开关
- **门店状态管理**：启用/禁用门店，排序权重设置

**门店列表页面设计**：
```javascript
// 页面功能
- 门店列表展示（支持分页）
- 门店状态筛选（全部/启用/禁用）
- 门店功能筛选（支持自提/支持核销）
- 门店搜索（按名称、编码、地址）
- 批量操作（批量启用/禁用）
- 新增门店按钮
- 编辑/删除操作

// 列表数据结构
const storeList = {
  total: 10,
  list: [
    {
      storeId: 1,
      storeName: "旗舰店",
      storeCode: "STORE001",
      address: "深圳市南山区科技园",
      contactPhone: "0755-12345678",
      businessHours: "09:00-21:00",
      isPickupEnabled: 1,
      isVerifyEnabled: 1,
      status: 1,
      createTime: "2025-08-26 10:00:00"
    }
  ]
};
```

**门店编辑页面设计**：
```javascript
// 门店表单数据结构
const storeForm = {
  // 基本信息
  storeName: "旗舰店",
  storeCode: "STORE001",
  contactName: "张经理",
  contactPhone: "0755-12345678",

  // 地址信息
  provinceId: 440000,
  cityId: 440300,
  areaId: 440305,
  address: "科技园南区",
  longitude: 113.123456,
  latitude: 22.123456,

  // 营业时间
  businessHours: "09:00-21:00",
  pickupHours: "09:00-20:00",

  // 功能配置
  isPickupEnabled: 1,
  isVerifyEnabled: 1,

  // 其他信息
  storeImage: "https://...",
  storeDesc: "门店描述",
  sortOrder: 100,
  status: 1
};

// 页面组件
- 基本信息编辑表单
- 地址选择器（省市区三级联动）
- 地图定位选择（经纬度）
- 营业时间设置器
- 功能开关配置
- 图片上传组件
- 表单验证和提交
```

#### 5.1.2 门店员工管理

**功能列表**：
- **员工信息管理**：员工基本信息、联系方式、工号
- **权限分配**：自提权限、核销权限、退款权限等
- **角色管理**：普通员工、店长、区域经理等角色
- **微信绑定**：支持员工微信账号绑定，便于移动端操作

**员工列表页面设计**：
```javascript
// 员工列表数据结构
const staffList = {
  total: 20,
  list: [
    {
      staffId: 1,
      staffName: "张三",
      staffCode: "STAFF001",
      phone: "13800138000",
      storeId: 1,
      storeName: "旗舰店",
      role: 1,                    // 1=普通员工，2=店长，3=区域经理
      permissions: [1, 2, 3],     // 权限ID数组
      wechatBound: true,          // 是否绑定微信
      status: 1,                  // 1=启用，0=禁用
      createTime: "2025-08-26 10:00:00"
    }
  ]
};

// 页面功能
- 员工列表展示（支持分页）
- 按门店筛选员工
- 按角色筛选员工
- 员工搜索（按姓名、工号、手机号）
- 批量权限分配
- 新增员工按钮
- 编辑/删除/重置密码操作
```

**员工编辑页面设计**：
```javascript
// 员工表单数据结构
const staffForm = {
  // 基本信息
  staffName: "张三",
  staffCode: "STAFF001",
  phone: "13800138000",
  email: "<EMAIL>",

  // 工作信息
  storeId: 1,                     // 所属门店
  role: 1,                        // 角色
  entryDate: "2025-08-26",        // 入职日期

  // 权限配置
  permissions: [
    {
      id: 1,
      name: "自提权限",
      code: "pickup",
      enabled: true
    },
    {
      id: 2,
      name: "核销权限",
      code: "verify",
      enabled: true
    },
    {
      id: 3,
      name: "退款权限",
      code: "refund",
      enabled: false
    }
  ],

  // 其他信息
  avatar: "https://...",
  remark: "备注信息",
  status: 1
};

// 页面组件
- 基本信息编辑表单
- 门店选择器
- 角色选择器
- 权限配置组件（多选框）
- 头像上传组件
- 微信绑定状态显示
- 表单验证和提交
```

### 5.2 自提订单功能

#### 5.2.1 订单创建
- **门店选择**：根据用户位置推荐就近门店
- **联系信息**：自提联系人和电话信息
- **核销码生成**：订单支付后自动生成唯一核销码
- **服务商品处理**：自动识别服务类商品并初始化相关信息

#### 5.2.2 订单状态管理
- **配送状态**：区分快递配送和门店自提
- **核销状态**：待核销、部分核销、已核销
- **核销进度**：实时计算和显示核销完成百分比
- **服务状态**：服务类商品的使用状态跟踪

### 5.3 核销管理功能

#### 5.3.1 核销订单列表页面

**功能列表**：
- **订单查询**：按核销码、订单号、用户信息查询
- **状态筛选**：待核销、部分核销、已核销
- **时间筛选**：按下单时间、核销时间筛选
- **门店筛选**：查看指定门店的核销订单
- **批量操作**：批量核销、批量导出

**页面设计**：
```javascript
// 核销订单列表数据结构
const verifyOrderList = {
  total: 50,
  list: [
    {
      orderId: 123456,
      orderSn: "202508260001",
      verifyCode: "CS12345678",
      buyerName: "李四",
      buyerPhone: "13900139000",
      totalAmount: 198.00,
      verifyStatus: 0,            // 0=待核销，1=部分核销，2=已核销
      pickupStoreId: 1,
      pickupStoreName: "旗舰店",
      createTime: "2025-08-26 10:30:00",
      products: [
        {
          productName: "测试商品",
          skuName: "红色/L",
          number: 2,
          verifyTimes: 0,         // 已核销次数
          totalTimes: 1           // 总次数
        }
      ]
    }
  ]
};

// 页面功能
- 订单列表展示（支持分页）
- 多条件筛选和搜索
- 订单详情查看
- 快速核销操作
- 核销记录查看
- 数据导出功能
```

#### 5.3.2 扫码核销功能

**扫码核销页面设计**：
```javascript
// 扫码核销流程
const verifyProcess = {
  // 步骤1：扫码或输入核销码
  scanStep: {
    methods: ['camera_scan', 'manual_input'],
    validation: 'real_time'
  },

  // 步骤2：验证核销码
  validateStep: {
    checkItems: [
      'code_validity',      // 核销码有效性
      'order_status',       // 订单状态
      'staff_permission',   // 员工权限
      'store_match'         // 门店匹配
    ]
  },

  // 步骤3：显示订单信息
  orderInfoStep: {
    displayInfo: [
      'buyer_info',         // 买家信息
      'product_list',       // 商品列表
      'verify_status',      // 核销状态
      'special_notes'       // 特殊说明
    ]
  },

  // 步骤4：执行核销
  executeStep: {
    options: [
      'whole_order',        // 整单核销
      'partial_verify'      // 部分核销
    ],
    confirmation: true,     // 需要确认
    logging: true          // 记录日志
  }
};

// 核销确认对话框
const verifyConfirmDialog = {
  title: "确认核销",
  content: {
    orderInfo: "订单号：202508260001",
    buyerInfo: "用户：李四 (13900139000)",
    productList: [
      {
        name: "测试商品",
        spec: "红色/L",
        quantity: 2,
        verifyType: "整单核销"
      }
    ],
    warningText: "核销后无法撤销，请确认用户已到店取货"
  },
  actions: ['confirm', 'cancel']
};
```

#### 5.3.3 整单核销功能

**功能特点**：
- **一键核销**：支持整个订单一次性核销完成
- **权限验证**：验证员工是否有权限核销该订单
- **状态更新**：核销后更新订单和商品状态
- **日志记录**：详细记录核销操作信息

**核销执行流程**：
```javascript
// 整单核销处理逻辑
const wholeOrderVerify = {
  // 前置检查
  preCheck: {
    verifyCodeValid: true,        // 核销码有效
    orderCanVerify: true,         // 订单可核销
    staffHasPermission: true,     // 员工有权限
    storeMatched: true           // 门店匹配
  },

  // 核销操作
  verifyAction: {
    updateOrderStatus: 'completed',     // 更新订单状态
    updateProductStatus: 'verified',    // 更新商品状态
    recordVerifyLog: true,              // 记录核销日志
    triggerAfterEvents: true            // 触发后续事件
  },

  // 结果反馈
  result: {
    success: true,
    message: "核销成功",
    verifyTime: "2025-08-26 14:30:00",
    staffName: "张三",
    storeName: "旗舰店"
  }
};
```

#### 5.3.4 部分核销功能（服务商品）

**功能特点**：
- **商品选择**：支持选择特定商品进行核销
- **次数控制**：灵活设置每次核销的服务次数
- **剩余次数**：实时显示和更新剩余可用次数
- **有效期检查**：验证服务是否在有效期内

**部分核销页面设计**：
```javascript
// 部分核销表单
const partialVerifyForm = {
  orderId: 123456,
  orderSn: "202508260001",
  products: [
    {
      orderProductId: 1001,
      productName: "瑜伽课程包",
      totalTimes: 10,           // 总次数
      usedTimes: 3,             // 已使用次数
      remainingTimes: 7,        // 剩余次数
      thisVerifyTimes: 1,       // 本次核销次数
      expireDate: "2025-12-31", // 到期日期
      canVerify: true           // 是否可核销
    }
  ],
  remark: "第4次瑜伽课程",
  staffId: 1
};

// 页面组件
- 商品列表展示
- 核销次数输入器
- 剩余次数显示
- 有效期检查提示
- 核销备注输入
- 确认核销按钮
```

### 5.4 商品管理功能（自提相关）

#### 5.4.1 商品自提配置

**商品编辑页面扩展**：
```javascript
// 商品自提配置表单
const productPickupConfig = {
  // 基本配置
  supportPickup: true,              // 是否支持自提
  verifyTotalTimes: 1,              // 核销总次数
  verifyValidDays: 30,              // 有效天数
  pickupAdvanceTime: 2,             // 提前预约时间(小时)

  // 门店限制配置
  storeLimit: {
    type: "include",                // include=仅限指定门店，exclude=排除指定门店，all=所有门店
    storeIds: [1, 2, 3],           // 门店ID数组
    description: "仅限旗舰店和体验店使用"
  },

  // 时间限制配置
  timeLimit: {
    validFrom: "2025-08-26",        // 开始日期
    validTo: "2025-12-31",          // 结束日期
    weekdays: [1, 2, 3, 4, 5],      // 可用星期（1-7，1=周一）
    timeRanges: [                   // 可用时间段
      {"start": "09:00", "end": "12:00"},
      {"start": "14:00", "end": "18:00"}
    ],
    holidays: {                     // 节假日规则
      available: false,             // 节假日是否可用
      exceptions: ["2025-10-01"]    // 例外日期
    }
  },

  // 使用规则配置
  usageRules: {
    description: "本商品为体验类服务，需提前2小时预约",
    rules: [
      "每人限购1次",
      "不可退款",
      "需携带身份证",
      "儿童需成人陪同"
    ],
    notices: [
      "请准时到店，逾期作废",
      "如需改期请提前1天联系客服"
    ]
  }
};

// 页面组件设计
- 自提开关配置
- 核销次数和有效期设置
- 门店限制选择器（支持多选、排除模式）
- 时间限制配置器（日期范围、星期、时间段）
- 使用规则编辑器（富文本编辑）
- 预览效果展示
```

**门店限制选择器组件**：
```javascript
// 门店限制选择器
const StoreLimitSelector = {
  props: {
    value: Object,              // 当前配置值
    merchantId: Number,         // 商户ID
    onChange: Function          // 变更回调
  },

  data: {
    limitType: 'all',           // all=所有门店，include=仅限指定，exclude=排除指定
    selectedStores: [],         // 选中的门店列表
    storeList: []              // 可选门店列表
  },

  methods: {
    loadStoreList: 'GET /business/store/list',
    handleTypeChange: 'updateLimitType',
    handleStoreSelect: 'updateSelectedStores',
    generateDescription: 'autoGenerateDescription'
  },

  template: `
    <div class="store-limit-selector">
      <RadioGroup v-model="limitType">
        <Radio value="all">所有门店可用</Radio>
        <Radio value="include">仅限指定门店</Radio>
        <Radio value="exclude">排除指定门店</Radio>
      </RadioGroup>

      <StoreCheckboxList
        v-if="limitType !== 'all'"
        v-model="selectedStores"
        :options="storeList"
        :multiple="true"
      />

      <Input
        v-model="description"
        placeholder="限制说明（自动生成）"
        readonly
      />
    </div>
  `
};
```

#### 5.4.2 时间限制配置器

**时间限制配置组件**：
```javascript
// 时间限制配置器
const TimeLimitSelector = {
  props: {
    value: Object,
    onChange: Function
  },

  data: {
    dateRange: [],              // 日期范围
    weekdays: [],               // 可用星期
    timeRanges: [],             // 时间段
    holidaySettings: {}         // 节假日设置
  },

  methods: {
    handleDateRangeChange: 'updateDateRange',
    handleWeekdayChange: 'updateWeekdays',
    handleTimeRangeChange: 'updateTimeRanges',
    addTimeRange: 'addNewTimeRange',
    removeTimeRange: 'removeTimeRange'
  },

  template: `
    <div class="time-limit-selector">
      <!-- 日期范围选择 -->
      <FormItem label="有效日期范围">
        <DatePicker
          v-model="dateRange"
          type="daterange"
          placeholder="选择开始和结束日期"
        />
      </FormItem>

      <!-- 星期选择 -->
      <FormItem label="可用星期">
        <CheckboxGroup v-model="weekdays">
          <Checkbox value="1">周一</Checkbox>
          <Checkbox value="2">周二</Checkbox>
          <Checkbox value="3">周三</Checkbox>
          <Checkbox value="4">周四</Checkbox>
          <Checkbox value="5">周五</Checkbox>
          <Checkbox value="6">周六</Checkbox>
          <Checkbox value="7">周日</Checkbox>
        </CheckboxGroup>
      </FormItem>

      <!-- 时间段设置 -->
      <FormItem label="可用时间段">
        <div v-for="(range, index) in timeRanges" :key="index">
          <TimePicker v-model="range.start" placeholder="开始时间" />
          <TimePicker v-model="range.end" placeholder="结束时间" />
          <Button @click="removeTimeRange(index)">删除</Button>
        </div>
        <Button @click="addTimeRange">添加时间段</Button>
      </FormItem>

      <!-- 节假日设置 -->
      <FormItem label="节假日规则">
        <Switch v-model="holidaySettings.available" />
        <span>节假日是否可用</span>
      </FormItem>
    </div>
  `
};
```

#### 5.4.3 商品列表页面扩展

**商品列表筛选扩展**：
```javascript
// 商品列表筛选条件扩展
const productListFilters = {
  // 原有筛选条件
  category: null,
  status: null,
  priceRange: [],

  // 新增自提相关筛选
  deliveryType: null,           // 配送方式：1=仅快递，2=仅自提，3=都支持
  supportPickup: null,          // 是否支持自提
  hasStoreLimit: null,          // 是否有门店限制
  hasTimeLimit: null,           // 是否有时间限制
  verifyStatus: null            // 核销状态
};

// 商品列表数据扩展
const productListItem = {
  // 原有字段
  productId: 12345,
  productName: "测试商品",
  price: 99.00,
  stock: 100,
  status: 1,

  // 新增自提相关字段
  supportPickup: true,          // 是否支持自提
  deliverySupportType: 3,       // 配送支持类型
  verifyTotalTimes: 1,          // 核销总次数
  verifyValidDays: 30,          // 有效天数
  hasStoreLimit: true,          // 是否有门店限制
  hasTimeLimit: true,           // 是否有时间限制

  // 显示标签
  tags: [
    { text: "支持自提", color: "blue" },
    { text: "限时使用", color: "orange" },
    { text: "指定门店", color: "green" }
  ]
};

// 页面功能扩展
- 配送方式筛选器
- 自提相关标签显示
- 批量设置自提配置
- 自提配置快速复制
- 配置预览功能
```

#### 5.4.4 商品配置模板

**配置模板管理**：
```javascript
// 自提配置模板
const pickupConfigTemplates = [
  {
    id: 1,
    name: "标准实物商品",
    description: "适用于普通实物商品的自提配置",
    config: {
      supportPickup: true,
      verifyTotalTimes: 1,
      verifyValidDays: 30,
      storeLimit: { type: "all" },
      timeLimit: null,
      usageRules: {
        description: "标准商品自提",
        rules: ["请携带有效证件", "核对订单信息"]
      }
    }
  },
  {
    id: 2,
    name: "体验类服务",
    description: "适用于需要预约的体验类服务",
    config: {
      supportPickup: true,
      verifyTotalTimes: 1,
      verifyValidDays: 90,
      pickupAdvanceTime: 24,
      storeLimit: { type: "include", storeIds: [1, 2] },
      timeLimit: {
        weekdays: [1, 2, 3, 4, 5],
        timeRanges: [{"start": "09:00", "end": "18:00"}]
      },
      usageRules: {
        description: "体验类服务，需提前预约",
        rules: ["需提前24小时预约", "不可退款", "不可改期"]
      }
    }
  }
];

// 模板应用功能
- 模板选择器
- 一键应用模板配置
- 自定义模板保存
- 模板配置对比
- 批量应用模板
```

## 6. 权限和安全设计

### 6.1 权限控制体系

#### 6.1.1 商户级权限隔离
- **数据隔离**：严格按商户ID隔离所有相关数据
- **门店权限**：员工只能操作所属商户的门店订单
- **跨商户限制**：禁止跨商户的数据访问和操作
- **管理权限**：商户管理员可管理本商户的所有门店和员工

#### 6.1.2 员工操作权限
- **功能权限**：自提权限、核销权限、退款权限等
- **门店权限**：员工只能操作所属门店的订单
- **时间权限**：可设置员工的工作时间限制
- **IP限制**：可选的IP地址访问限制

### 6.2 安全机制

#### 6.2.1 核销码安全
- **唯一性保证**：确保核销码在全平台唯一
- **复杂度要求**：使用足够复杂的编码规则
- **有效期控制**：可设置核销码的有效期限制
- **防重复使用**：严格防止核销码被重复使用

#### 6.2.2 操作安全
- **操作日志**：详细记录所有核销操作
- **异常监控**：监控异常的核销行为
- **撤销机制**：支持错误操作的撤销功能
- **审计追踪**：完整的操作审计链路

## 7. 接口设计

### 7.1 门店相关接口

```java
// 获取商户门店列表
GET /api/merchant/stores
// 获取门店详情
GET /api/merchant/stores/{storeId}
// 获取门店员工列表
GET /api/merchant/stores/{storeId}/staff
```

### 7.2 自提订单接口

```java
// 创建自提订单
POST /api/orders/pickup
// 获取订单核销信息
GET /api/orders/{orderId}/verify-info
// 查询核销记录
GET /api/orders/{orderId}/verify-logs
```

### 7.3 核销操作接口

```java
// 整单核销
POST /api/verify/order
// 部分核销（服务商品）
POST /api/verify/service
// 批量核销
POST /api/verify/batch
// 核销撤销
POST /api/verify/cancel
```

## 8. 实施计划

### 8.1 开发阶段规划

**第一阶段（2周）**：基础架构和数据库
- 数据库表结构设计和创建
- 基础实体类和DAO层开发
- 核心服务接口定义

**第二阶段（3周）**：核心功能开发
- 门店管理功能实现
- 自提订单流程开发
- 整单核销功能实现

**第三阶段（2周）**：服务商品和部分核销
- 服务商品配置功能
- 部分核销业务逻辑
- 核销记录和统计功能

**第四阶段（1周）**：测试和优化
- 功能测试和性能测试
- 安全测试和压力测试
- 代码优化和文档完善

### 8.2 上线策略

1. **灰度发布**：选择部分商户先行试用
2. **功能开关**：通过配置开关控制功能启用
3. **数据监控**：实时监控核销数据和系统性能
4. **逐步推广**：根据试用效果逐步扩大使用范围
5. **全量上线**：确认稳定后向所有商户开放

## 9. 风险评估和应对

### 9.1 技术风险

**数据一致性风险**
- 风险：多表事务操作可能导致数据不一致
- 应对：使用分布式事务，添加数据校验机制

**性能影响风险**
- 风险：新增功能可能影响现有系统性能
- 应对：合理设计索引，使用缓存优化，分阶段上线

**兼容性风险**
- 风险：新功能与现有功能可能存在冲突
- 应对：充分的兼容性测试，保持向下兼容

### 9.2 业务风险

**操作错误风险**
- 风险：员工误操作导致核销错误
- 应对：添加操作确认机制，提供撤销功能

**权限管理风险**
- 风险：权限配置错误影响业务流程
- 应对：完善的权限验证，详细的操作日志

**用户体验风险**
- 风险：复杂的操作流程影响用户体验
- 应对：简化操作界面，提供操作指引

## 10. 总结

本设计方案基于CereShop多商户电商系统的特点，在参考YSHOP B2C单商户系统成功经验的基础上，设计了适合多商户场景的商品自提和核销功能。

**主要特色**：
1. **多商户支持**：完善的商户数据隔离和权限控制
2. **灵活核销**：支持整单核销和服务商品分次核销
3. **安全可控**：完善的权限验证和操作审计机制
4. **扩展性强**：预留了丰富的扩展接口和配置选项

**技术优势**：
1. **架构合理**：清晰的模块划分和数据结构设计
2. **性能优化**：合理的索引设计和缓存策略
3. **安全保障**：多层次的安全防护机制
4. **易于维护**：完善的日志记录和监控体系

该方案为CereShop多商户电商平台提供了完整的自提核销解决方案，能够满足不同类型商户的业务需求，提升用户购物体验和商户运营效率。

## 11. 详细实施步骤

### 11.1 数据库改动清单

#### 11.1.1 新增表（按优先级排序）

**高优先级（第一阶段必须）**：
1. `cere_merchant_store` - 商户门店表
2. `cere_store_staff` - 门店员工表
3. `cere_verify_log` - 核销记录表

**中优先级（第二阶段）**：
4. `cere_service_product` - 服务商品配置表

**低优先级（第三阶段，可选）**：
5. `cere_service_booking` - 服务预约表（如需预约功能）

#### 11.1.2 表结构修改清单

**cere_order表新增字段**：
```sql
-- 第一阶段：基础自提功能
ALTER TABLE `cere_order` ADD COLUMN `delivery_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '配送方式：1=快递配送，2=门店自提';
ALTER TABLE `cere_order` ADD COLUMN `pickup_store_id` bigint(20) DEFAULT NULL COMMENT '自提门店ID';
ALTER TABLE `cere_order` ADD COLUMN `pickup_store_name` varchar(100) DEFAULT NULL COMMENT '自提门店名称';
ALTER TABLE `cere_order` ADD COLUMN `pickup_contact` varchar(50) DEFAULT NULL COMMENT '自提联系人';
ALTER TABLE `cere_order` ADD COLUMN `pickup_phone` varchar(20) DEFAULT NULL COMMENT '自提联系电话';
ALTER TABLE `cere_order` ADD COLUMN `verify_code` varchar(32) DEFAULT NULL COMMENT '核销码';
ALTER TABLE `cere_order` ADD COLUMN `verify_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '核销状态：0=待核销，1=部分核销，2=已核销';

-- 第二阶段：服务商品功能
ALTER TABLE `cere_order` ADD COLUMN `verify_progress` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '核销进度百分比(0-100)';
ALTER TABLE `cere_order` ADD COLUMN `first_verify_time` datetime DEFAULT NULL COMMENT '首次核销时间';
ALTER TABLE `cere_order` ADD COLUMN `last_verify_time` datetime DEFAULT NULL COMMENT '最后核销时间';
ALTER TABLE `cere_order` ADD COLUMN `has_service_product` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否包含服务类商品：0=否，1=是';

-- 添加索引
ALTER TABLE `cere_order` ADD INDEX `idx_delivery_type` (`delivery_type`);
ALTER TABLE `cere_order` ADD INDEX `idx_pickup_store_id` (`pickup_store_id`);
ALTER TABLE `cere_order` ADD INDEX `idx_verify_code` (`verify_code`);
ALTER TABLE `cere_order` ADD INDEX `idx_verify_status` (`verify_status`);
```

**cere_order_product表新增字段**：
```sql
-- 第二阶段：服务商品核销
ALTER TABLE `cere_order_product` ADD COLUMN `is_service_product` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为服务类商品：0=否，1=是';
ALTER TABLE `cere_order_product` ADD COLUMN `service_type` tinyint(2) DEFAULT NULL COMMENT '服务类型：1=次数卡，2=时长卡，3=课程包';
ALTER TABLE `cere_order_product` ADD COLUMN `total_times` int(11) DEFAULT NULL COMMENT '总服务次数（仅服务类商品）';
ALTER TABLE `cere_order_product` ADD COLUMN `used_times` int(11) NOT NULL DEFAULT 0 COMMENT '已使用次数';
ALTER TABLE `cere_order_product` ADD COLUMN `remaining_times` int(11) DEFAULT NULL COMMENT '剩余可用次数';
ALTER TABLE `cere_order_product` ADD COLUMN `validity_days` int(11) DEFAULT NULL COMMENT '有效期天数';
ALTER TABLE `cere_order_product` ADD COLUMN `expire_date` date DEFAULT NULL COMMENT '到期日期';
ALTER TABLE `cere_order_product` ADD COLUMN `service_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '服务状态：0=未开始，1=使用中，2=已完成，3=已过期';
ALTER TABLE `cere_order_product` ADD COLUMN `first_use_time` datetime DEFAULT NULL COMMENT '首次使用时间';
ALTER TABLE `cere_order_product` ADD COLUMN `last_use_time` datetime DEFAULT NULL COMMENT '最后使用时间';
ALTER TABLE `cere_order_product` ADD COLUMN `verify_times` int(11) NOT NULL DEFAULT 0 COMMENT '已核销次数';
ALTER TABLE `cere_order_product` ADD COLUMN `verify_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '核销状态：0=未核销，1=部分核销，2=已核销';

-- 添加索引
ALTER TABLE `cere_order_product` ADD INDEX `idx_is_service_product` (`is_service_product`);
ALTER TABLE `cere_order_product` ADD INDEX `idx_service_status` (`service_status`);
ALTER TABLE `cere_order_product` ADD INDEX `idx_verify_status` (`verify_status`);
ALTER TABLE `cere_order_product` ADD INDEX `idx_expire_date` (`expire_date`);
```

### 11.2 代码改动清单

#### 11.2.1 新增实体类

**第一阶段**：
1. `CereMerchantStore.java` - 商户门店实体
2. `CereStoreStaff.java` - 门店员工实体
3. `CereVerifyLog.java` - 核销记录实体

**第二阶段**：
4. `CereServiceProduct.java` - 服务商品配置实体

#### 11.2.2 新增DAO接口

**第一阶段**：
1. `CereMerchantStoreDAO.java` - 门店数据访问接口
2. `CereStoreStaffDAO.java` - 员工数据访问接口
3. `CereVerifyLogDAO.java` - 核销记录数据访问接口

**第二阶段**：
4. `CereServiceProductDAO.java` - 服务商品配置数据访问接口

#### 11.2.3 新增Service服务类

**第一阶段**：
1. `CereMerchantStoreService.java` - 门店管理服务
2. `CereStoreStaffService.java` - 员工管理服务
3. `CerePickupOrderService.java` - 自提订单服务
4. `CereVerifyService.java` - 核销服务

**第二阶段**：
5. `CereServiceProductService.java` - 服务商品管理服务
6. `CerePartialVerifyService.java` - 部分核销服务

#### 11.2.4 新增Controller控制器

**第一阶段**：
1. `CereMerchantStoreController.java` - 门店管理控制器
2. `CereStoreStaffController.java` - 员工管理控制器
3. `CerePickupOrderController.java` - 自提订单控制器
4. `CereVerifyController.java` - 核销控制器

**第二阶段**：
5. `CereServiceProductController.java` - 服务商品管理控制器

#### 11.2.5 修改现有类

**CereOrderService.java**：
- 添加自提订单创建逻辑
- 添加核销码生成逻辑
- 添加订单状态更新逻辑

**CereOrderController.java**：
- 添加自提相关接口
- 修改订单创建接口支持自提

**CereOrderProductService.java**：
- 添加服务商品初始化逻辑
- 添加核销状态更新逻辑

### 11.3 业务逻辑改动

#### 11.3.1 订单创建流程改动

**原有流程**：
```
选择商品 → 加入购物车 → 填写收货地址 → 选择配送方式 → 提交订单 → 支付 → 等待发货
```

**新增流程**：
```
选择商品 → 加入购物车 → 选择配送方式 →
├─ 快递配送：填写收货地址 → 提交订单 → 支付 → 等待发货
└─ 门店自提：选择门店 → 填写联系信息 → 提交订单 → 支付 → 生成核销码 → 等待核销
```

**关键改动点**：
1. 在购物车页面添加配送方式选择
2. 自提时显示门店选择界面
3. 订单支付成功后生成核销码
4. 服务商品自动初始化次数和有效期

#### 11.3.2 核销业务逻辑

**整单核销流程**：
```java
public class CereVerifyService {

    @Transactional
    public void verifyWholeOrder(String verifyCode, Long staffId) {
        // 1. 验证核销码和权限
        CereOrder order = validateVerifyCode(verifyCode, staffId);

        // 2. 检查订单状态
        if (order.getVerifyStatus() == 2) {
            throw new BusinessException("订单已核销");
        }

        // 3. 更新订单状态
        order.setVerifyStatus(2);
        order.setVerifyProgress(new BigDecimal("100.00"));
        order.setFirstVerifyTime(order.getFirstVerifyTime() == null ? new Date() : order.getFirstVerifyTime());
        order.setLastVerifyTime(new Date());
        cereOrderDAO.updateByPrimaryKeySelective(order);

        // 4. 更新所有商品状态
        List<CereOrderProduct> products = cereOrderProductDAO.findByOrderId(order.getOrderId());
        for (CereOrderProduct product : products) {
            product.setVerifyStatus(2);
            product.setVerifyTimes(product.getProductNum());
            if (product.getIsServiceProduct() == 1) {
                product.setUsedTimes(product.getTotalTimes());
                product.setRemainingTimes(0);
                product.setServiceStatus(2); // 已完成
            }
            cereOrderProductDAO.updateByPrimaryKeySelective(product);
        }

        // 5. 记录核销日志
        recordVerifyLog(order, products, staffId, 1); // 整单核销

        // 6. 触发后续业务（积分、佣金等）
        triggerAfterVerifyEvents(order);
    }
}
```

**部分核销流程**：
```java
@Transactional
public void verifyServiceProduct(String verifyCode, Long orderProductId,
                                Integer verifyTimes, Long staffId) {
    // 1. 验证核销码和权限
    CereOrder order = validateVerifyCode(verifyCode, staffId);
    CereOrderProduct product = cereOrderProductDAO.selectByPrimaryKey(orderProductId);

    // 2. 验证服务商品状态
    if (product.getIsServiceProduct() != 1) {
        throw new BusinessException("该商品不支持部分核销");
    }

    if (product.getRemainingTimes() < verifyTimes) {
        throw new BusinessException("剩余次数不足");
    }

    if (product.getExpireDate() != null && product.getExpireDate().before(new Date())) {
        throw new BusinessException("服务已过期");
    }

    // 3. 更新商品状态
    product.setUsedTimes(product.getUsedTimes() + verifyTimes);
    product.setRemainingTimes(product.getRemainingTimes() - verifyTimes);
    product.setVerifyTimes(product.getVerifyTimes() + verifyTimes);

    if (product.getFirstUseTime() == null) {
        product.setFirstUseTime(new Date());
        product.setServiceStatus(1); // 使用中
    }
    product.setLastUseTime(new Date());

    if (product.getRemainingTimes() == 0) {
        product.setServiceStatus(2); // 已完成
        product.setVerifyStatus(2); // 已核销
    } else {
        product.setVerifyStatus(1); // 部分核销
    }

    cereOrderProductDAO.updateByPrimaryKeySelective(product);

    // 4. 更新订单状态
    updateOrderVerifyProgress(order.getOrderId());

    // 5. 记录核销日志
    recordServiceVerifyLog(order, product, verifyTimes, staffId);
}
```

### 11.4 前端页面改动

#### 11.4.1 用户端页面改动

**购物车页面**：
- 添加配送方式选择（快递/自提）
- 自提时显示门店选择组件
- 添加自提联系信息填写

**订单详情页面**：
- 显示自提门店信息
- 显示核销码（二维码）
- 服务商品显示使用进度
- 显示核销记录

**订单列表页面**：
- 区分显示快递订单和自提订单
- 自提订单显示核销状态
- 添加核销码快速查看

#### 11.4.2 商户端页面改动

**门店管理页面**：
- 门店信息管理
- 门店员工管理
- 营业时间设置
- 服务配置管理

**核销管理页面**：
- 扫码核销界面
- 订单核销详情
- 核销记录查询
- 核销统计报表

#### 11.4.3 管理端页面改动

**订单管理页面**：
- 添加配送方式筛选
- 显示核销状态
- 核销记录查看

**商户管理页面**：
- 门店管理功能
- 员工权限管理
- 核销数据统计

### 11.5 配置和常量定义

#### 11.5.1 枚举类定义

```java
/**
 * 配送方式枚举
 */
public enum DeliveryTypeEnum {
    EXPRESS(1, "快递配送"),
    PICKUP(2, "门店自提");

    private final Integer code;
    private final String desc;
}

/**
 * 核销状态枚举
 */
public enum VerifyStatusEnum {
    PENDING(0, "待核销"),
    PARTIAL(1, "部分核销"),
    COMPLETED(2, "已核销");

    private final Integer code;
    private final String desc;
}

/**
 * 服务状态枚举
 */
public enum ServiceStatusEnum {
    NOT_STARTED(0, "未开始"),
    IN_USE(1, "使用中"),
    COMPLETED(2, "已完成"),
    EXPIRED(3, "已过期");

    private final Integer code;
    private final String desc;
}

/**
 * 服务类型枚举
 */
public enum ServiceTypeEnum {
    TIMES_CARD(1, "次数卡"),
    DURATION_CARD(2, "时长卡"),
    COURSE_PACKAGE(3, "课程包");

    private final Integer code;
    private final String desc;
}
```

#### 11.5.2 配置参数

```properties
# 核销码配置
cereshop.verify.code.length=12
cereshop.verify.code.prefix=CS
cereshop.verify.code.expire.days=30

# 服务商品配置
cereshop.service.default.validity.days=365
cereshop.service.max.times=999
cereshop.service.booking.advance.hours=24

# 门店配置
cereshop.store.max.distance.km=50
cereshop.store.default.business.hours=09:00-21:00
```

### 11.6 数据迁移脚本

#### 11.6.1 历史数据处理

```sql
-- 初始化现有订单的配送方式（默认为快递配送）
UPDATE cere_order SET delivery_type = 1 WHERE delivery_type IS NULL;

-- 为现有订单商品初始化核销相关字段
UPDATE cere_order_product SET
    verify_times = product_num,
    verify_status = 2
WHERE order_id IN (
    SELECT order_id FROM cere_order WHERE order_status = 4 -- 已完成订单
);

-- 创建默认门店（如果商户没有门店）
INSERT INTO cere_merchant_store (merchant_id, store_name, contact_name, contact_phone, address, is_pickup_enabled, is_verify_enabled)
SELECT
    merchant_id,
    CONCAT(merchant_name, '默认门店'),
    contact_name,
    contact_phone,
    address,
    1,
    1
FROM cere_merchant
WHERE merchant_id NOT IN (SELECT DISTINCT merchant_id FROM cere_merchant_store);
```

### 11.7 测试用例设计

#### 11.7.1 功能测试用例

**自提订单创建测试**：
1. 正常创建自提订单
2. 未选择门店时的错误处理
3. 门店不存在时的错误处理
4. 联系信息验证测试

**核销功能测试**：
1. 正常整单核销
2. 重复核销防护测试
3. 权限验证测试
4. 核销码无效时的处理

**服务商品测试**：
1. 服务商品部分核销
2. 次数不足时的处理
3. 服务过期时的处理
4. 有效期计算测试

#### 11.7.2 性能测试用例

**并发核销测试**：
- 同一订单并发核销
- 不同订单并发核销
- 大量核销请求压力测试

**数据库性能测试**：
- 大量核销记录查询性能
- 复杂条件筛选性能
- 统计报表生成性能

### 11.8 监控和告警

#### 11.8.1 业务监控指标

**核销相关指标**：
- 每日核销订单数量
- 核销成功率
- 平均核销时间
- 异常核销次数

**服务商品指标**：
- 服务使用率
- 服务过期率
- 平均使用周期
- 客户满意度

#### 11.8.2 技术监控指标

**系统性能指标**：
- 核销接口响应时间
- 数据库查询性能
- 缓存命中率
- 错误率统计

**告警规则**：
- 核销失败率超过5%时告警
- 接口响应时间超过3秒时告警
- 数据库连接数超过阈值时告警
- 系统异常时立即告警

## 12. 总结和后续规划

### 12.1 项目价值

**业务价值**：
1. **提升用户体验**：提供更灵活的商品获取方式
2. **降低物流成本**：减少快递配送成本
3. **增加用户粘性**：服务类商品增加用户到店频次
4. **扩展业务场景**：支持更多类型的商品和服务

**技术价值**：
1. **系统完善**：补充了电商系统的重要功能模块
2. **架构优化**：提升了系统的扩展性和可维护性
3. **数据价值**：积累了用户行为和商户运营数据
4. **技术积累**：为后续功能开发奠定了基础

### 12.2 后续规划

**短期规划（3个月内）**：
1. 完成基础自提和核销功能开发
2. 在部分商户试点运行
3. 收集用户反馈并优化体验
4. 完善监控和告警机制

**中期规划（6个月内）**：
1. 推广到所有商户使用
2. 开发服务商品预约功能
3. 增加核销数据分析和报表
4. 优化移动端用户体验

**长期规划（1年内）**：
1. 开发智能推荐门店功能
2. 集成第三方地图和导航
3. 支持更多类型的服务商品
4. 开发商户运营工具和数据分析

### 12.3 成功标准

**功能完整性**：
- 所有设计功能正常运行
- 用户操作流程顺畅
- 商户管理功能完善
- 数据统计准确可靠

**性能指标**：
- 核销接口响应时间 < 2秒
- 系统可用性 > 99.9%
- 并发支持 > 1000用户
- 数据准确性 100%

**业务指标**：
- 自提订单占比 > 20%
- 核销成功率 > 95%
- 用户满意度 > 4.5分
- 商户采用率 > 80%

### 12.4 风险控制措施

**技术风险控制**：
1. **分阶段上线**：先实现基础功能，再逐步完善
2. **充分测试**：功能测试、性能测试、安全测试全覆盖
3. **监控完善**：实时监控系统运行状态和业务指标
4. **回滚机制**：准备快速回滚方案应对突发问题

**业务风险控制**：
1. **用户培训**：提供详细的操作指南和视频教程
2. **客服支持**：加强客服团队对新功能的培训
3. **反馈机制**：建立用户反馈收集和处理机制
4. **灰度发布**：逐步扩大功能使用范围

### 12.5 关键里程碑

**第一阶段里程碑（2周）**：
- [ ] 完成数据库设计和创建
- [ ] 完成基础实体类和DAO开发
- [ ] 完成门店管理功能
- [ ] 完成员工管理功能

**第二阶段里程碑（5周）**：
- [ ] 完成自提订单创建流程
- [ ] 完成整单核销功能
- [ ] 完成用户端页面开发
- [ ] 完成商户端页面开发

**第三阶段里程碑（7周）**：
- [ ] 完成服务商品配置功能
- [ ] 完成部分核销功能
- [ ] 完成核销记录和统计
- [ ] 完成功能测试

**第四阶段里程碑（8周）**：
- [ ] 完成性能测试和优化
- [ ] 完成安全测试
- [ ] 完成文档编写
- [ ] 准备上线发布

通过以上详细的设计和实施方案，CereShop多商户电商系统将具备完善的商品自提和核销功能，为用户和商户提供更好的服务体验，推动平台业务的持续发展。

## 13. 订单创建流程完善（第三阶段更新）

### 13.1 用户端订单创建流程集成

#### 13.1.1 订单参数扩展
在`OrderParam`类中新增自提相关参数：
- `deliveryType`: 配送方式（1=快递配送，2=门店自提）
- `pickupStoreId`: 自提门店ID
- `pickupStoreName`: 自提门店名称
- `pickupContact`: 自提联系人
- `pickupPhone`: 自提联系电话

#### 13.1.2 订单创建逻辑扩展
在`NormalPlaceOrder`类中新增`handlePickupOrder`方法：
1. **参数验证**：验证自提必要参数的完整性
2. **门店验证**：验证门店存在性、归属性和自提支持
3. **运费处理**：自提订单运费设为0，重新计算订单金额
4. **状态设置**：设置初始核销状态为待核销

#### 13.1.3 支付成功后处理
在`CereShopOrderServiceImpl`中新增`handlePickupOrderAfterPayment`方法：
1. **核销码生成**：调用核销服务生成唯一核销码
2. **订单状态更新**：将自提订单状态设为"待核销"
3. **异常处理**：确保支付流程不受影响

#### 13.1.4 订单状态扩展
在`IntegerEnum`中新增订单状态：
- `ORDER_STAY_VERIFY`: 订单状态-待核销（状态码：7）

### 13.2 核销完成后的业务处理

#### 13.2.1 订单状态流转
自提订单的完整状态流转：
```
待付款(1) → 待核销(7) → 已完成(4)
```

#### 13.2.2 核销后业务事件
在`CereVerifyServiceImpl`中新增`triggerAfterVerifyEvents`方法：
1. **用户积分更新**：根据订单金额计算积分
2. **分销佣金计算**：触发分销佣金结算
3. **核销成功通知**：发送用户和商户通知
4. **销量统计更新**：更新商品销量数据

### 13.3 前端对接要求更新

#### 13.3.1 购物车页面新增功能
1. **配送方式选择器**：支持快递配送/门店自提切换
2. **门店选择组件**：
   - 门店列表展示（名称、地址、距离、营业时间）
   - 地图定位和附近门店查询
   - 门店详情查看
3. **自提信息填写**：联系人和联系电话输入

#### 13.3.2 订单详情页面新增功能
1. **核销码展示**：
   - 二维码生成和展示
   - 核销码文本显示
   - 核销状态实时更新
2. **门店信息展示**：
   - 门店名称、地址、联系电话
   - 营业时间和自提时间
   - 导航功能集成

#### 13.3.3 订单列表页面新增功能
1. **配送方式标识**：区分快递和自提订单
2. **核销状态标签**：待核销、已核销状态显示
3. **筛选功能**：按配送方式筛选订单

### 13.4 API接口使用说明

#### 13.4.1 订单创建接口扩展
```javascript
// 订单提交参数示例
{
  "deliveryType": 2,           // 门店自提
  "pickupStoreId": 1,          // 门店ID
  "pickupStoreName": "旗舰店",  // 门店名称
  "pickupContact": "张三",      // 联系人
  "pickupPhone": "13800138000", // 联系电话
  // ... 其他订单参数
}
```

#### 13.4.2 核销码获取接口
```javascript
// 获取订单核销码
GET /app/order/verify/code/{orderId}

// 响应示例
{
  "code": 200,
  "data": "CS12345678",
  "message": "success"
}
```

#### 13.4.3 核销状态查询接口
```javascript
// 获取核销状态
GET /app/order/verify/status/{orderId}

// 响应示例
{
  "code": 200,
  "data": 0,  // 0=待核销，1=部分核销，2=已核销
  "message": "success"
}
```

### 13.5 错误处理和异常情况

#### 13.5.1 订单创建阶段错误
- 门店不存在：返回错误码1001
- 门店不支持自提：返回错误码1002
- 自提参数缺失：返回错误码400

#### 13.5.2 支付成功后错误
- 核销码生成失败：记录日志，不影响支付流程
- 订单状态更新失败：记录日志，支持手动修复

#### 13.5.3 核销阶段错误
- 核销码无效：返回错误码1003
- 订单已核销：返回错误码1004
- 员工无权限：返回错误码1005

---

**文档版本**: v1.3
**创建时间**: 2025-08-26
**最后更新**: 2025-08-26
**文档状态**: 开发阶段
**负责人**: 开发团队
**更新内容**:
- v1.0: 初始版本，基础架构和数据库设计
- v1.1: 新增第三阶段订单创建流程完善内容
- v1.2: 完善多商户购物车支持，基于现有订单结构扩展自提功能
- v1.3: 完善商户端功能设计，包含门店管理、员工管理、核销管理、商品管理的详细页面设计和API接口
