-- CereShop 商品自提和核销功能 - 第一阶段数据库脚本
-- 创建时间: 2025-08-26
-- 版本: v1.3
-- 最后更新: 2025-08-26
-- 更新内容:
--   v1.0: 初始版本，基础表结构
--   v1.1: 完善商品表扩展，删除cere_service_product表设计
--   v1.2: 添加订单商品表扩展，支持商品级别的配送方式选择
--   v1.3: 完善员工权限管理，添加触发器和存储过程

-- =====================================================
-- 1. 商品表扩展（在现有商品表基础上添加自提相关字段）
-- =====================================================

-- 扩展现有商品表，添加自提相关字段
ALTER TABLE `cere_shop_product`
    ADD COLUMN `support_pickup` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否支持自提：0=否，1=是' AFTER `update_time`,
    ADD COLUMN `verify_total_times` INT(11) NOT NULL DEFAULT 1 COMMENT '核销总次数，默认1次' AFTER `support_pickup`,
    ADD COLUMN `verify_valid_days` INT(11) NOT NULL DEFAULT 30 COMMENT '核销有效天数' AFTER `verify_total_times`,
    ADD COLUMN `pickup_advance_time` INT(11) NOT NULL DEFAULT 0 COMMENT '自提提前预约时间(小时)' AFTER `verify_valid_days`,
    ADD COLUMN `store_limit` TEXT COMMENT '可使用门店限制，JSON格式存储门店ID数组' AFTER `pickup_advance_time`,
    ADD COLUMN `time_limit` TEXT COMMENT '使用时间限制，JSON格式' AFTER `store_limit`,
    ADD COLUMN `usage_rules` TEXT COMMENT '使用规则说明' AFTER `time_limit`;

-- 添加索引
ALTER TABLE `cere_shop_product`
    ADD INDEX `idx_support_pickup` (`support_pickup`);

-- 商品配置字段说明和示例数据
-- store_limit字段示例：
-- {
--   "type": "include",           // include=仅限指定门店，exclude=排除指定门店，all=所有门店
--   "storeIds": [1, 2, 3, 5],   // 门店ID数组
--   "description": "仅限旗舰店和体验店使用"
-- }

-- time_limit字段示例：
-- {
--   "validFrom": "2025-08-26",   // 开始日期
--   "validTo": "2025-12-31",     // 结束日期
--   "weekdays": [1, 2, 3, 4, 5], // 可用星期（1-7，1=周一）
--   "timeRanges": [              // 可用时间段
--     {"start": "09:00", "end": "12:00"},
--     {"start": "14:00", "end": "18:00"}
--   ],
--   "holidays": {                // 节假日规则
--     "available": false,        // 节假日是否可用
--     "exceptions": ["2025-10-01"] // 例外日期
--   }
-- }

-- usage_rules字段示例：
-- {
--   "description": "本商品为体验类服务，需提前2小时预约",
--   "rules": [
--     "每人限购1次",
--     "不可退款",
--     "需携带身份证",
--     "儿童需成人陪同"
--   ],
--   "notices": [
--     "请准时到店，逾期作废",
--     "如需改期请提前1天联系客服"
--   ]
-- }

-- =====================================================
-- 2. 商户门店表
-- =====================================================
CREATE TABLE `cere_merchant_store` (
  `store_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '门店ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `store_name` varchar(100) NOT NULL COMMENT '门店名称',
  `store_code` varchar(50) DEFAULT NULL COMMENT '门店编码',
  `contact_name` varchar(50) NOT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
  `province_id` int(11) DEFAULT NULL COMMENT '省份ID',
  `city_id` int(11) DEFAULT NULL COMMENT '城市ID',
  `area_id` int(11) DEFAULT NULL COMMENT '区域ID',
  `address` varchar(255) NOT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `business_hours` varchar(200) DEFAULT NULL COMMENT '营业时间，JSON格式',
  `pickup_hours` varchar(200) DEFAULT NULL COMMENT '自提时间，JSON格式',
  `store_image` varchar(500) DEFAULT NULL COMMENT '门店图片',
  `store_desc` text COMMENT '门店描述',
  `is_pickup_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持自提：0=否，1=是',
  `is_verify_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持核销：0=否，1=是',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`store_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_pickup_enabled` (`is_pickup_enabled`),
  KEY `idx_verify_enabled` (`is_verify_enabled`),
  KEY `idx_store_code` (`store_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户门店表';

-- =====================================================
-- 2. 门店员工表
-- =====================================================
CREATE TABLE `cere_store_staff` (
  `staff_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `store_id` bigint(20) NOT NULL COMMENT '门店ID',
  `staff_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `staff_code` varchar(50) DEFAULT NULL COMMENT '员工工号',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `role` tinyint(2) NOT NULL DEFAULT 1 COMMENT '角色：1=普通员工，2=店长，3=区域经理',
  `permissions` varchar(200) DEFAULT NULL COMMENT '权限列表，逗号分隔',
  `wechat_openid` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `wechat_bound` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否绑定微信：0=否，1=是',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`staff_id`),
  UNIQUE KEY `uk_merchant_phone` (`merchant_id`, `phone`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_wechat_openid` (`wechat_openid`),
  KEY `idx_status` (`status`),
  KEY `idx_staff_code` (`staff_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店员工表';

-- =====================================================
-- 3. 员工权限表
-- =====================================================
CREATE TABLE `cere_staff_permission` (
  `permission_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_name` varchar(50) NOT NULL COMMENT '权限名称',
  `permission_code` varchar(50) NOT NULL COMMENT '权限代码',
  `permission_desc` varchar(200) DEFAULT NULL COMMENT '权限描述',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`permission_id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工权限表';

-- =====================================================
-- 4. 扩展订单表（添加自提相关字段）
-- =====================================================

-- 扩展现有订单表，添加自提相关字段
ALTER TABLE `cere_shop_order`
    ADD COLUMN `delivery_type` TINYINT(2) NOT NULL DEFAULT 1 COMMENT '配送方式：1=快递配送，2=门店自提' AFTER `logistics_price`,
    ADD COLUMN `pickup_store_id` BIGINT(20) DEFAULT NULL COMMENT '自提门店ID' AFTER `delivery_type`,
    ADD COLUMN `pickup_store_name` VARCHAR(100) DEFAULT NULL COMMENT '自提门店名称' AFTER `pickup_store_id`,
    ADD COLUMN `pickup_contact` VARCHAR(50) DEFAULT NULL COMMENT '自提联系人' AFTER `pickup_store_name`,
    ADD COLUMN `pickup_phone` VARCHAR(20) DEFAULT NULL COMMENT '自提联系电话' AFTER `pickup_contact`,
    ADD COLUMN `verify_code` VARCHAR(20) DEFAULT NULL COMMENT '核销码' AFTER `pickup_phone`,
    ADD COLUMN `verify_status` TINYINT(2) NOT NULL DEFAULT 0 COMMENT '核销状态：0=待核销，1=部分核销，2=已核销' AFTER `verify_code`;

-- 添加索引
ALTER TABLE `cere_shop_order`
    ADD INDEX `idx_delivery_type` (`delivery_type`),
    ADD INDEX `idx_pickup_store_id` (`pickup_store_id`),
    ADD UNIQUE INDEX `uk_verify_code` (`verify_code`),
    ADD INDEX `idx_verify_status` (`verify_status`);

-- =====================================================
-- 5. 扩展订单商品表（添加自提和核销相关字段）
-- =====================================================

-- 扩展现有订单商品表，添加自提和核销相关字段
ALTER TABLE `cere_order_product`
    ADD COLUMN `selected_delivery_type` TINYINT(2) NOT NULL DEFAULT 1 COMMENT '用户选择的配送方式：1=快递，2=自提' AFTER `after_sale_status`,
    ADD COLUMN `pickup_store_id` BIGINT(20) DEFAULT NULL COMMENT '自提门店ID' AFTER `selected_delivery_type`,
    ADD COLUMN `pickup_store_name` VARCHAR(100) DEFAULT NULL COMMENT '自提门店名称' AFTER `pickup_store_id`,
    ADD COLUMN `is_service_product` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为服务类商品：0=否，1=是' AFTER `pickup_store_name`,
    ADD COLUMN `service_type` TINYINT(2) DEFAULT NULL COMMENT '服务类型：1=次数卡，2=时长卡，3=课程包' AFTER `is_service_product`,
    ADD COLUMN `total_times` INT(11) DEFAULT NULL COMMENT '总服务次数（仅服务类商品）' AFTER `service_type`,
    ADD COLUMN `used_times` INT(11) NOT NULL DEFAULT 0 COMMENT '已使用次数' AFTER `total_times`,
    ADD COLUMN `remaining_times` INT(11) DEFAULT NULL COMMENT '剩余可用次数' AFTER `used_times`,
    ADD COLUMN `validity_days` INT(11) DEFAULT NULL COMMENT '有效期天数' AFTER `remaining_times`,
    ADD COLUMN `expire_date` DATE DEFAULT NULL COMMENT '到期日期' AFTER `validity_days`,
    ADD COLUMN `service_status` TINYINT(2) NOT NULL DEFAULT 0 COMMENT '服务状态：0=未开始，1=使用中，2=已完成，3=已过期' AFTER `expire_date`,
    ADD COLUMN `first_use_time` DATETIME DEFAULT NULL COMMENT '首次使用时间' AFTER `service_status`,
    ADD COLUMN `last_use_time` DATETIME DEFAULT NULL COMMENT '最后使用时间' AFTER `first_use_time`,
    ADD COLUMN `verify_times` INT(11) NOT NULL DEFAULT 0 COMMENT '已核销次数' AFTER `last_use_time`,
    ADD COLUMN `verify_status` TINYINT(2) NOT NULL DEFAULT 0 COMMENT '核销状态：0=未核销，1=部分核销，2=已核销' AFTER `verify_times`;

-- 添加索引
ALTER TABLE `cere_order_product`
    ADD INDEX `idx_selected_delivery_type` (`selected_delivery_type`),
    ADD INDEX `idx_pickup_store_id` (`pickup_store_id`),
    ADD INDEX `idx_is_service_product` (`is_service_product`),
    ADD INDEX `idx_service_status` (`service_status`),
    ADD INDEX `idx_verify_status` (`verify_status`),
    ADD INDEX `idx_expire_date` (`expire_date`);

-- =====================================================
-- 6. 核销记录表
-- =====================================================
CREATE TABLE `cere_verify_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_sn` varchar(32) NOT NULL COMMENT '订单号',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `order_product_id` bigint(20) NOT NULL COMMENT '订单商品ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `sku_name` varchar(255) DEFAULT NULL COMMENT 'SKU名称',
  `verify_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '核销类型：1=整单核销，2=单商品核销，3=服务次数核销',
  `verify_times` int(11) NOT NULL DEFAULT 1 COMMENT '本次核销次数',
  `remaining_times` int(11) DEFAULT NULL COMMENT '剩余次数（服务类商品）',
  `verify_code` varchar(32) NOT NULL COMMENT '核销码',
  `staff_id` bigint(20) NOT NULL COMMENT '核销员工ID',
  `staff_name` varchar(50) NOT NULL COMMENT '核销员工姓名',
  `store_id` bigint(20) NOT NULL COMMENT '核销门店ID',
  `store_name` varchar(100) NOT NULL COMMENT '核销门店名称',
  `verify_time` datetime NOT NULL COMMENT '核销时间',
  `service_date` date DEFAULT NULL COMMENT '服务日期（可能与核销时间不同）',
  `service_duration` int(11) DEFAULT NULL COMMENT '实际服务时长(分钟)',
  `remark` varchar(500) DEFAULT NULL COMMENT '核销备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_order_product_id` (`order_product_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_verify_code` (`verify_code`),
  KEY `idx_staff_id` (`staff_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_verify_time` (`verify_time`),
  KEY `idx_service_date` (`service_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核销记录表';


-- =====================================================
-- 4. 扩展订单表 - 添加自提相关字段
-- =====================================================
-- 第一阶段：基础自提功能字段
ALTER TABLE `cere_shop_order`
    ADD COLUMN `delivery_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '配送方式：1=快递配送，2=门店自提' AFTER `update_time`,
    ADD COLUMN `pickup_store_id` bigint(20) DEFAULT NULL COMMENT '自提门店ID' AFTER `delivery_type`,
    ADD COLUMN `pickup_store_name` varchar(100) DEFAULT NULL COMMENT '自提门店名称' AFTER `pickup_store_id`,
    ADD COLUMN `pickup_contact` varchar(50) DEFAULT NULL COMMENT '自提联系人' AFTER `pickup_store_name`,
    ADD COLUMN `pickup_phone` varchar(20) DEFAULT NULL COMMENT '自提联系电话' AFTER `pickup_contact`,
    ADD COLUMN `verify_code` varchar(32) DEFAULT NULL COMMENT '核销码' AFTER `pickup_phone`,
    ADD COLUMN `verify_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '核销状态：0=待核销，1=部分核销，2=已核销' AFTER `verify_code`;

-- 添加索引
ALTER TABLE `cere_shop_order`
    ADD INDEX `idx_delivery_type` (`delivery_type`),
    ADD INDEX `idx_pickup_store_id` (`pickup_store_id`),
    ADD INDEX `idx_verify_code` (`verify_code`),
    ADD INDEX `idx_verify_status` (`verify_status`);

-- =====================================================
-- 5. 初始化历史数据
-- =====================================================
-- 初始化现有订单的配送方式（默认为快递配送）
UPDATE `cere_shop_order` SET `delivery_type` = 1 WHERE `delivery_type` IS NULL;

-- =====================================================
-- 6. 创建默认门店（为现有商户创建默认门店）
-- =====================================================
INSERT INTO `cere_merchant_store` (
    `merchant_id`,
    `store_name`,
    `contact_name`,
    `contact_phone`,
    `address`,
    `is_pickup_enabled`,
    `is_verify_enabled`,
    `status`
)
SELECT
    s.`shop_id` as merchant_id,
    CONCAT(s.`shop_name`, '默认门店') as store_name,
    COALESCE(s.`charge_person_name`, '管理员') as contact_name,
    COALESCE(s.`charge_person_phone`, '***********') as contact_phone,
    COALESCE(s.`shop_adress`, '待完善地址') as address,
    1 as is_pickup_enabled,
    1 as is_verify_enabled,
    1 as status
FROM `cere_platform_shop` s
WHERE s.`shop_id` NOT IN (
    SELECT DISTINCT `merchant_id` FROM `cere_merchant_store`
)
  AND s.`state` = 1;

-- =====================================================
-- 8. 初始化权限数据
-- =====================================================

-- 插入默认权限数据
INSERT INTO `cere_staff_permission` (`permission_name`, `permission_code`, `permission_desc`, `sort_order`, `status`) VALUES
('自提权限', 'pickup', '处理门店自提订单的权限', 1, 1),
('核销权限', 'verify', '核销订单和服务的权限', 2, 1),
('退款权限', 'refund', '处理订单退款的权限', 3, 1),
('订单查询权限', 'order_query', '查询订单信息的权限', 4, 1),
('统计查看权限', 'statistics', '查看统计报表的权限', 5, 1);

-- =====================================================
-- 9. 触发器（自动化业务逻辑）
-- =====================================================

-- 订单支付成功后自动生成核销码的触发器
DELIMITER $$
CREATE TRIGGER `tr_generate_verify_code`
AFTER UPDATE ON `cere_shop_order`
FOR EACH ROW
BEGIN
    -- 当订单状态从待付款变为已付款，且是自提订单时，生成核销码
    IF OLD.state = 1 AND NEW.state = 2 AND NEW.delivery_type = 2 AND NEW.verify_code IS NULL THEN
        UPDATE cere_shop_order
        SET verify_code = CONCAT('CS', LPAD(FLOOR(RAND() * 100000000), 8, '0')),
            verify_status = 0,
            state = 7  -- 设置为待核销状态
        WHERE order_id = NEW.order_id;
    END IF;
END$$
DELIMITER ;

-- 核销完成后自动更新订单状态的触发器
DELIMITER $$
CREATE TRIGGER `tr_update_order_after_verify`
AFTER INSERT ON `cere_verify_log`
FOR EACH ROW
BEGIN
    DECLARE total_products INT DEFAULT 0;
    DECLARE verified_products INT DEFAULT 0;

    -- 统计订单中的商品数量和已核销商品数量
    SELECT COUNT(*), SUM(CASE WHEN verify_status = 2 THEN 1 ELSE 0 END)
    INTO total_products, verified_products
    FROM cere_order_product
    WHERE order_id = NEW.order_id;

    -- 更新订单核销状态
    IF verified_products = total_products THEN
        -- 全部核销完成
        UPDATE cere_shop_order
        SET verify_status = 2, state = 4  -- 已核销，已完成
        WHERE order_id = NEW.order_id;
    ELSEIF verified_products > 0 THEN
        -- 部分核销
        UPDATE cere_shop_order
        SET verify_status = 1  -- 部分核销
        WHERE order_id = NEW.order_id;
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 10. 存储过程（复杂业务逻辑）
-- =====================================================

-- 执行整单核销的存储过程
DELIMITER $$
CREATE PROCEDURE `sp_verify_whole_order`(
    IN p_verify_code VARCHAR(20),
    IN p_staff_id BIGINT,
    IN p_remark VARCHAR(500),
    OUT p_result INT,
    OUT p_message VARCHAR(200)
)
BEGIN
    DECLARE v_order_id BIGINT DEFAULT 0;
    DECLARE v_order_count INT DEFAULT 0;
    DECLARE v_merchant_id BIGINT DEFAULT 0;
    DECLARE v_store_id BIGINT DEFAULT 0;
    DECLARE v_staff_store_id BIGINT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = -1;
        SET p_message = '核销失败，系统错误';
    END;

    START TRANSACTION;

    -- 验证核销码是否存在且有效
    SELECT order_id, shop_id INTO v_order_id, v_merchant_id
    FROM cere_shop_order
    WHERE verify_code = p_verify_code
      AND delivery_type = 2
      AND verify_status < 2
      AND state = 7;

    GET DIAGNOSTICS v_order_count = ROW_COUNT;

    IF v_order_count = 0 THEN
        SET p_result = 1;
        SET p_message = '核销码无效或订单已核销';
        ROLLBACK;
    ELSE
        -- 验证员工权限和门店匹配
        SELECT store_id INTO v_staff_store_id
        FROM cere_store_staff
        WHERE staff_id = p_staff_id
          AND merchant_id = v_merchant_id
          AND status = 1
          AND FIND_IN_SET('verify', permissions) > 0;

        IF v_staff_store_id IS NULL THEN
            SET p_result = 2;
            SET p_message = '员工无核销权限或门店不匹配';
            ROLLBACK;
        ELSE
            SET v_store_id = v_staff_store_id;

            -- 更新订单商品核销状态
            UPDATE cere_order_product
            SET verify_status = 2,
                verify_times = total_times,
                used_times = total_times,
                remaining_times = 0,
                last_use_time = NOW(),
                service_status = CASE WHEN is_service_product = 1 THEN 2 ELSE service_status END
            WHERE order_id = v_order_id;

            -- 插入核销日志
            INSERT INTO cere_verify_log (
                order_id, order_sn, order_product_id, product_id, product_name,
                sku_id, sku_name, verify_code, verify_type, verify_times,
                remaining_times, merchant_id, store_id, store_name,
                staff_id, staff_name, buyer_user_id, buyer_name, buyer_phone,
                verify_time, remark
            )
            SELECT
                op.order_id, o.order_sn, op.order_product_id, op.product_id, op.product_name,
                op.sku_id, op.sku_name, o.verify_code, 1, op.total_times,
                0, o.shop_id, v_store_id, o.pickup_store_name,
                p_staff_id, s.staff_name, o.buyer_user_id, o.buyer_name, o.buyer_phone,
                NOW(), p_remark
            FROM cere_order_product op
            JOIN cere_shop_order o ON op.order_id = o.order_id
            JOIN cere_store_staff s ON s.staff_id = p_staff_id
            WHERE op.order_id = v_order_id;

            SET p_result = 0;
            SET p_message = '核销成功';
            COMMIT;
        END IF;
    END IF;
END$$
DELIMITER ;

-- =====================================================
-- 11. 视图和统计
-- =====================================================

-- 创建核销统计视图
CREATE VIEW `v_verify_statistics` AS
SELECT
    DATE(vl.verify_time) as verify_date,
    vl.merchant_id,
    vl.store_id,
    vl.store_name,
    COUNT(*) as verify_count,
    COUNT(DISTINCT vl.order_id) as order_count,
    COUNT(DISTINCT vl.buyer_user_id) as user_count
FROM cere_verify_log vl
WHERE vl.verify_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(vl.verify_time), vl.merchant_id, vl.store_id, vl.store_name;

-- 创建门店自提订单统计视图
CREATE VIEW `v_pickup_order_statistics` AS
SELECT
    DATE(o.create_time) as order_date,
    o.shop_id as merchant_id,
    o.pickup_store_id,
    o.pickup_store_name,
    COUNT(*) as pickup_order_count,
    SUM(o.order_price) as pickup_order_amount,
    COUNT(CASE WHEN o.verify_status = 2 THEN 1 END) as verified_order_count
FROM cere_shop_order o
WHERE o.delivery_type = 2
  AND o.create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(o.create_time), o.shop_id, o.pickup_store_id, o.pickup_store_name;

-- =====================================================
-- 12. 索引优化
-- =====================================================

-- 为高频查询添加复合索引
ALTER TABLE `cere_verify_log` ADD INDEX `idx_merchant_store_time` (`merchant_id`, `store_id`, `verify_time`);
ALTER TABLE `cere_shop_order` ADD INDEX `idx_delivery_verify_status` (`delivery_type`, `verify_status`);
ALTER TABLE `cere_order_product` ADD INDEX `idx_service_expire` (`is_service_product`, `expire_date`);

-- =====================================================
-- 13. 数据完整性约束
-- =====================================================

-- 添加检查约束
ALTER TABLE `cere_merchant_store` ADD CONSTRAINT `chk_store_longitude` CHECK (`longitude` BETWEEN -180 AND 180);
ALTER TABLE `cere_merchant_store` ADD CONSTRAINT `chk_store_latitude` CHECK (`latitude` BETWEEN -90 AND 90);
ALTER TABLE `cere_store_staff` ADD CONSTRAINT `chk_staff_role` CHECK (`role` IN (1, 2, 3));
ALTER TABLE `cere_order_product` ADD CONSTRAINT `chk_verify_times` CHECK (`verify_times` >= 0 AND `verify_times` <= `total_times`);
ALTER TABLE `cere_order_product` ADD CONSTRAINT `chk_remaining_times` CHECK (`remaining_times` >= 0);

-- =====================================================
-- 14. 性能优化建议和维护脚本
-- =====================================================

-- 定期清理过期核销日志的存储过程
DELIMITER $$
CREATE PROCEDURE `sp_cleanup_verify_logs`(
    IN p_days_to_keep INT
)
BEGIN
    DECLARE v_cutoff_date DATE;
    SET v_cutoff_date = DATE_SUB(CURDATE(), INTERVAL p_days_to_keep DAY);

    DELETE FROM cere_verify_log
    WHERE DATE(verify_time) < v_cutoff_date;

    SELECT ROW_COUNT() as deleted_rows;
END$$
DELIMITER ;

-- =====================================================
-- 15. 示例数据（可选，用于测试）
-- =====================================================

-- 插入示例门店数据（仅用于测试环境）
-- INSERT INTO `cere_merchant_store` (
--     `merchant_id`, `store_name`, `store_code`, `contact_name`, `contact_phone`,
--     `address`, `longitude`, `latitude`, `business_hours`, `pickup_hours`,
--     `is_pickup_enabled`, `is_verify_enabled`, `status`
-- ) VALUES
-- (1, '旗舰店', 'STORE001', '张经理', '0755-12345678',
--  '深圳市南山区科技园', 113.123456, 22.123456, '09:00-21:00', '09:00-20:00',
--  1, 1, 1),
-- (1, '体验店', 'STORE002', '李经理', '0755-87654321',
--  '深圳市福田区中心区', 114.123456, 22.654321, '10:00-22:00', '10:00-21:00',
--  1, 1, 1);

-- 插入示例员工数据（仅用于测试环境）
-- INSERT INTO `cere_store_staff` (
--     `merchant_id`, `store_id`, `staff_name`, `staff_code`, `phone`,
--     `role`, `permissions`, `status`
-- ) VALUES
-- (1, 1, '张三', 'STAFF001', '***********', 1, 'pickup,verify,order_query', 1),
-- (1, 1, '李四', 'STAFF002', '***********', 2, 'pickup,verify,refund,order_query,statistics', 1);

-- =====================================================
-- 结束
-- =====================================================

-- 脚本执行完成提示
SELECT 'CereShop 商品自提和核销功能数据库初始化完成！' as message;
SELECT 'Version: v1.3' as version;
SELECT NOW() as completed_at;
