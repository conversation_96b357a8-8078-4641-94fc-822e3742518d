-- =====================================================
-- 触发器最终修复版本
-- 解决字段名不匹配问题
-- =====================================================

-- 删除可能存在的旧触发器
DROP TRIGGER IF EXISTS `tr_create_default_store_after_shop_insert`;
DROP TRIGGER IF EXISTS `tr_generate_verify_code`;
DROP TRIGGER IF EXISTS `tr_update_order_after_verify`;

-- =====================================================
-- 1. 修复后的商户默认门店创建触发器
-- 字段名修正：contact_name -> charge_person_name, contact_phone -> charge_person_phone
-- =====================================================
DELIMITER $$

CREATE TRIGGER `tr_create_default_store_after_shop_insert`
AFTER INSERT ON `cere_platform_shop`
FOR EACH ROW
BEGIN
    DECLARE create_time_str VARCHAR(20);
    DECLARE default_store_name VARCHAR(100);
    DECLARE default_store_code VARCHAR(50);

    -- 使用非保留关键字作为变量名
    SET create_time_str = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');
    SET default_store_name = CONCAT(IFNULL(NEW.shop_name, '商户'), '默认门店');
    SET default_store_code = CONCAT('STORE_', NEW.shop_id, '_001');

    -- 插入默认门店记录（只使用确定存在的字段）
    INSERT INTO `cere_merchant_store` (
        `merchant_id`, `store_name`, `store_code`, `contact_name`, `contact_phone`,
        `address`, `business_hours`, `pickup_hours`,
        `store_desc`, `is_pickup_enabled`, `is_verify_enabled`, `sort_order`,
        `status`, `is_default`, `create_time`, `update_time`
    ) VALUES (
        NEW.shop_id,
        default_store_name,
        default_store_code,
        IFNULL(NEW.charge_person_name, '管理员'),
        IFNULL(NEW.charge_person_phone, '***********'),
        IFNULL(NEW.shop_adress, '待完善详细地址'),
        '{"monday":"09:00-21:00","tuesday":"09:00-21:00","wednesday":"09:00-21:00","thursday":"09:00-21:00","friday":"09:00-21:00","saturday":"09:00-21:00","sunday":"09:00-21:00"}',
        '{"monday":"09:00-20:00","tuesday":"09:00-20:00","wednesday":"09:00-20:00","thursday":"09:00-20:00","friday":"09:00-20:00","saturday":"09:00-20:00","sunday":"09:00-20:00"}',
        '系统自动创建的默认门店，请及时完善门店信息',
        1, 1, 100, 1, 1,
        create_time_str, create_time_str
    );
END$$

DELIMITER ;

-- =====================================================
-- 2. 修复后的核销码生成触发器
-- =====================================================
DELIMITER $$

CREATE TRIGGER `tr_generate_verify_code`
AFTER UPDATE ON `cere_shop_order`
FOR EACH ROW
BEGIN
    DECLARE verify_code_str VARCHAR(20);
    
    -- 当订单状态从待付款变为已付款，且是自提订单时，生成核销码
    IF OLD.state = 1 AND NEW.state = 2 AND NEW.delivery_type = 2 AND NEW.verify_code IS NULL THEN
        -- 生成8位随机数字核销码
        SET verify_code_str = CONCAT('CS', LPAD(FLOOR(RAND() * 100000000), 8, '0'));
        
        UPDATE cere_shop_order
        SET verify_code = verify_code_str,
            verify_status = 0,
            state = 7  -- 设置为待核销状态
        WHERE order_id = NEW.order_id;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 3. 修复后的核销完成状态更新触发器
-- =====================================================
DELIMITER $$

CREATE TRIGGER `tr_update_order_after_verify`
AFTER INSERT ON `cere_verify_log`
FOR EACH ROW
BEGIN
    DECLARE total_products INT DEFAULT 0;
    DECLARE verified_products INT DEFAULT 0;
    DECLARE update_time_str VARCHAR(20);

    SET update_time_str = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');

    -- 统计订单中的商品数量和已核销商品数量
    SELECT COUNT(*), SUM(CASE WHEN verify_status = 2 THEN 1 ELSE 0 END)
    INTO total_products, verified_products
    FROM cere_order_product
    WHERE order_id = NEW.order_id;

    -- 更新订单核销状态
    IF verified_products = total_products THEN
        -- 全部核销完成
        UPDATE cere_shop_order
        SET verify_status = 2, 
            state = 4,  -- 已核销，已完成
            update_time = update_time_str
        WHERE order_id = NEW.order_id;
    ELSEIF verified_products > 0 THEN
        -- 部分核销
        UPDATE cere_shop_order
        SET verify_status = 1,  -- 部分核销
            update_time = update_time_str
        WHERE order_id = NEW.order_id;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 4. 字段名对照表（用于参考）
-- =====================================================
/*
cere_platform_shop 表字段名（确认存在的字段）：
- shop_id (商户ID)
- shop_name (商户名称)
- charge_person_name (负责人姓名) ← 注意：不是 contact_name
- charge_person_phone (负责人电话) ← 注意：不是 contact_phone
- shop_adress (商户地址) ← 注意：拼写是 adress，不是 address

注意：province_id, city_id, area_id 字段在该表中可能不存在，触发器中已移除

cere_merchant_store 表字段名：
- merchant_id (商户ID)
- store_name (门店名称)
- store_code (门店编码)
- contact_name (联系人姓名)
- contact_phone (联系电话)
- province_id (省份ID)
- city_id (城市ID)
- area_id (区域ID)
- address (详细地址)
- business_hours (营业时间)
- pickup_hours (自提时间)
- is_pickup_enabled (是否支持自提)
- is_verify_enabled (是否支持核销)
- is_default (是否默认门店)
*/

-- =====================================================
-- 5. 触发器验证查询
-- =====================================================

-- 查看触发器是否创建成功
SHOW TRIGGERS LIKE 'tr_%';

-- 查看触发器详细信息
SELECT 
    TRIGGER_NAME,
    EVENT_MANIPULATION,
    EVENT_OBJECT_TABLE,
    ACTION_TIMING,
    TRIGGER_SCHEMA,
    CREATED
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE()
AND TRIGGER_NAME LIKE 'tr_%';

-- =====================================================
-- 6. 测试用例
-- =====================================================

-- 测试商户创建触发器（只使用确定存在的字段）
/*
INSERT INTO cere_platform_shop (
    shop_name,
    charge_person_name,
    charge_person_phone,
    shop_adress
) VALUES (
    '测试商户',
    '张三',
    '***********',
    '测试地址123号'
);

-- 验证是否自动创建了默认门店
SELECT * FROM cere_merchant_store WHERE merchant_id = LAST_INSERT_ID();
*/

-- =====================================================
-- 7. 常见问题解决方案
-- =====================================================

/*
问题1：Unknown column 'contact_name' in 'NEW'
解决：cere_platform_shop表中的字段名是charge_person_name，不是contact_name

问题2：Unknown column 'contact_phone' in 'NEW'
解决：cere_platform_shop表中的字段名是charge_person_phone，不是contact_phone

问题3：Unknown column 'province_id' in 'NEW'
解决：cere_platform_shop表中可能不存在province_id, city_id, area_id字段，已从触发器中移除

问题4：You have an error in your SQL syntax near 'current_time'
解决：current_time是MySQL保留关键字，改为create_time_str等非保留字

问题5：触发器执行失败
解决：检查表结构是否匹配，字段类型是否正确，外键约束是否满足

问题6：JSON格式错误
解决：确保JSON字符串格式正确，使用双引号，避免特殊字符
*/
