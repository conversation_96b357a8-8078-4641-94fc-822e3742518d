-- =====================================================
-- 触发器系统数据库表结构
-- =====================================================

-- =====================================================
-- 1. 触发器配置表
-- =====================================================
CREATE TABLE `cere_trigger` (
  `trigger_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '触发器ID',
  `trigger_name` varchar(100) NOT NULL COMMENT '触发器名称',
  `trigger_code` varchar(50) NOT NULL COMMENT '触发器编码',
  `trigger_desc` varchar(500) DEFAULT NULL COMMENT '触发器描述',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `trigger_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '触发器类型：1=数据变更触发，2=时间触发，3=事件触发',
  `trigger_event` varchar(50) NOT NULL COMMENT '触发事件：INSERT/UPDATE/DELETE/TIMER/CUSTOM',
  `target_table` varchar(100) DEFAULT NULL COMMENT '目标表名（数据变更触发时使用）',
  `trigger_condition` text COMMENT '触发条件JSON配置',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
  `execute_mode` tinyint(2) NOT NULL DEFAULT 1 COMMENT '执行模式：1=同步执行，2=异步执行',
  `retry_times` int(11) NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `retry_interval` int(11) NOT NULL DEFAULT 60 COMMENT '重试间隔(秒)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`trigger_id`),
  UNIQUE KEY `uk_merchant_code` (`merchant_id`, `trigger_code`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_trigger_type` (`trigger_type`),
  KEY `idx_trigger_event` (`trigger_event`),
  KEY `idx_target_table` (`target_table`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器配置表';

-- =====================================================
-- 2. 触发器字段配置表
-- =====================================================
CREATE TABLE `cere_trigger_field` (
  `field_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字段ID',
  `trigger_id` bigint(20) NOT NULL COMMENT '触发器ID',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_code` varchar(50) NOT NULL COMMENT '字段编码',
  `field_type` varchar(20) NOT NULL COMMENT '字段类型：STRING/INTEGER/DECIMAL/DATE/BOOLEAN/JSON',
  `field_desc` varchar(500) DEFAULT NULL COMMENT '字段描述',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填：0=否，1=是',
  `default_value` text COMMENT '默认值',
  `validation_rule` text COMMENT '验证规则JSON配置',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`field_id`),
  UNIQUE KEY `uk_trigger_code` (`trigger_id`, `field_code`),
  KEY `idx_trigger_id` (`trigger_id`),
  KEY `idx_field_type` (`field_type`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_trigger_field_trigger` FOREIGN KEY (`trigger_id`) REFERENCES `cere_trigger` (`trigger_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器字段配置表';

-- =====================================================
-- 3. 触发器值配置表
-- =====================================================
CREATE TABLE `cere_trigger_value` (
  `value_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '值ID',
  `trigger_id` bigint(20) NOT NULL COMMENT '触发器ID',
  `field_id` bigint(20) NOT NULL COMMENT '字段ID',
  `value_name` varchar(100) NOT NULL COMMENT '值名称',
  `value_code` varchar(50) NOT NULL COMMENT '值编码',
  `value_content` text NOT NULL COMMENT '值内容',
  `value_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '值类型：1=固定值，2=变量值，3=表达式值，4=函数值',
  `value_desc` varchar(500) DEFAULT NULL COMMENT '值描述',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认值：0=否，1=是',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`value_id`),
  UNIQUE KEY `uk_field_code` (`field_id`, `value_code`),
  KEY `idx_trigger_id` (`trigger_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_value_type` (`value_type`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_trigger_value_trigger` FOREIGN KEY (`trigger_id`) REFERENCES `cere_trigger` (`trigger_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_trigger_value_field` FOREIGN KEY (`field_id`) REFERENCES `cere_trigger_field` (`field_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器值配置表';

-- =====================================================
-- 4. 触发器条件表
-- =====================================================
CREATE TABLE `cere_trigger_condition` (
  `condition_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '条件ID',
  `trigger_id` bigint(20) NOT NULL COMMENT '触发器ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父条件ID（用于条件分组）',
  `condition_name` varchar(100) NOT NULL COMMENT '条件名称',
  `condition_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '条件类型：1=字段条件，2=表达式条件，3=函数条件',
  `field_name` varchar(100) DEFAULT NULL COMMENT '字段名称',
  `operator` varchar(20) NOT NULL COMMENT '操作符：=,!=,>,<,>=,<=,LIKE,IN,NOT IN,IS NULL,IS NOT NULL',
  `condition_value` text COMMENT '条件值',
  `value_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '值类型：1=固定值，2=字段值，3=变量值，4=表达式值',
  `logic_operator` varchar(10) DEFAULT 'AND' COMMENT '逻辑操作符：AND/OR',
  `condition_group` varchar(50) DEFAULT NULL COMMENT '条件分组',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`condition_id`),
  KEY `idx_trigger_id` (`trigger_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_condition_type` (`condition_type`),
  KEY `idx_field_name` (`field_name`),
  KEY `idx_condition_group` (`condition_group`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_enabled` (`is_enabled`),
  CONSTRAINT `fk_trigger_condition_trigger` FOREIGN KEY (`trigger_id`) REFERENCES `cere_trigger` (`trigger_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_trigger_condition_parent` FOREIGN KEY (`parent_id`) REFERENCES `cere_trigger_condition` (`condition_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器条件表';

-- =====================================================
-- 5. 触发器动作表
-- =====================================================
CREATE TABLE `cere_trigger_action` (
  `action_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '动作ID',
  `trigger_id` bigint(20) NOT NULL COMMENT '触发器ID',
  `action_name` varchar(100) NOT NULL COMMENT '动作名称',
  `action_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '动作类型：1=数据库操作，2=HTTP请求，3=消息发送，4=函数调用，5=脚本执行',
  `action_config` text NOT NULL COMMENT '动作配置JSON',
  `action_params` text COMMENT '动作参数JSON',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '执行顺序',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
  `on_success` varchar(20) DEFAULT 'CONTINUE' COMMENT '成功后操作：CONTINUE=继续，STOP=停止',
  `on_failure` varchar(20) DEFAULT 'CONTINUE' COMMENT '失败后操作：CONTINUE=继续，STOP=停止，RETRY=重试',
  `timeout` int(11) DEFAULT 30 COMMENT '超时时间(秒)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`action_id`),
  KEY `idx_trigger_id` (`trigger_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_enabled` (`is_enabled`),
  CONSTRAINT `fk_trigger_action_trigger` FOREIGN KEY (`trigger_id`) REFERENCES `cere_trigger` (`trigger_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器动作表';

-- =====================================================
-- 6. 触发器执行日志表
-- =====================================================
CREATE TABLE `cere_trigger_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `trigger_id` bigint(20) NOT NULL COMMENT '触发器ID',
  `trigger_name` varchar(100) NOT NULL COMMENT '触发器名称',
  `execute_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
  `execute_status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '执行状态：1=成功，2=失败，3=部分成功',
  `trigger_data` text COMMENT '触发数据JSON',
  `execute_result` text COMMENT '执行结果JSON',
  `error_message` text COMMENT '错误信息',
  `execute_duration` int(11) DEFAULT NULL COMMENT '执行耗时(毫秒)',
  `retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '重试次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_trigger_id` (`trigger_id`),
  KEY `idx_execute_time` (`execute_time`),
  KEY `idx_execute_status` (`execute_status`),
  KEY `idx_retry_count` (`retry_count`),
  CONSTRAINT `fk_trigger_log_trigger` FOREIGN KEY (`trigger_id`) REFERENCES `cere_trigger` (`trigger_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器执行日志表';

-- =====================================================
-- 7. 触发器变量表
-- =====================================================
CREATE TABLE `cere_trigger_variable` (
  `variable_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '变量ID',
  `trigger_id` bigint(20) NOT NULL COMMENT '触发器ID',
  `variable_name` varchar(100) NOT NULL COMMENT '变量名称',
  `variable_code` varchar(50) NOT NULL COMMENT '变量编码',
  `variable_type` varchar(20) NOT NULL COMMENT '变量类型：STRING/INTEGER/DECIMAL/DATE/BOOLEAN/JSON',
  `variable_value` text COMMENT '变量值',
  `variable_desc` varchar(500) DEFAULT NULL COMMENT '变量描述',
  `is_global` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否全局变量：0=否，1=是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`variable_id`),
  UNIQUE KEY `uk_trigger_code` (`trigger_id`, `variable_code`),
  KEY `idx_trigger_id` (`trigger_id`),
  KEY `idx_variable_type` (`variable_type`),
  KEY `idx_is_global` (`is_global`),
  CONSTRAINT `fk_trigger_variable_trigger` FOREIGN KEY (`trigger_id`) REFERENCES `cere_trigger` (`trigger_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器变量表';

-- =====================================================
-- 8. 添加索引优化
-- =====================================================
-- 为触发器表添加复合索引
ALTER TABLE `cere_trigger` ADD INDEX `idx_merchant_enabled_priority` (`merchant_id`, `is_enabled`, `priority`);
ALTER TABLE `cere_trigger` ADD INDEX `idx_event_table_enabled` (`trigger_event`, `target_table`, `is_enabled`);

-- 为触发器条件表添加复合索引
ALTER TABLE `cere_trigger_condition` ADD INDEX `idx_trigger_enabled_order` (`trigger_id`, `is_enabled`, `sort_order`);

-- 为触发器动作表添加复合索引
ALTER TABLE `cere_trigger_action` ADD INDEX `idx_trigger_enabled_order` (`trigger_id`, `is_enabled`, `sort_order`);

-- 为触发器日志表添加复合索引
ALTER TABLE `cere_trigger_log` ADD INDEX `idx_trigger_time_status` (`trigger_id`, `execute_time`, `execute_status`);
