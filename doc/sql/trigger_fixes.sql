-- =====================================================
-- 触发器语法修复文件
-- =====================================================

-- 删除可能存在的旧触发器
DROP TRIGGER IF EXISTS `tr_create_default_store_after_shop_insert`;
DROP TRIGGER IF EXISTS `tr_generate_verify_code`;
DROP TRIGGER IF EXISTS `tr_update_order_after_verify`;

-- =====================================================
-- 1. 修复后的商户默认门店创建触发器
-- =====================================================
DELIMITER $$

CREATE TRIGGER `tr_create_default_store_after_shop_insert`
AFTER INSERT ON `cere_platform_shop`
FOR EACH ROW
BEGIN
    DECLARE create_time_str VARCHAR(20);
    DECLARE default_store_name VARCHAR(100);
    DECLARE default_store_code VARCHAR(50);

    -- 使用非保留关键字作为变量名
    SET create_time_str = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');
    SET default_store_name = CONCAT(IFNULL(NEW.shop_name, '商户'), '默认门店');
    SET default_store_code = CONCAT('STORE_', NEW.shop_id, '_001');

    -- 插入默认门店记录
    INSERT INTO `cere_merchant_store` (
        `merchant_id`, `store_name`, `store_code`, `contact_name`, `contact_phone`,
        `province_id`, `city_id`, `area_id`, `address`, `business_hours`, `pickup_hours`,
        `store_desc`, `is_pickup_enabled`, `is_verify_enabled`, `sort_order`,
        `status`, `is_default`, `create_time`, `update_time`
    ) VALUES (
        NEW.shop_id, default_store_name, default_store_code,
        IFNULL(NEW.contact_name, ''), IFNULL(NEW.contact_phone, ''),
        IFNULL(NEW.province_id, 0), IFNULL(NEW.city_id, 0), IFNULL(NEW.area_id, 0),
        IFNULL(NEW.shop_adress, '待完善详细地址'), 
        '{"monday":"09:00-21:00","tuesday":"09:00-21:00","wednesday":"09:00-21:00","thursday":"09:00-21:00","friday":"09:00-21:00","saturday":"09:00-21:00","sunday":"09:00-21:00"}',
        '{"monday":"09:00-20:00","tuesday":"09:00-20:00","wednesday":"09:00-20:00","thursday":"09:00-20:00","friday":"09:00-20:00","saturday":"09:00-20:00","sunday":"09:00-20:00"}',
        '系统自动创建的默认门店，请及时完善门店信息', 1, 1, 100, 1, 1,
        create_time_str, create_time_str
    );
END$$

DELIMITER ;

-- =====================================================
-- 2. 修复后的核销码生成触发器
-- =====================================================
DELIMITER $$

CREATE TRIGGER `tr_generate_verify_code`
AFTER UPDATE ON `cere_shop_order`
FOR EACH ROW
BEGIN
    DECLARE verify_code_str VARCHAR(20);
    
    -- 当订单状态从待付款变为已付款，且是自提订单时，生成核销码
    IF OLD.state = 1 AND NEW.state = 2 AND NEW.delivery_type = 2 AND NEW.verify_code IS NULL THEN
        -- 生成8位随机数字核销码
        SET verify_code_str = CONCAT('CS', LPAD(FLOOR(RAND() * 100000000), 8, '0'));
        
        UPDATE cere_shop_order
        SET verify_code = verify_code_str,
            verify_status = 0,
            state = 7  -- 设置为待核销状态
        WHERE order_id = NEW.order_id;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 3. 修复后的核销完成状态更新触发器
-- =====================================================
DELIMITER $$

CREATE TRIGGER `tr_update_order_after_verify`
AFTER INSERT ON `cere_verify_log`
FOR EACH ROW
BEGIN
    DECLARE total_products INT DEFAULT 0;
    DECLARE verified_products INT DEFAULT 0;
    DECLARE update_time_str VARCHAR(20);

    SET update_time_str = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');

    -- 统计订单中的商品数量和已核销商品数量
    SELECT COUNT(*), SUM(CASE WHEN verify_status = 2 THEN 1 ELSE 0 END)
    INTO total_products, verified_products
    FROM cere_order_product
    WHERE order_id = NEW.order_id;

    -- 更新订单核销状态
    IF verified_products = total_products THEN
        -- 全部核销完成
        UPDATE cere_shop_order
        SET verify_status = 2, 
            state = 4,  -- 已核销，已完成
            update_time = update_time_str
        WHERE order_id = NEW.order_id;
    ELSEIF verified_products > 0 THEN
        -- 部分核销
        UPDATE cere_shop_order
        SET verify_status = 1,  -- 部分核销
            update_time = update_time_str
        WHERE order_id = NEW.order_id;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 4. 触发器状态检查
-- =====================================================
-- 查看触发器是否创建成功
SHOW TRIGGERS LIKE 'tr_%';

-- 查看触发器详细信息
SELECT 
    TRIGGER_NAME,
    EVENT_MANIPULATION,
    EVENT_OBJECT_TABLE,
    ACTION_TIMING,
    TRIGGER_SCHEMA,
    CREATED
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE()
AND TRIGGER_NAME LIKE 'tr_%';

-- =====================================================
-- 5. 触发器测试建议
-- =====================================================
/*
测试步骤：
1. 测试商户创建触发器：
   INSERT INTO cere_platform_shop (shop_name, contact_name, contact_phone) 
   VALUES ('测试商户', '测试联系人', '13800138000');
   
2. 检查是否自动创建了默认门店：
   SELECT * FROM cere_merchant_store WHERE merchant_id = LAST_INSERT_ID();

3. 测试核销码生成触发器：
   -- 先创建一个自提订单，然后更新状态为已付款
   UPDATE cere_shop_order SET state = 2 WHERE order_id = ? AND delivery_type = 2;
   
4. 检查核销码是否生成：
   SELECT verify_code, verify_status, state FROM cere_shop_order WHERE order_id = ?;

5. 测试核销完成触发器：
   INSERT INTO cere_verify_log (...) VALUES (...);
   
6. 检查订单状态是否更新：
   SELECT verify_status, state FROM cere_shop_order WHERE order_id = ?;
*/
