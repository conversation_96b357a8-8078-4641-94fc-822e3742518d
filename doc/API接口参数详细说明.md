# CereShop 自提核销功能 API接口参数详细说明

## 文档概述

本文档详细说明了CereShop自提核销功能涉及的所有API接口的请求参数、响应参数和数据流转过程。

## 用户端API接口

### 1. 门店相关接口

#### 1.1 获取支持自提的门店列表
```
GET /app/store/pickup/{merchantId}
```

**路径参数**：
- `merchantId` (Long): 商户ID，必填

**响应参数**：
```json
{
  "code": "00000",
  "message": "success",
  "data": [
    {
      "storeId": 1,                    // 门店ID
      "merchantId": 100,               // 商户ID
      "storeName": "旗舰店",            // 门店名称
      "storeCode": "STORE001",         // 门店编码
      "contactName": "张经理",          // 联系人
      "contactPhone": "0755-12345678", // 联系电话
      "address": "深圳市南山区科技园",   // 详细地址
      "longitude": 113.123456,         // 经度
      "latitude": 22.123456,           // 纬度
      "businessHours": "09:00-21:00",  // 营业时间
      "pickupHours": "09:00-20:00",    // 自提时间
      "storeImage": "https://...",     // 门店图片
      "isPickupEnabled": 1,            // 是否支持自提
      "sortOrder": 100,                // 排序权重
      "status": 1                      // 状态：1=启用
    }
  ]
}
```

#### 1.2 获取附近支持自提的门店
```
GET /app/store/pickup/{merchantId}/nearby
```

**路径参数**：
- `merchantId` (Long): 商户ID，必填

**查询参数**：
- `longitude` (Double): 用户经度，必填
- `latitude` (Double): 用户纬度，必填
- `distance` (Double): 搜索半径(公里)，可选，默认50

**响应参数**：
```json
{
  "code": "00000",
  "message": "success",
  "data": [
    {
      // ... 门店基本信息（同上）
      "distance": 2.5  // 距离用户的距离(公里)
    }
  ]
}
```

### 2. 订单相关接口

#### 2.1 提交订单（基于现有结构扩展）
```
POST /app/order/submit
```

**请求参数**（基于现有结构扩展）：
```json
{
  "shops": [
    {
      "shopId": 100,                   // 商户ID，必填
      "skus": [                        // 使用现有的skus结构
        {
          "skuId": 67890,              // SKU ID，必填
          "number": 2,                 // 购买数量，必填
          "ifLogistics": 1,            // 保持原有逻辑：商品是否支持物流
          "selected": 1,               // 是否选中，必填

          // 新增：用户选择的配送方式
          "selectedDeliveryType": 2,   // 1=快递，2=自提，必填
          "pickupStoreId": 5,          // 自提门店ID（当selectedDeliveryType=2时必填）
          "pickupStoreName": "旗舰店",  // 自提门店名称
          "verifyTotalTimes": 1,       // 核销总次数
          "verifyValidDays": 30,       // 有效天数

          // 其他现有字段保持不变
          "shopSeckillId": 0,
          "shopDiscountId": 0,
          "platformSeckillId": 0,
          "platformDiscountId": 0,
          "useMember": false,
          "priceId": 0,
          "composeId": 0,
          "sceneId": 0,
          "weight": 0.00
        }
      ],
      "distribution": {
        "logisticsId": null,           // 有自提商品时可为null
        "distributionName": "混合配送", // 描述配送方式
        "distributionPrice": 0,

        // 新增配送类型统计
        "hasExpressProducts": false,   // 是否有快递商品
        "hasPickupProducts": true,     // 是否有自提商品
        "deliveryType": 2              // 主要配送方式
      }
    }
  ],

  // 收货地址（快递商品需要，自提商品不需要）
  "receiveId": 4849,

  // 新增：自提联系信息（有自提商品时必填）
  "pickupContact": "张三",
  "pickupPhone": "13800138000",

  // 其他现有字段保持不变
  "couponId": 0,
  "price": 1122.00,
  "discountPrice": 0.00,
  "remark": "",
  "paymentMode": 1,
  "subPaymentMode": 1
}
```

**响应参数**：
```json
{
  "code": "00000",
  "message": "success",
  "data": {
    "payUrl": "weixin://wxpay/bizpayurl?pr=xxx",  // 支付链接
    "orderId": 123456,                            // 订单ID
    "orderSn": "202508260001",                    // 订单号
    "totalAmount": 198.00                         // 订单总金额
  }
}
```

#### 2.2 获取订单详情
```
GET /app/order/detail/{orderId}
```

**路径参数**：
- `orderId` (Long): 订单ID，必填

**响应参数**：
```json
{
  "code": "00000",
  "message": "success",
  "data": {
    "orderId": 123456,
    "orderSn": "202508260001",
    "state": 7,                        // 订单状态：7=待核销
    "deliveryType": 2,                 // 配送方式：2=门店自提
    "verifyCode": "CS12345678",        // 核销码（支付成功后生成）
    "verifyStatus": 0,                 // 核销状态：0=待核销
    
    // 自提相关信息
    "pickupStoreId": 1,
    "pickupStoreName": "旗舰店",
    "pickupContact": "张三",
    "pickupPhone": "13800138000",
    
    // 订单基本信息
    "buyerUserId": 1001,
    "totalAmount": 198.00,
    "paymentMode": 1,
    "createTime": "2025-08-26 10:30:00",
    "payTime": "2025-08-26 10:35:00",
    
    // 商品信息（包含配送方式）
    "products": [
      {
        "productId": 12345,
        "productName": "测试商品",
        "skuId": 67890,
        "skuName": "红色/L",
        "number": 2,
        "price": 99.00,
        "totalPrice": 198.00,
        "selectedDeliveryType": 2,   // 该商品选择的配送方式
        "pickupStoreId": 1,          // 该商品的自提门店
        "verifyTotalTimes": 1,       // 核销总次数
        "verifyValidDays": 30        // 有效天数
      }
    ]
  }
}
```

### 3. 核销相关接口

#### 3.1 获取订单核销码
```
GET /app/order/verify/code/{orderId}
```

**路径参数**：
- `orderId` (Long): 订单ID，必填

**响应参数**：
```json
{
  "code": "00000",
  "message": "success",
  "data": "CS12345678"  // 核销码字符串
}
```

#### 3.2 获取订单核销状态
```
GET /app/order/verify/status/{orderId}
```

**路径参数**：
- `orderId` (Long): 订单ID，必填

**响应参数**：
```json
{
  "code": "00000",
  "message": "success",
  "data": 0  // 核销状态：0=待核销，1=部分核销，2=已核销
}
```

#### 3.3 获取核销记录
```
GET /app/order/verify/logs/{orderId}
```

**路径参数**：
- `orderId` (Long): 订单ID，必填

**响应参数**：
```json
{
  "code": "00000",
  "message": "success",
  "data": [
    {
      "logId": 1,
      "orderId": 123456,
      "orderSn": "202508260001",
      "productName": "测试商品",
      "skuName": "红色/L",
      "verifyType": 1,                 // 核销类型：1=整单核销
      "verifyTimes": 1,                // 本次核销次数
      "remainingTimes": 0,             // 剩余次数
      "staffName": "李员工",            // 核销员工
      "storeName": "旗舰店",            // 核销门店
      "verifyTime": "2025-08-26 14:30:00",  // 核销时间
      "remark": "用户已到店取货"        // 核销备注
    }
  ]
}
```

## 商户端API接口

### 1. 核销管理接口

#### 1.1 验证核销码
```
GET /business/verify/validate
```

**查询参数**：
- `verifyCode` (String): 核销码，必填
- `staffId` (Long): 员工ID，必填

**响应参数**：
```json
{
  "code": "00000",
  "message": "success",
  "data": {
    "orderId": 123456,
    "orderSn": "202508260001",
    "buyerName": "李四",
    "buyerPhone": "***********",
    "totalAmount": 198.00,
    "verifyStatus": 0,               // 当前核销状态
    "canVerify": true,               // 是否可以核销
    "products": [
      {
        "productId": 12345,
        "productName": "测试商品",
        "skuName": "红色/L",
        "number": 2,
        "price": 99.00,
        "canVerifyTimes": 1          // 可核销次数
      }
    ]
  }
}
```

#### 1.2 执行整单核销
```
POST /business/verify/whole-order
```

**请求参数**：
```json
{
  "verifyCode": "CS12345678",        // 核销码，必填
  "staffId": 1,                      // 员工ID，必填
  "remark": "用户已到店取货"          // 核销备注，可选
}
```

**响应参数**：
```json
{
  "code": "00000",
  "message": "核销成功"
}
```

## 数据流转过程

### 1. 订单创建到核销码生成
```
用户提交订单 → 订单创建成功 → 用户支付 → 支付成功回调
    ↓
后台检测自提订单 → 生成核销码 → 更新订单状态 → 用户可查看核销码
```

### 2. 核销操作流程
```
商户扫码/输入核销码 → 调用验证接口 → 显示订单信息 → 确认核销
    ↓
调用核销接口 → 更新订单状态 → 记录核销日志 → 触发后续业务
```

### 3. 状态变更流程
```
订单状态流转：
待付款(1) → 待核销(7) → 已完成(4)

核销状态流转：
待核销(0) → 已核销(2)
```

---

**文档版本**: v1.0  
**创建时间**: 2025-08-26  
**最后更新**: 2025-08-26
