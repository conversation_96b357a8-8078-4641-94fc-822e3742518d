# CereShop 商品自提和核销功能 - 前端对接文档

## 文档概述

本文档为CereShop多商户电商系统商品自提和核销功能的前端对接指南，包含完整的业务流程、API接口、交互流程、页面设计要求和数据格式说明。

## 目录

1. [完整业务流程](#完整业务流程)
2. [用户端（C端）对接](#用户端c端对接)
3. [商户端（B端）对接](#商户端b端对接)
4. [平台管理端对接](#平台管理端对接)
5. [通用数据格式](#通用数据格式)
6. [错误码说明](#错误码说明)

## 完整业务流程

### 1. 自提订单完整流程图

```
用户选择商品 → 加入购物车 → 进入结算页面 → 选择配送方式
    ↓
选择门店自提 → 获取门店列表 → 选择门店 → 填写自提信息
    ↓
提交订单 → 订单创建成功 → 进入支付 → 支付成功
    ↓
生成核销码 → 订单状态变为"待核销" → 用户查看核销码
    ↓
用户到店 → 出示核销码 → 商户扫码核销 → 核销完成 → 订单完成
```

### 2. 核心API流程和参数流转

#### 2.1 商品选择到购物车阶段

**流程**：用户浏览商品 → 选择规格 → 加入购物车

**涉及API**：
- 商品详情：`GET /app/product/{productId}`
- 加入购物车：`POST /app/cart/add`

**关键参数**：
```javascript
// 加入购物车参数
{
  "productId": 12345,
  "skuId": 67890,
  "number": 2,
  "shopId": 100
}
```

#### 2.2 购物车到订单创建阶段

**流程**：查看购物车 → 选择配送方式 → 选择门店 → 填写信息 → 提交订单

**步骤1：获取购物车信息（扩展自提信息）**
```javascript
GET /app/cart/list

// 响应数据（扩展后）
{
  "code": "00000",
  "data": {
    "shops": [
      {
        "shopId": 100,
        "shopName": "测试商户",
        "products": [
          {
            "cartId": 1001,
            "productId": 12345,
            "skuId": 67890,
            "productName": "测试商品",
            "price": 99.00,
            "number": 2,

            // 新增：配送支持情况
            "supportExpress": true,       // 是否支持快递
            "supportPickup": true,        // 是否支持自提
            "deliverySupportType": 3,     // 1=仅快递，2=仅自提，3=都支持

            // 新增：用户选择的配送方式
            "selectedDeliveryType": 2,    // 1=快递，2=自提
            "pickupStoreId": 5,           // 选择的门店ID
            "pickupStoreName": "旗舰店",   // 门店名称

            // 新增：自提限制信息
            "storeLimit": {
              "type": "include",
              "storeIds": [1, 2, 5],
              "description": "仅限指定门店"
            },
            "timeLimit": {
              "validFrom": "2025-08-26",
              "validTo": "2025-12-31"
            },
            "usageRules": {
              "description": "体验类服务商品",
              "rules": ["需提前预约", "不可退款"]
            }
          }
        ]
      }
    ]
  }
}
```

**步骤2：获取支持自提的门店列表**
```javascript
GET /app/store/pickup/{merchantId}

// 响应数据
{
  "code": "00000",
  "data": [
    {
      "storeId": 1,
      "storeName": "旗舰店",
      "address": "深圳市南山区科技园",
      "contactPhone": "0755-12345678",
      "longitude": 113.123456,
      "latitude": 22.123456,
      "businessHours": "09:00-21:00",
      "pickupHours": "09:00-20:00",
      "distance": 2.5
    }
  ]
}
```

**步骤3：获取附近门店（可选）**
```javascript
GET /app/store/pickup/{merchantId}/nearby?longitude=113.123456&latitude=22.123456&distance=10

// 参数说明
{
  "merchantId": 100,        // 商户ID
  "longitude": 113.123456,  // 用户经度
  "latitude": 22.123456,    // 用户纬度
  "distance": 10            // 搜索半径(公里)
}
```

**步骤4：提交订单（基于现有结构扩展）**
```javascript
POST /app/order/submit

// 请求参数（基于现有结构扩展）
{
  "shops": [
    {
      "shopId": 100,
      "skus": [                           // 使用现有的skus结构
        {
          "skuId": 67890,
          "number": 2,
          "ifLogistics": 1,               // 保持原有逻辑：商品是否支持物流
          "selected": 1,

          // 新增：用户选择的配送方式
          "selectedDeliveryType": 2,      // 1=快递，2=自提
          "pickupStoreId": 5,             // 自提门店ID
          "pickupStoreName": "旗舰店",     // 自提门店名称
          "verifyTotalTimes": 1,          // 核销总次数
          "verifyValidDays": 30,          // 有效天数

          // 其他现有字段保持不变
          "shopSeckillId": 0,
          "shopDiscountId": 0,
          "platformSeckillId": 0,
          "platformDiscountId": 0,
          "useMember": false,
          "priceId": 0,
          "composeId": 0,
          "sceneId": 0,
          "weight": 0.00
        }
      ],
      "distribution": {
        "logisticsId": null,              // 有自提商品时可为null
        "distributionName": "混合配送",    // 描述配送方式
        "distributionPrice": 0,

        // 新增配送类型统计
        "hasExpressProducts": false,      // 是否有快递商品
        "hasPickupProducts": true,        // 是否有自提商品
        "deliveryType": 2                 // 主要配送方式
      }
    }
  ],

  // 收货地址（快递商品需要，自提商品不需要）
  "receiveId": 4849,

  // 新增：自提联系信息（有自提商品时必填）
  "pickupContact": "张三",
  "pickupPhone": "***********",

  // 其他现有字段保持不变
  "couponId": 0,
  "price": 1122.00,
  "discountPrice": 0.00,
  "remark": "",
  "paymentMode": 1,
  "subPaymentMode": 1
}

// 响应数据
{
  "code": "00000",
  "data": {
    "payUrl": "weixin://wxpay/bizpayurl?pr=xxx",
    "orderId": 123456,
    "orderSn": "202508260001"
  }
}
```

#### 2.3 支付成功到核销码生成阶段

**流程**：支付成功 → 后台生成核销码 → 订单状态更新

**支付成功回调处理**：
- 后台自动检测到支付成功
- 判断是否为自提订单（`deliveryType = 2`）
- 生成唯一核销码（格式：CS + 8位数字）
- 更新订单状态为"待核销"（`state = 7`）
- 设置核销状态为"待核销"（`verifyStatus = 0`）

**核销码生成规则**：
```javascript
// 核销码格式
{
  "verifyCode": "**********",  // CS + 8位随机数字
  "orderId": 123456,
  "orderSn": "202508260001"
}
```

#### 2.4 用户查看核销码阶段

**流程**：用户查看订单 → 获取核销码 → 展示二维码

**步骤1：获取订单详情**
```javascript
GET /app/order/detail/{orderId}

// 响应数据（包含核销码信息）
{
  "code": "00000",
  "data": {
    "orderId": 123456,
    "orderSn": "202508260001",
    "state": 7,                    // 订单状态：7=待核销
    "deliveryType": 2,             // 配送方式：2=门店自提
    "verifyCode": "**********",    // 核销码
    "verifyStatus": 0,             // 核销状态：0=待核销
    "pickupStoreId": 1,
    "pickupStoreName": "旗舰店",
    "pickupContact": "张三",
    "pickupPhone": "***********",
    "products": [...]
  }
}
```

**步骤2：获取核销码（专用接口）**
```javascript
GET /app/order/verify/code/{orderId}

// 响应数据
{
  "code": "00000",
  "data": "**********"
}
```

**步骤3：获取核销状态**
```javascript
GET /app/order/verify/status/{orderId}

// 响应数据
{
  "code": "00000",
  "data": 0  // 0=待核销，1=部分核销，2=已核销
}
```

#### 2.5 商户核销阶段

**流程**：商户扫码 → 验证核销码 → 执行核销 → 更新状态

**步骤1：验证核销码**
```javascript
GET /business/verify/validate?verifyCode=**********&staffId=1

// 响应数据
{
  "code": "00000",
  "data": {
    "orderId": 123456,
    "orderSn": "202508260001",
    "buyerName": "李四",
    "buyerPhone": "***********",
    "products": [
      {
        "productName": "测试商品",
        "skuName": "红色/L",
        "number": 2,
        "price": 99.00
      }
    ],
    "totalAmount": 198.00,
    "canVerify": true
  }
}
```

**步骤2：执行整单核销**
```javascript
POST /business/verify/whole-order

// 请求参数
{
  "verifyCode": "**********",
  "staffId": 1,
  "remark": "用户已到店取货"
}

// 响应数据
{
  "code": "00000",
  "message": "核销成功"
}
```

#### 2.6 核销完成后状态更新

**自动处理流程**：
- 订单状态更新为"已完成"（`state = 4`）
- 核销状态更新为"已核销"（`verifyStatus = 2`）
- 记录核销日志
- 触发后续业务事件（积分、佣金等）

### 3. 关键参数说明

#### 3.1 配送方式（deliveryType）
```javascript
const DELIVERY_TYPES = {
  EXPRESS: 1,    // 快递配送
  PICKUP: 2      // 门店自提
};
```

#### 3.2 订单状态（state）
```javascript
const ORDER_STATES = {
  STAY_PAY: 1,        // 待付款
  STAY_DELIVERY: 2,   // 待发货
  HAVE_DELIVERY: 3,   // 待收货
  FINISH: 4,          // 已完成
  STOP: 5,            // 已取消
  STAY_COLLAGE: 6,    // 待成团
  STAY_VERIFY: 7      // 待核销
};
```

#### 3.3 核销状态（verifyStatus）
```javascript
const VERIFY_STATES = {
  PENDING: 0,     // 待核销
  PARTIAL: 1,     // 部分核销
  COMPLETED: 2    // 已核销
};
```

#### 3.4 支付方式（paymentMode）
```javascript
const PAYMENT_MODES = {
  WECHAT: 1,      // 微信支付
  ALIPAY: 2,      // 支付宝
  BALANCE: 3      // 余额支付
};
```

### 4. 前端页面状态管理

#### 4.1 购物车页面状态
```javascript
// 页面状态（多商户支持）
const cartState = {
  shops: [                   // 按商户分组的购物车数据
    {
      shopId: 100,
      shopName: "测试商户",
      products: [
        {
          cartId: 1001,
          productId: 12345,
          deliverySupportType: 3,    // 配送支持类型
          selectedDeliveryType: 2,   // 用户选择的配送方式
          pickupStoreId: 5,          // 选择的门店ID
          pickupStoreName: "旗舰店"   // 门店名称
        }
      ]
    }
  ],
  pickupInfo: {              // 全局自提联系信息
    contact: '',
    phone: ''
  },
  userLocation: {            // 用户位置
    longitude: null,
    latitude: null
  }
};

// 状态变更流程（多商户）
1. 用户为每个商品选择配送方式 → selectedDeliveryType = 1/2
2. 选择自提时获取对应商户门店列表 → 按shopId获取门店
3. 用户选择门店 → 更新对应商品的pickupStoreId
4. 填写全局自提联系信息 → pickupInfo = {...}
5. 提交订单 → 按商户分组传递参数
```

#### 4.2 订单详情页面状态
```javascript
// 页面状态
const orderDetailState = {
  orderInfo: {},            // 订单基本信息
  verifyCode: '',          // 核销码
  verifyStatus: 0,         // 核销状态
  qrCodeUrl: '',           // 二维码图片URL
  verifyLogs: []           // 核销记录
};

// 状态更新流程
1. 加载订单详情 → orderInfo = {...}
2. 如果是自提订单 → 获取核销码和状态
3. 生成二维码 → qrCodeUrl = generateQRCode(verifyCode)
4. 定时刷新状态 → 检查是否已核销
```

### 5. 错误处理和边界情况

#### 5.1 常见错误情况
```javascript
// 错误码处理
const ERROR_HANDLERS = {
  '11002': '参数不合法',
  '11001': '必填参数不能为空',
  '1001': '核销码无效',
  '1002': '订单已核销',
  '1003': '员工无核销权限',
  '1004': '门店不支持自提',
  '1005': '门店不支持核销'
};
```

#### 5.2 边界情况处理
- **门店列表为空**：提示用户该商户暂不支持自提
- **定位失败**：显示所有门店，按距离排序功能不可用
- **核销码生成失败**：显示错误信息，提供重试按钮
- **网络异常**：提供重试机制和离线提示

## 用户端（C端）对接

### 1.1 购物车页面改造

#### 页面功能要求
- 在购物车结算页面添加配送方式选择
- 支持快递配送和门店自提两种方式切换
- 自提时显示门店选择界面

#### 核心组件设计

**配送方式选择组件**
```javascript
// 配送方式切换
<DeliveryTypeSelector
  value={deliveryType}
  onChange={handleDeliveryTypeChange}
  options={[
    { code: 1, name: '快递配送', icon: 'express' },
    { code: 2, name: '门店自提', icon: 'pickup' }
  ]}
/>
```

**门店选择组件**
```javascript
// 门店选择器
<StoreSelector
  merchantId={shopId}
  userLocation={userLocation}
  selectedStoreId={selectedStoreId}
  onStoreSelect={handleStoreSelect}
  showDistance={true}
/>
```

**自提信息填写组件**
```javascript
// 自提联系信息
<PickupInfoForm
  contact={pickupContact}
  phone={pickupPhone}
  onContactChange={handleContactChange}
  onPhoneChange={handlePhoneChange}
/>
```

### 1.2 订单详情页面改造

#### 页面功能要求
- 区分显示快递订单和自提订单
- 自提订单显示门店信息和核销码
- 显示核销状态和核销记录

#### 核心组件设计

**订单类型判断**
```javascript
// 根据deliveryType判断订单类型
const isPickupOrder = orderDetail.deliveryType === 2;

// 条件渲染不同内容
{isPickupOrder ? (
  <PickupOrderInfo orderDetail={orderDetail} />
) : (
  <ExpressOrderInfo orderDetail={orderDetail} />
)}
```

**核销码展示组件**
```javascript
// 核销码展示
<VerifyCodeDisplay
  verifyCode={orderDetail.verifyCode}
  verifyStatus={orderDetail.verifyStatus}
  storeInfo={{
    storeName: orderDetail.pickupStoreName,
    storeAddress: orderDetail.storeAddress,
    storePhone: orderDetail.storePhone
  }}
  onRefreshStatus={handleRefreshStatus}
/>
```

**核销记录组件**
```javascript
// 核销记录列表
<VerifyLogList
  orderId={orderDetail.orderId}
  logs={verifyLogs}
  onRefresh={handleRefreshLogs}
/>
```

#### 状态轮询机制
```javascript
// 定时检查核销状态
useEffect(() => {
  if (isPickupOrder && verifyStatus === 0) {
    const timer = setInterval(() => {
      checkVerifyStatus(orderId);
    }, 30000); // 30秒检查一次

    return () => clearInterval(timer);
  }
}, [isPickupOrder, verifyStatus, orderId]);
```

### 1.3 订单列表页面改造

#### 页面功能要求
- 订单列表区分显示配送方式
- 自提订单显示核销状态
- 支持按配送方式筛选

#### 核心组件设计

**订单列表项组件**
```javascript
// 订单列表项
<OrderListItem
  order={order}
  showDeliveryType={true}
  showVerifyStatus={order.deliveryType === 2}
  onItemClick={handleOrderClick}
/>
```

**筛选组件**
```javascript
// 订单筛选
<OrderFilter
  filters={{
    deliveryType: selectedDeliveryType,
    verifyStatus: selectedVerifyStatus
  }}
  onFilterChange={handleFilterChange}
/>
```

### 1.4 前端数据流转和状态管理

#### 全局状态管理
```javascript
// 使用Redux/Zustand等状态管理工具
const useOrderStore = create((set, get) => ({
  // 购物车状态
  cart: {
    deliveryType: 1,
    selectedStoreId: null,
    pickupInfo: {
      contact: '',
      phone: ''
    }
  },

  // 订单状态
  currentOrder: null,
  verifyCode: '',
  verifyStatus: 0,

  // 门店状态
  storeList: [],
  selectedStore: null,

  // 操作方法
  setDeliveryType: (type) => set((state) => ({
    cart: { ...state.cart, deliveryType: type }
  })),

  setSelectedStore: (store) => set((state) => ({
    cart: { ...state.cart, selectedStoreId: store.storeId },
    selectedStore: store
  })),

  updateVerifyStatus: (status) => set({ verifyStatus: status })
}));
```

#### 数据流转图
```
用户操作 → 组件状态更新 → API调用 → 后端处理 → 响应数据 → 全局状态更新 → UI重新渲染

具体流程：
1. 用户选择门店自提 → setDeliveryType(2) → 触发门店列表API
2. 用户选择门店 → setSelectedStore(store) → 更新表单数据
3. 提交订单 → submitOrder(params) → 获取支付链接
4. 支付成功 → 后台生成核销码 → 前端轮询获取状态
5. 核销完成 → 后台更新状态 → 前端实时更新显示
```

#### 关键数据结构
```javascript
// 订单提交参数
const orderSubmitParams = {
  shops: [
    {
      shopId: number,
      products: Array<{
        productId: number,
        skuId: number,
        number: number
      }>,
      remark: string
    }
  ],
  paymentMode: number,
  deliveryType: number,
  // 自提相关参数（当deliveryType=2时必填）
  pickupStoreId?: number,
  pickupStoreName?: string,
  pickupContact?: string,
  pickupPhone?: string
};

// 订单详情数据结构
const orderDetail = {
  orderId: number,
  orderSn: string,
  state: number,           // 订单状态
  deliveryType: number,    // 配送方式
  verifyCode?: string,     // 核销码（自提订单）
  verifyStatus?: number,   // 核销状态（自提订单）
  pickupStoreId?: number,
  pickupStoreName?: string,
  pickupContact?: string,
  pickupPhone?: string,
  products: Array<ProductInfo>,
  // ... 其他订单信息
};
```

### 1.5 用户端API接口汇总

#### 门店相关接口
```javascript
// 获取支持自提的门店列表
GET /app/store/pickup/{merchantId}
// 响应：门店列表数组

// 获取附近支持自提的门店
GET /app/store/pickup/{merchantId}/nearby
// 参数：longitude, latitude, distance
// 响应：按距离排序的门店列表

// 获取门店详情
GET /app/store/{storeId}
// 响应：门店详细信息

// 检查门店是否支持自提
GET /app/store/{storeId}/pickup-enabled
// 响应：boolean

// 检查门店是否支持核销
GET /app/store/{storeId}/verify-enabled
// 响应：boolean
```

#### 订单相关接口
```javascript
// 提交订单（扩展自提参数）
POST /app/order/submit
// 参数：包含deliveryType、pickupStoreId等自提参数
// 响应：支付链接和订单信息

// 获取订单详情
GET /app/order/detail/{orderId}
// 响应：包含核销码的完整订单信息
```

#### 核销相关接口
```javascript
// 获取订单核销码
GET /app/order/verify/code/{orderId}
// 响应：核销码字符串

// 获取订单核销状态
GET /app/order/verify/status/{orderId}
// 响应：核销状态数字

// 获取核销记录
GET /app/order/verify/logs/{orderId}
// 响应：核销记录列表

// 根据订单号查询核销记录
GET /app/order/verify/logs/order-sn/{orderSn}
// 响应：核销记录列表

// 根据核销码查询核销记录
GET /app/order/verify/logs/verify-code/{verifyCode}
// 响应：核销记录列表
```

## 商户端（B端）对接

### 2.1 门店管理页面

#### 页面功能要求
- 门店列表展示和管理
- 门店信息的增删改查
- 门店状态管理

#### 相关API接口

**门店管理接口**
```javascript
// 获取门店列表
GET /business/store/list
// 查询参数
{
  page: 1,
  size: 20,
  status: null,           // 门店状态筛选
  isPickupEnabled: null,  // 自提功能筛选
  isVerifyEnabled: null,  // 核销功能筛选
  keyword: ""            // 搜索关键词
}

// 创建门店
POST /business/store/create
// 请求参数
{
  storeName: "旗舰店",
  storeCode: "STORE001",
  contactName: "张经理",
  contactPhone: "0755-12345678",
  provinceId: 440000,
  cityId: 440300,
  areaId: 440305,
  address: "科技园南区",
  longitude: 113.123456,
  latitude: 22.123456,
  businessHours: "09:00-21:00",
  pickupHours: "09:00-20:00",
  isPickupEnabled: 1,
  isVerifyEnabled: 1,
  storeImage: "https://...",
  storeDesc: "门店描述",
  sortOrder: 100
}

// 更新门店信息
PUT /business/store/update/{storeId}
// 请求参数同创建门店

// 删除门店
DELETE /business/store/delete/{storeId}

// 获取门店详情
GET /business/store/{storeId}

// 更新门店状态
PUT /business/store/{storeId}/status
// 请求参数
{
  status: 1  // 1=启用，0=禁用
}

// 批量更新门店状态
PUT /business/store/batch-status
// 请求参数
{
  storeIds: [1, 2, 3],
  status: 1
}
```

#### 前端页面组件设计

**门店列表组件**
```javascript
// 门店列表页面状态
const storeListState = {
  loading: false,
  storeList: [],
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0
  },
  filters: {
    status: null,
    isPickupEnabled: null,
    isVerifyEnabled: null,
    keyword: ''
  },
  selectedStoreIds: []
};

// 页面功能
- 表格展示门店信息（名称、地址、状态、功能开关）
- 多条件筛选和搜索
- 批量操作（启用/禁用）
- 新增门店按钮
- 编辑/删除操作
- 门店详情查看
```

**门店表单组件**
```javascript
// 门店编辑表单状态
const storeFormState = {
  formData: {
    storeName: '',
    storeCode: '',
    contactName: '',
    contactPhone: '',
    provinceId: null,
    cityId: null,
    areaId: null,
    address: '',
    longitude: null,
    latitude: null,
    businessHours: '09:00-21:00',
    pickupHours: '09:00-20:00',
    isPickupEnabled: 1,
    isVerifyEnabled: 1,
    storeImage: '',
    storeDesc: '',
    sortOrder: 100
  },
  rules: {
    storeName: [{ required: true, message: '请输入门店名称' }],
    contactPhone: [{ required: true, message: '请输入联系电话' }],
    address: [{ required: true, message: '请输入详细地址' }]
  }
};

// 页面组件
- 基本信息编辑表单
- 地址选择器（省市区三级联动）
- 地图定位选择器
- 营业时间设置器
- 功能开关配置
- 图片上传组件
- 表单验证和提交
```

### 2.2 员工管理页面

#### 页面功能要求
- 员工列表展示和管理
- 员工权限配置
- 员工状态管理
- 员工微信绑定管理

#### 相关API接口

**员工管理接口**
```javascript
// 获取员工列表
GET /business/staff/list
// 查询参数
{
  page: 1,
  size: 20,
  storeId: null,          // 门店筛选
  role: null,             // 角色筛选
  status: null,           // 状态筛选
  keyword: ""             // 搜索关键词
}

// 创建员工
POST /business/staff/create
// 请求参数
{
  staffName: "张三",
  staffCode: "STAFF001",
  phone: "***********",
  email: "<EMAIL>",
  storeId: 1,
  role: 1,                // 1=普通员工，2=店长，3=区域经理
  permissions: [1, 2, 3], // 权限ID数组
  entryDate: "2025-08-26",
  avatar: "https://...",
  remark: "备注信息"
}

// 更新员工信息
PUT /business/staff/update/{staffId}
// 请求参数同创建员工

// 删除员工
DELETE /business/staff/delete/{staffId}

// 获取员工详情
GET /business/staff/{staffId}

// 更新员工状态
PUT /business/staff/{staffId}/status
// 请求参数
{
  status: 1  // 1=启用，0=禁用
}

// 重置员工密码
PUT /business/staff/{staffId}/reset-password

// 获取权限列表
GET /business/staff/permissions

// 批量分配权限
PUT /business/staff/batch-permissions
// 请求参数
{
  staffIds: [1, 2, 3],
  permissions: [1, 2, 3]
}
```

#### 前端页面组件设计

**员工列表组件**
```javascript
// 员工列表页面状态
const staffListState = {
  loading: false,
  staffList: [],
  storeList: [],          // 门店选项
  roleList: [             // 角色选项
    { value: 1, label: '普通员工' },
    { value: 2, label: '店长' },
    { value: 3, label: '区域经理' }
  ],
  permissionList: [],     // 权限选项
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0
  },
  filters: {
    storeId: null,
    role: null,
    status: null,
    keyword: ''
  },
  selectedStaffIds: []
};

// 页面功能
- 员工列表展示（姓名、工号、门店、角色、权限、状态）
- 按门店、角色、状态筛选
- 员工搜索（按姓名、工号、手机号）
- 批量权限分配
- 新增员工按钮
- 编辑/删除/重置密码操作
```

**员工编辑组件**
```javascript
// 员工编辑表单状态
const staffFormState = {
  formData: {
    staffName: '',
    staffCode: '',
    phone: '',
    email: '',
    storeId: null,
    role: 1,
    permissions: [],
    entryDate: '',
    avatar: '',
    remark: ''
  },
  rules: {
    staffName: [{ required: true, message: '请输入员工姓名' }],
    staffCode: [{ required: true, message: '请输入员工工号' }],
    phone: [{ required: true, message: '请输入手机号' }],
    storeId: [{ required: true, message: '请选择所属门店' }]
  }
};

// 页面组件
- 基本信息编辑表单
- 门店选择器
- 角色选择器
- 权限配置组件（多选框）
- 头像上传组件
- 微信绑定状态显示
- 表单验证和提交
```

### 2.3 核销管理页面

#### 核销操作完整流程

**步骤1：扫码或输入核销码**
```javascript
// 扫码组件
<QRCodeScanner
  onScanSuccess={handleScanSuccess}
  onScanError={handleScanError}
/>

// 手动输入
<VerifyCodeInput
  value={verifyCode}
  onChange={setVerifyCode}
  onSubmit={handleVerifyCodeSubmit}
/>
```

**步骤2：验证核销码**
```javascript
// API调用
GET /business/verify/validate?verifyCode=**********&staffId=1

// 响应数据
{
  "code": "00000",
  "data": {
    "orderId": 123456,
    "orderSn": "202508260001",
    "buyerName": "李四",
    "buyerPhone": "***********",
    "totalAmount": 198.00,
    "products": [
      {
        "productName": "测试商品",
        "skuName": "红色/L",
        "number": 2,
        "price": 99.00
      }
    ],
    "canVerify": true,
    "verifyStatus": 0
  }
}
```

**步骤3：确认核销信息**
```javascript
// 核销确认组件
<VerifyConfirmDialog
  orderInfo={orderInfo}
  onConfirm={handleVerifyConfirm}
  onCancel={handleVerifyCancel}
/>
```

**步骤4：执行核销**
```javascript
// API调用
POST /business/verify/whole-order

// 请求参数
{
  "verifyCode": "**********",
  "staffId": 1,
  "remark": "用户已到店取货"
}

// 成功响应
{
  "code": "00000",
  "message": "核销成功"
}
```

#### 核销记录管理

**记录查询组件**
```javascript
// 核销记录列表
<VerifyLogTable
  logs={verifyLogs}
  pagination={pagination}
  filters={filters}
  onFilterChange={handleFilterChange}
  onPageChange={handlePageChange}
/>

// 筛选条件
const filters = {
  startTime: '2025-08-01 00:00:00',
  endTime: '2025-08-31 23:59:59',
  storeId: null,
  staffId: null
};
```

#### 核销统计报表

**统计数据获取**
```javascript
// 获取统计数据
const getVerifyStatistics = async () => {
  const [merchantCount, storeStats] = await Promise.all([
    // 商户总核销数量
    api.get('/business/verify/count/merchant', {
      params: { startTime, endTime }
    }),
    // 各门店核销数量
    api.get('/business/verify/count/stores', {
      params: { startTime, endTime }
    })
  ]);

  return { merchantCount, storeStats };
};
```

### 2.4 商品管理页面扩展

#### 页面功能要求
- 商品自提配置
- 门店使用限制设置
- 时间使用限制设置
- 使用规则配置
- 配置模板管理

#### 相关API接口

**商品自提配置接口**
```javascript
// 获取商品详情（包含自提配置）
GET /business/product/{productId}
// 响应数据包含自提相关字段
{
  "productId": 12345,
  "productName": "测试商品",
  "supportPickup": 1,
  "verifyTotalTimes": 1,
  "verifyValidDays": 30,
  "pickupAdvanceTime": 2,
  "storeLimit": "{\"type\":\"include\",\"storeIds\":[1,2,3]}",
  "timeLimit": "{\"validFrom\":\"2025-08-26\",\"validTo\":\"2025-12-31\"}",
  "usageRules": "{\"description\":\"体验类服务\",\"rules\":[\"需提前预约\"]}"
}

// 更新商品自提配置
PUT /business/product/{productId}/pickup-config
// 请求参数
{
  supportPickup: 1,
  verifyTotalTimes: 1,
  verifyValidDays: 30,
  pickupAdvanceTime: 2,
  storeLimit: {
    type: "include",
    storeIds: [1, 2, 3],
    description: "仅限指定门店"
  },
  timeLimit: {
    validFrom: "2025-08-26",
    validTo: "2025-12-31",
    weekdays: [1, 2, 3, 4, 5],
    timeRanges: [{"start": "09:00", "end": "18:00"}]
  },
  usageRules: {
    description: "体验类服务，需提前预约",
    rules: ["需提前24小时预约", "不可退款"],
    notices: ["请准时到店", "逾期作废"]
  }
}

// 获取配置模板列表
GET /business/product/pickup-templates

// 应用配置模板
PUT /business/product/{productId}/apply-template
// 请求参数
{
  templateId: 1
}

// 批量应用配置
PUT /business/product/batch-pickup-config
// 请求参数
{
  productIds: [1, 2, 3],
  config: { /* 配置对象 */ }
}
```

#### 核心组件设计

**商品自提配置组件**
```javascript
// 自提配置表单状态
const pickupConfigState = {
  formData: {
    supportPickup: false,
    verifyTotalTimes: 1,
    verifyValidDays: 30,
    pickupAdvanceTime: 0,
    storeLimit: {
      type: 'all',
      storeIds: [],
      description: ''
    },
    timeLimit: {
      validFrom: '',
      validTo: '',
      weekdays: [],
      timeRanges: [],
      holidays: {
        available: false,
        exceptions: []
      }
    },
    usageRules: {
      description: '',
      rules: [],
      notices: []
    }
  },
  storeList: [],
  templateList: []
};

// 自提配置表单
<PickupConfigForm
  productData={currentProduct}
  storeList={storeList}
  templateList={templateList}
  onConfigChange={handleConfigChange}
  onTemplateApply={handleTemplateApply}
/>
```

**门店限制选择器组件**
```javascript
// 门店限制选择器
<StoreLimitSelector
  value={storeLimit}
  storeList={storeList}
  onChange={handleStoreLimitChange}
/>

// 组件功能
- 限制类型选择（所有门店/仅限指定/排除指定）
- 门店多选列表
- 自动生成限制说明
- 预览效果展示
```

**时间限制配置器组件**
```javascript
// 时间限制配置器
<TimeLimitSelector
  value={timeLimit}
  onChange={handleTimeLimitChange}
/>

// 组件功能
- 日期范围选择器
- 星期多选组件
- 时间段设置器
- 节假日规则配置
- 配置预览和验证
```

**使用规则编辑器组件**
```javascript
// 使用规则编辑器
<UsageRulesEditor
  value={usageRules}
  onChange={handleUsageRulesChange}
/>

// 组件功能
- 规则描述编辑
- 规则列表管理（增删改）
- 注意事项管理
- 富文本编辑支持
- 模板快速插入
```

**配置模板管理**
```javascript
// 配置模板选择器
<TemplateSelector
  templates={templateList}
  onTemplateSelect={handleTemplateSelect}
  onTemplateApply={handleTemplateApply}
/>

// 模板功能
- 预设模板选择
- 模板预览
- 一键应用模板
- 自定义模板保存
- 模板配置对比
```

### 2.5 商户端API接口汇总

#### 门店管理接口
```javascript
// 门店CRUD操作
GET /business/store/list                    // 获取门店列表
POST /business/store/create                 // 创建门店
PUT /business/store/update/{storeId}        // 更新门店信息
DELETE /business/store/delete/{storeId}     // 删除门店
GET /business/store/{storeId}               // 获取门店详情
PUT /business/store/{storeId}/status        // 更新门店状态
PUT /business/store/batch-status            // 批量更新门店状态
```

#### 员工管理接口
```javascript
// 员工CRUD操作
GET /business/staff/list                    // 获取员工列表
POST /business/staff/create                 // 创建员工
PUT /business/staff/update/{staffId}        // 更新员工信息
DELETE /business/staff/delete/{staffId}     // 删除员工
GET /business/staff/{staffId}               // 获取员工详情
PUT /business/staff/{staffId}/status        // 更新员工状态
PUT /business/staff/{staffId}/reset-password // 重置员工密码
GET /business/staff/permissions             // 获取权限列表
PUT /business/staff/batch-permissions       // 批量分配权限
```

#### 核销管理接口
```javascript
// 核销操作
GET /business/verify/validate               // 验证核销码
POST /business/verify/whole-order           // 整单核销
POST /business/verify/partial               // 部分核销
GET /business/verify/logs/merchant          // 查询核销记录
GET /business/verify/count/merchant         // 统计核销数量
GET /business/verify/count/stores           // 各门店核销统计
```

#### 商品管理接口
```javascript
// 商品自提配置
GET /business/product/{productId}           // 获取商品详情（含自提配置）
PUT /business/product/{productId}/pickup-config // 更新自提配置
GET /business/product/pickup-templates      // 获取配置模板列表
PUT /business/product/{productId}/apply-template // 应用配置模板
PUT /business/product/batch-pickup-config   // 批量应用配置
```

## 平台管理端对接

### 3.1 商户门店管理

#### 页面功能要求
- 查看所有商户的门店信息
- 门店数据统计
- 强制管理门店状态

#### 相关API接口

**平台管理接口**
```
GET /admin/store/merchant/{merchantId} - 获取商户门店列表
GET /admin/store/{storeId} - 获取门店详情
PUT /admin/store/{storeId}/force-status - 强制更新门店状态
```

### 3.2 核销数据统计

#### 页面功能要求
- 核销数据统计图表
- 商户核销排行
- 异常核销监控

## 通用数据格式

### 4.1 统一响应格式

```javascript
// 成功响应
{
  "code": 200,
  "message": "success",
  "data": {}, // 具体数据
  "timestamp": 1693123456789
}

// 错误响应
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "timestamp": 1693123456789
}
```

### 4.2 分页数据格式

```javascript
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [], // 数据列表
    "total": 100, // 总数量
    "pageNum": 1, // 当前页码
    "pageSize": 10, // 每页大小
    "pages": 10 // 总页数
  }
}
```

### 4.3 枚举值说明

**配送方式（deliveryType）**
- 1: 快递配送
- 2: 门店自提

**核销状态（verifyStatus）**
- 0: 待核销
- 1: 部分核销
- 2: 已核销

**门店状态（status）**
- 0: 禁用
- 1: 启用

**员工角色类型（roleType）**
- 1: 普通员工
- 2: 店长
- 3: 区域经理

## 错误码说明

### 5.1 通用错误码
- 200: 成功
- 400: 参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 系统错误

### 5.2 业务错误码
- 1001: 核销码无效
- 1002: 订单已核销
- 1003: 员工无核销权限
- 1004: 门店不支持自提
- 1005: 门店不支持核销

## 前端开发最佳实践

### 6.1 错误处理策略

#### 网络错误处理
```javascript
// 统一的错误处理
const handleApiError = (error, context) => {
  const { code, message } = error.response?.data || {};

  switch (code) {
    case '11001':
      showToast('必填参数不能为空');
      break;
    case '11002':
      showToast('参数不合法');
      break;
    case '1001':
      showToast('核销码无效');
      break;
    case '1002':
      showToast('订单已核销');
      break;
    case '1004':
      showToast('门店不支持自提');
      break;
    default:
      showToast(message || '操作失败，请重试');
  }

  // 记录错误日志
  logError(error, context);
};
```

#### 业务异常处理
```javascript
// 订单状态异常处理
const handleOrderStateError = (orderDetail) => {
  if (orderDetail.deliveryType === 2 && !orderDetail.verifyCode) {
    // 自提订单但没有核销码
    return {
      showError: true,
      message: '核销码生成中，请稍后刷新',
      action: 'refresh'
    };
  }

  if (orderDetail.verifyStatus === 2) {
    // 已核销订单
    return {
      showSuccess: true,
      message: '订单已核销完成',
      action: 'none'
    };
  }

  return { showError: false };
};
```

### 6.2 性能优化策略

#### 数据缓存
```javascript
// 门店列表缓存
const useStoreCache = () => {
  const [cache, setCache] = useState(new Map());

  const getStores = async (merchantId) => {
    const cacheKey = `stores_${merchantId}`;

    if (cache.has(cacheKey)) {
      const { data, timestamp } = cache.get(cacheKey);
      // 缓存5分钟
      if (Date.now() - timestamp < 5 * 60 * 1000) {
        return data;
      }
    }

    const stores = await api.getStores(merchantId);
    setCache(prev => prev.set(cacheKey, {
      data: stores,
      timestamp: Date.now()
    }));

    return stores;
  };

  return { getStores };
};
```

#### 状态轮询优化
```javascript
// 智能轮询策略
const useSmartPolling = (orderId, verifyStatus) => {
  useEffect(() => {
    if (verifyStatus !== 0) return; // 非待核销状态不轮询

    let interval = 30000; // 初始30秒
    let attempts = 0;

    const poll = async () => {
      try {
        const status = await checkVerifyStatus(orderId);
        if (status !== 0) {
          // 状态已变更，停止轮询
          return;
        }

        attempts++;
        // 逐渐增加轮询间隔，最大5分钟
        interval = Math.min(interval * 1.2, 5 * 60 * 1000);

        setTimeout(poll, interval);
      } catch (error) {
        // 错误时停止轮询
        console.error('轮询失败:', error);
      }
    };

    const timer = setTimeout(poll, interval);
    return () => clearTimeout(timer);
  }, [orderId, verifyStatus]);
};
```

### 6.3 用户体验优化

#### 加载状态管理
```javascript
// 全局加载状态
const useLoading = () => {
  const [loadingStates, setLoadingStates] = useState({});

  const setLoading = (key, loading) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: loading
    }));
  };

  const isLoading = (key) => loadingStates[key] || false;

  return { setLoading, isLoading };
};

// 使用示例
const { setLoading, isLoading } = useLoading();

const handleSubmitOrder = async () => {
  setLoading('submitOrder', true);
  try {
    await submitOrder(orderParams);
    showSuccess('订单提交成功');
  } catch (error) {
    handleApiError(error, 'submitOrder');
  } finally {
    setLoading('submitOrder', false);
  }
};
```

#### 操作反馈
```javascript
// 操作确认
const showConfirmDialog = (message, onConfirm) => {
  return new Promise((resolve) => {
    Modal.confirm({
      title: '确认操作',
      content: message,
      onOk: () => {
        onConfirm?.();
        resolve(true);
      },
      onCancel: () => resolve(false)
    });
  });
};

// 核销确认
const handleVerifyOrder = async (verifyCode) => {
  const confirmed = await showConfirmDialog(
    '确认核销该订单吗？核销后无法撤销。'
  );

  if (confirmed) {
    await verifyOrder(verifyCode);
  }
};
```

### 6.4 移动端适配

#### 扫码功能
```javascript
// 扫码组件适配
const QRCodeScanner = ({ onScanSuccess, onScanError }) => {
  const [hasPermission, setHasPermission] = useState(null);

  useEffect(() => {
    // 请求摄像头权限
    navigator.mediaDevices.getUserMedia({ video: true })
      .then(() => setHasPermission(true))
      .catch(() => setHasPermission(false));
  }, []);

  if (hasPermission === false) {
    return (
      <div className="scanner-error">
        <p>需要摄像头权限才能扫码</p>
        <Button onClick={() => window.location.reload()}>
          重新授权
        </Button>
      </div>
    );
  }

  return (
    <div className="scanner-container">
      {/* 扫码组件实现 */}
    </div>
  );
};
```

#### 地图定位
```javascript
// 获取用户位置
const useGeolocation = () => {
  const [location, setLocation] = useState(null);
  const [error, setError] = useState(null);

  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      setError('浏览器不支持定位功能');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setLocation({
          longitude: position.coords.longitude,
          latitude: position.coords.latitude
        });
      },
      (error) => {
        setError('定位失败，请手动选择门店');
      },
      {
        timeout: 10000,
        enableHighAccuracy: true
      }
    );
  };

  return { location, error, getCurrentLocation };
};
```

## 开发检查清单

### 用户端开发清单
- [ ] 购物车页面配送方式选择
- [ ] 门店列表获取和展示
- [ ] 门店选择和自提信息填写
- [ ] 订单提交参数完整性
- [ ] 订单详情页面核销码展示
- [ ] 核销状态实时更新
- [ ] 核销记录查询展示
- [ ] 错误处理和用户提示
- [ ] 移动端适配和响应式设计
- [ ] 性能优化和缓存策略

### 商户端开发清单
- [ ] 门店管理CRUD功能
- [ ] 员工管理和权限配置
- [ ] 扫码核销功能实现
- [ ] 核销码验证和确认
- [ ] 核销记录查询和统计
- [ ] 核销报表和数据导出
- [ ] 权限控制和角色管理
- [ ] 操作日志和审计跟踪

### 通用开发清单
- [ ] API接口统一封装
- [ ] 错误处理统一管理
- [ ] 加载状态统一处理
- [ ] 数据缓存策略实现
- [ ] 用户体验优化
- [ ] 单元测试编写
- [ ] 集成测试验证
- [ ] 性能测试和优化

## 技术支持

### 联系方式
- **技术支持**: 开发团队
- **文档维护**: 架构师
- **问题反馈**: 项目经理

### 常见问题
1. **Q: 核销码生成失败怎么处理？**
   A: 检查订单状态，确认是自提订单且已支付，可尝试刷新或联系技术支持。

2. **Q: 门店列表为空怎么办？**
   A: 确认商户已配置门店且启用了自提功能，检查API参数是否正确。

3. **Q: 扫码功能在某些设备上不工作？**
   A: 检查摄像头权限，提供手动输入核销码的备选方案。

4. **Q: 核销状态更新不及时？**
   A: 实现轮询机制或WebSocket实时推送，优化状态同步策略。

## 更新日志

- **v1.0** (2025-08-26): 初始版本，包含基础API接口和页面设计要求
- **v1.1** (2025-08-26): 新增完整业务流程、数据流转和最佳实践
- **v1.2** (2025-08-26): 完善错误处理、性能优化和开发检查清单

---

**注意**: 本文档会随着功能的完善和需求的变化持续更新，请前端开发团队及时关注文档变更。建议在开发过程中严格按照本文档的规范和流程进行实现，确保功能的完整性和用户体验的一致性。
