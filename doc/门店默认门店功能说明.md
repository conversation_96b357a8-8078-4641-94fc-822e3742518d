# 门店默认门店功能说明

## 概述

为了提升用户体验和简化商户操作，系统新增了默认门店功能。每个商户都有一个默认门店，用于处理订单配送、核销等业务场景。

## 功能特性

### 1. 自动创建默认门店

- **触发时机**：商户注册成功时
- **实现方式**：数据库触发器自动创建
- **默认配置**：
  - 门店名称：`{商户名称}默认门店`
  - 门店编码：`STORE_{商户ID}_001`
  - 联系信息：继承商户联系信息
  - 地址信息：继承商户地址信息
  - 营业时间：09:00-21:00（全周）
  - 自提时间：09:00-20:00（全周）
  - 支持自提：是
  - 支持核销：是
  - 状态：启用
  - 默认门店：是

### 2. 默认门店管理

- **唯一性**：每个商户有且仅有一个默认门店
- **不可删除**：默认门店不能删除，只能修改信息
- **切换机制**：设置新的默认门店时，原默认门店自动取消默认状态
- **数据一致性**：通过唯一索引确保数据完整性

### 3. 业务应用场景

- **订单配送**：当用户选择门店自提时，优先显示默认门店
- **核销服务**：默认门店作为主要核销点
- **商户展示**：在商户详情页面展示默认门店信息
- **快速操作**：为商户提供快速访问默认门店的入口

## 数据库设计

### 表结构变更

```sql
-- 为门店表添加默认门店字段
ALTER TABLE `cere_merchant_store`
ADD COLUMN `is_default` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否默认门店：0=否，1=是' AFTER `status`;

-- 创建索引优化查询性能
CREATE INDEX `idx_merchant_default` ON `cere_merchant_store` (`merchant_id`, `is_default`);
```

### 触发器设计

```sql
-- 创建触发器：当新增商户时自动创建默认门店
DELIMITER $$

CREATE TRIGGER `tr_create_default_store_after_shop_insert`
AFTER INSERT ON `cere_platform_shop`
FOR EACH ROW
BEGIN
    DECLARE current_time VARCHAR(20);
    DECLARE default_store_name VARCHAR(100);
    DECLARE default_store_code VARCHAR(50);

    SET current_time = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');
    SET default_store_name = CONCAT(IFNULL(NEW.shop_name, '商户'), '默认门店');
    SET default_store_code = CONCAT('STORE_', NEW.shop_id, '_001');

    INSERT INTO `cere_merchant_store` (
        `merchant_id`, `store_name`, `store_code`, `contact_name`, `contact_phone`,
        `province_id`, `city_id`, `area_id`, `address`, `business_hours`, `pickup_hours`,
        `store_desc`, `is_pickup_enabled`, `is_verify_enabled`, `sort_order`,
        `status`, `is_default`, `create_time`, `update_time`
    ) VALUES (
        NEW.shop_id, default_store_name, default_store_code,
        IFNULL(NEW.contact_name, ''), IFNULL(NEW.contact_phone, ''),
        IFNULL(NEW.province_id, 0), IFNULL(NEW.city_id, 0), IFNULL(NEW.area_id, 0),
        IFNULL(NEW.shop_adress, '待完善详细地址'), '09:00-21:00', '09:00-20:00',
        '系统自动创建的默认门店，请及时完善门店信息', 1, 1, 100, 1, 1,
        current_time, current_time
    );
END$$

DELIMITER ;
```

## API接口

### 1. 获取默认门店

```http
GET /business/store/default
Authorization: Bearer {token}
```

**响应示例**：
```json
{
  "code": "00000",
  "message": "SUCCESS",
  "data": {
    "storeId": 1,
    "storeName": "旗舰店默认门店",
    "storeCode": "STORE_1_001",
    "isDefault": 1,
    "status": 1
  }
}
```

### 2. 设置默认门店

```http
PUT /business/store/{storeId}/set-default
Authorization: Bearer {token}
```

**响应示例**：
```json
{
  "code": "00000",
  "message": "SUCCESS"
}
```

## 前端实现

### 1. 门店列表显示

- 在门店列表中标识默认门店（如添加"默认"标签）
- 提供"设为默认"操作按钮
- 默认门店不显示删除按钮

### 2. 默认门店切换

```javascript
// 设置默认门店
const setDefaultStore = async (storeId) => {
  try {
    const response = await api.put(`/business/store/${storeId}/set-default`);
    if (response.code === '00000') {
      message.success('设置成功');
      // 刷新列表
      loadStoreList();
    }
  } catch (error) {
    message.error('设置失败');
  }
}
```

### 3. 用户体验优化

- 默认门店在列表中置顶显示
- 提供快速访问默认门店的入口
- 在门店选择器中优先显示默认门店

## 业务规则

### 1. 创建规则

- 商户注册时自动创建默认门店
- 默认门店信息基于商户信息生成
- 商户可以后续完善默认门店信息

### 2. 管理规则

- 每个商户必须有一个默认门店
- 默认门店不能删除
- 可以修改默认门店信息
- 可以将其他门店设为默认门店

### 3. 状态规则

- 默认门店默认为启用状态
- 可以禁用默认门店，但不影响默认标识
- 设置新默认门店时，原默认门店自动取消默认状态

## 错误处理

### 1. 常见错误

- **未找到默认门店**：商户数据异常，需要手动创建
- **多个默认门店**：数据一致性问题，需要修复
- **默认门店被删除**：业务逻辑错误，需要恢复

### 2. 异常处理

```java
@Override
public CereMerchantStore getDefaultStore(Long merchantId) throws CoBusinessException {
    CereMerchantStore defaultStore = cereMerchantStoreDAO.findDefaultByMerchantId(merchantId);
    if (defaultStore == null) {
        throw new CoBusinessException(CoReturnFormat.PARAM_INVALID, "未找到默认门店");
    }
    return defaultStore;
}
```

## 测试验证

### 1. 功能测试

- 验证商户注册时是否自动创建默认门店
- 验证默认门店的各项信息是否正确
- 验证默认门店切换功能
- 验证默认门店不能删除

### 2. 数据验证

```sql
-- 验证每个商户是否都有默认门店
SELECT 
    ps.shop_id,
    ps.shop_name,
    COUNT(ms.store_id) as total_stores,
    SUM(CASE WHEN ms.is_default = 1 THEN 1 ELSE 0 END) as default_stores
FROM cere_platform_shop ps
LEFT JOIN cere_merchant_store ms ON ps.shop_id = ms.merchant_id
GROUP BY ps.shop_id, ps.shop_name
HAVING default_stores != 1;
```

## 部署说明

### 1. 部署顺序

1. 执行数据库变更SQL
2. 部署后端代码
3. 部署前端代码
4. 验证功能正常

### 2. 回滚方案

1. 如需回滚，先删除触发器
2. 回滚代码版本
3. 可选择保留is_default字段数据

### 3. 注意事项

- 在生产环境部署前，建议先在测试环境验证
- 触发器创建后，新注册的商户会自动创建默认门店
- 现有商户需要手动设置默认门店或运行数据修复脚本
