-- =============================================
-- 门店默认门店功能数据库变更
-- 创建时间: 2025-08-27
-- 说明: 为门店表添加默认门店字段，并创建触发器自动创建默认门店
-- =============================================

-- 1. 为门店表添加默认门店字段
ALTER TABLE `cere_merchant_store` 
ADD COLUMN `is_default` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否默认门店：0=否，1=是' AFTER `status`;

-- 2. 为现有数据设置默认门店（每个商户的第一个门店设为默认门店）
UPDATE cere_merchant_store cms1 
SET is_default = 1 
WHERE cms1.store_id = (
    SELECT MIN(cms2.store_id) 
    FROM (SELECT * FROM cere_merchant_store) cms2 
    WHERE cms2.merchant_id = cms1.merchant_id
);

-- 3. 创建触发器：当新增商户时自动创建默认门店
DELIMITER $$

CREATE TRIGGER `tr_create_default_store_after_shop_insert`
AFTER INSERT ON `cere_platform_shop`
FOR EACH ROW
BEGIN
    -- 声明变量
    DECLARE current_time VARCHAR(20);
    DECLARE default_store_name VARCHAR(100);
    DECLARE default_store_code VARCHAR(50);
    DECLARE default_address VARCHAR(200);

    -- 获取当前时间
    SET current_time = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s');

    -- 构建默认门店名称
    SET default_store_name = CONCAT(IFNULL(NEW.shop_name, '商户'), '默认门店');

    -- 构建默认门店编码
    SET default_store_code = CONCAT('STORE_', NEW.shop_id, '_001');

    -- 构建默认地址
    SET default_address = IFNULL(NEW.shop_adress, '待完善详细地址');

    -- 为新商户创建默认门店
    INSERT INTO `cere_merchant_store` (
        `merchant_id`,
        `store_name`,
        `store_code`,
        `contact_name`,
        `contact_phone`,
        `province_id`,
        `city_id`,
        `area_id`,
        `address`,
        `longitude`,
        `latitude`,
        `business_hours`,
        `pickup_hours`,
        `store_image`,
        `store_desc`,
        `is_pickup_enabled`,
        `is_verify_enabled`,
        `sort_order`,
        `status`,
        `is_default`,
        `create_time`,
        `update_time`
    ) VALUES (
        NEW.shop_id,                                    -- 商户ID
        default_store_name,                             -- 门店名称
        default_store_code,                             -- 门店编码
        IFNULL(NEW.contact_name, ''),                   -- 联系人
        IFNULL(NEW.contact_phone, ''),                  -- 联系电话
        IFNULL(NEW.province_id, 0),                     -- 省份ID
        IFNULL(NEW.city_id, 0),                         -- 城市ID
        IFNULL(NEW.area_id, 0),                         -- 区域ID
        default_address,                                -- 详细地址
        NULL,                                           -- 经度（待完善）
        NULL,                                           -- 纬度（待完善）
        '09:00-21:00',                                  -- 营业时间（简化格式）
        '09:00-20:00',                                  -- 自提时间（简化格式）
        '',                                             -- 门店图片（待上传）
        '系统自动创建的默认门店，请及时完善门店信息',      -- 门店描述
        1,                                              -- 支持自提
        1,                                              -- 支持核销
        100,                                            -- 排序权重
        1,                                              -- 状态：启用
        1,                                              -- 是否默认门店：是
        current_time,                                   -- 创建时间
        current_time                                    -- 更新时间
    );
END$$

DELIMITER ;

-- 4. 创建唯一索引确保每个商户只有一个默认门店
-- 注意：MySQL不支持WHERE条件的唯一索引，改用函数索引或业务逻辑控制
-- 这里使用复合索引，通过业务逻辑确保唯一性
CREATE INDEX `idx_merchant_default_unique` ON `cere_merchant_store` (`merchant_id`, `is_default`);

-- 5. 为门店表添加索引优化查询性能
CREATE INDEX `idx_merchant_default` ON `cere_merchant_store` (`merchant_id`, `is_default`);
CREATE INDEX `idx_merchant_status` ON `cere_merchant_store` (`merchant_id`, `status`);

-- 6. 创建存储过程确保默认门店唯一性
DELIMITER $$

CREATE PROCEDURE `sp_set_default_store`(
    IN p_store_id BIGINT,
    IN p_merchant_id BIGINT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 检查门店是否存在且属于指定商户
    IF NOT EXISTS (
        SELECT 1 FROM cere_merchant_store
        WHERE store_id = p_store_id AND merchant_id = p_merchant_id
    ) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '门店不存在或不属于当前商户';
    END IF;

    -- 取消当前默认门店
    UPDATE cere_merchant_store
    SET is_default = 0, update_time = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
    WHERE merchant_id = p_merchant_id AND is_default = 1;

    -- 设置新的默认门店
    UPDATE cere_merchant_store
    SET is_default = 1, update_time = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
    WHERE store_id = p_store_id AND merchant_id = p_merchant_id;

    COMMIT;
END$$

DELIMITER ;

-- =============================================
-- 数据验证查询
-- =============================================

-- 验证每个商户是否都有默认门店
SELECT 
    ps.shop_id,
    ps.shop_name,
    COUNT(ms.store_id) as total_stores,
    SUM(CASE WHEN ms.is_default = 1 THEN 1 ELSE 0 END) as default_stores
FROM cere_platform_shop ps
LEFT JOIN cere_merchant_store ms ON ps.shop_id = ms.merchant_id
GROUP BY ps.shop_id, ps.shop_name
HAVING default_stores != 1;

-- 查看默认门店信息
SELECT
    ms.merchant_id,
    ps.shop_name,
    ms.store_name,
    ms.store_code,
    ms.is_default,
    ms.status,
    ms.create_time
FROM cere_merchant_store ms
JOIN cere_platform_shop ps ON ms.merchant_id = ps.shop_id
WHERE ms.is_default = 1
ORDER BY ms.merchant_id;

-- =============================================
-- 触发器说明文档
-- =============================================

/*
触发器功能说明：
1. 触发时机：当cere_platform_shop表新增记录时
2. 触发动作：自动为新商户创建默认门店
3. 默认门店配置：
   - 门店名称：{商户名称}默认门店
   - 门店编码：STORE_{商户ID}_001
   - 联系信息：继承商户联系信息
   - 地址信息：继承商户地址信息
   - 营业时间：默认09:00-21:00（简化格式）
   - 自提时间：默认09:00-20:00（简化格式）
   - 功能开关：默认开启自提和核销功能
   - 状态：默认启用
   - 默认门店：是

业务规则：
1. 每个商户有且仅有一个默认门店
2. 默认门店不能删除，只能修改信息
3. 设置新的默认门店时，通过存储过程确保原默认门店自动取消默认状态
4. 通过存储过程和事务确保数据一致性

存储过程说明：
- sp_set_default_store：用于设置默认门店，确保唯一性
- 包含事务处理，确保操作的原子性
- 包含业务验证，确保门店属于指定商户

注意事项：
1. 触发器依赖cere_platform_shop表的字段结构
2. 如果商户表字段发生变化，需要同步更新触发器
3. 营业时间和自提时间使用简化格式，避免JSON格式在触发器中的复杂性
4. 建议在生产环境部署前先在测试环境验证触发器和存储过程功能
5. 可以通过查询验证每个商户是否都有默认门店
*/
