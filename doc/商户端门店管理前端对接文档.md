# 商户端门店管理功能前端对接文档

## 概述

本文档描述商户端门店管理功能的前端对接要求，包括页面设计、API接口调用、数据结构和用户交互流程。

## 1. 页面结构设计

### 1.1 门店管理主页面

**页面路径**: `/business/store/management`

**页面组成**:
- 统计卡片区域
- 搜索筛选区域  
- 操作工具栏
- 门店列表表格
- 分页组件
- 门店编辑对话框

### 1.2 统计卡片设计

显示4个统计卡片，横向排列：

```javascript
// 统计数据结构
const statistics = {
  totalStores: 10,        // 门店总数
  enabledStores: 8,       // 启用门店数
  todayPickupOrders: 25,  // 今日自提订单
  todayVerifyOrders: 18   // 今日核销订单
}
```

**卡片样式要求**:
- 每个卡片显示数字和标签
- 数字使用大字体，颜色为主题色
- 支持响应式布局

### 1.3 搜索筛选区域

**筛选条件**:
- 门店状态：全部/启用/禁用
- 自提功能：全部/支持自提/不支持自提
- 核销功能：全部/支持核销/不支持核销
- 默认门店：全部/默认门店/非默认门店
- 关键词搜索：门店名称/编码/地址

**操作按钮**:
- 搜索按钮：触发列表查询
- 重置按钮：清空所有筛选条件

## 2. API接口对接

**重要说明**：
- 所有商户端API接口都需要在请求头中携带商户登录token
- 商户ID通过后端ContextUtil.getShopId()从ThreadLocal中获取，无需前端传递
- 接口会自动验证商户权限，只能操作属于当前商户的门店
- 分页返回格式使用项目统一的Page对象，包含list和total字段

**默认门店规则**：
- 每个商户有且仅有一个默认门店
- 新注册商户会自动创建默认门店（通过数据库触发器实现）
- 默认门店不能删除，只能修改信息
- 设置新的默认门店时，原默认门店会自动取消默认状态

### 2.1 获取门店列表

```javascript
// 接口地址
GET /business/store/list

// 请求头
{
  "Authorization": "Bearer {token}"  // 商户登录token
}

// 请求参数
{
  page: 1,                    // 页码，默认1
  size: 20,                   // 每页大小，默认20
  status: null,               // 门店状态：0=禁用，1=启用
  isPickupEnabled: null,      // 是否支持自提：0=否，1=是
  isVerifyEnabled: null,      // 是否支持核销：0=否，1=是
  keyword: ""                 // 搜索关键词
}

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS",
  "data": {
    "total": 50,
    "list": [
      {
        "storeId": 1,
        "storeName": "旗舰店",
        "storeCode": "STORE001",
        "contactName": "张经理",
        "contactPhone": "0755-12345678",
        "address": "深圳市南山区科技园",
        "businessHours": "09:00-21:00",
        "pickupHours": "09:00-20:00",
        "isPickupEnabled": 1,
        "isVerifyEnabled": 1,
        "status": 1,
        "isDefault": 0,
        "storeImage": "https://...",
        "createTime": "2025-08-26 10:00:00"
      }
    ]
  }
}
```

### 2.2 获取门店统计信息

```javascript
// 接口地址
GET /business/store/statistics

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS",
  "data": {
    "totalStores": 10,
    "enabledStores": 8,
    "disabledStores": 2,
    "pickupEnabledStores": 6,
    "verifyEnabledStores": 8,
    "todayVerifyOrders": 18,
    "monthVerifyOrders": 450,
    "todayPickupOrders": 25,
    "monthPickupOrders": 680
  }
}
```

### 2.3 创建门店

```javascript
// 接口地址
POST /business/store/create

// 请求参数
{
  "storeName": "新门店",
  "storeCode": "STORE002",
  "contactName": "李经理", 
  "contactPhone": "0755-87654321",
  "provinceId": 440000,
  "cityId": 440300,
  "areaId": 440305,
  "address": "详细地址",
  "longitude": 113.123456,
  "latitude": 22.123456,
  "businessHours": "09:00-21:00",
  "pickupHours": "09:00-20:00",
  "isPickupEnabled": 1,
  "isVerifyEnabled": 1,
  "storeImage": "https://...",
  "storeDesc": "门店描述",
  "sortOrder": 100
}

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS",
  "data": {
    "storeId": 2,
    "storeName": "新门店",
    // ... 其他字段
  }
}
```

### 2.4 更新门店

```javascript
// 接口地址  
PUT /business/store/update/{storeId}

// 请求参数：同创建门店参数
// 响应数据：同创建门店响应
```

### 2.5 获取门店详情

```javascript
// 接口地址
GET /business/store/{storeId}

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS",
  "data": {
    "storeId": 1,
    "storeName": "旗舰店",
    // ... 完整门店信息
  }
}
```

### 2.6 更新门店状态

```javascript
// 接口地址
PUT /business/store/{storeId}/status?status=1

// 请求头
{
  "Content-Type": "application/json",
  "Authorization": "Bearer {token}"  // 商户登录token
}

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS"
}
```

### 2.7 批量更新门店状态

```javascript
// 接口地址
PUT /business/store/batch-status

// 请求头
{
  "Content-Type": "application/json",
  "Authorization": "Bearer {token}"  // 商户登录token
}

// 请求参数
{
  "storeIds": [1, 2, 3],
  "status": 1
}

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS"
}
```

### 2.8 检查门店编码唯一性

```javascript
// 接口地址
GET /business/store/check-code?storeCode=STORE001&excludeStoreId=1

// 请求头
{
  "Authorization": "Bearer {token}"  // 商户登录token
}

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS",
  "data": true  // true=唯一，false=重复
}
```

### 2.9 获取默认门店

```javascript
// 接口地址
GET /business/store/default

// 请求头
{
  "Authorization": "Bearer {token}"  // 商户登录token
}

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS",
  "data": {
    "storeId": 1,
    "storeName": "默认门店",
    "storeCode": "STORE_001",
    "isDefault": 1,
    // ... 其他门店字段
  }
}
```

### 2.10 设置默认门店

```javascript
// 接口地址
PUT /business/store/{storeId}/set-default

// 请求头
{
  "Content-Type": "application/json",
  "Authorization": "Bearer {token}"  // 商户登录token
}

// 响应数据
{
  "code": "00000",
  "message": "SUCCESS"
}
```

## 3. 页面交互流程

### 3.1 门店列表加载流程

1. 页面初始化时调用统计接口和列表接口
2. 支持分页、筛选、搜索功能
3. 列表支持批量选择和操作
4. 状态切换使用开关组件，实时更新

### 3.2 门店编辑流程

1. 新增：点击"新增门店"按钮，打开编辑对话框
2. 编辑：点击列表中的"编辑"按钮，先获取详情再打开对话框
3. 表单验证：必填字段验证、格式验证
4. 地址选择：集成地图组件选择经纬度
5. 保存：调用创建或更新接口

### 3.3 批量操作流程

1. 用户勾选多个门店
2. 点击批量启用/禁用按钮
3. 显示确认对话框
4. 确认后调用批量更新接口
5. 操作完成后刷新列表

## 4. 表单验证规则

### 4.1 必填字段验证

```javascript
const formRules = {
  storeName: [
    { required: true, message: '请输入门店名称' },
    { max: 100, message: '门店名称不能超过100个字符' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
  ],
  address: [
    { required: true, message: '请输入详细地址' },
    { max: 200, message: '地址不能超过200个字符' }
  ]
}
```

### 4.2 门店编码唯一性验证

```javascript
// 编码输入时实时验证
const validateStoreCode = async (rule, value) => {
  if (!value) return Promise.resolve();
  
  const response = await checkStoreCodeUnique(value, editingStoreId);
  if (!response.data) {
    return Promise.reject('门店编码已存在');
  }
  return Promise.resolve();
}
```

## 5. 错误处理

### 5.1 网络错误处理

```javascript
// 统一错误处理
const handleApiError = (error, defaultMessage = '操作失败') => {
  if (error.response) {
    // 服务器返回错误
    const { code, message } = error.response.data;
    this.$message.error(message || defaultMessage);
  } else {
    // 网络错误
    this.$message.error('网络错误，请重试');
  }
}
```

### 5.2 业务错误处理

```javascript
// 根据返回码处理
const handleBusinessError = (response) => {
  if (response.code !== '00000') {
    this.$message.error(response.message || '操作失败');
    return false;
  }
  return true;
}
```

## 6. 性能优化建议

### 6.1 列表优化

- 使用虚拟滚动处理大量数据
- 图片懒加载
- 防抖搜索

### 6.2 缓存策略

- 门店列表数据缓存5分钟
- 统计数据缓存1分钟
- 地区数据长期缓存

## 7. 响应式设计要求

### 7.1 移动端适配

- 统计卡片在小屏幕上垂直排列
- 表格在移动端使用卡片式布局
- 搜索条件收起为抽屉式

### 7.2 平板适配

- 保持桌面端布局
- 调整间距和字体大小
- 优化触摸操作体验

## 8. 技术实现细节

### 8.1 商户身份验证

```javascript
// 后端通过ContextUtil.getShopId()获取商户ID
// 前端无需传递商户ID，只需确保请求头包含有效token
const apiRequest = {
  headers: {
    'Authorization': `Bearer ${getToken()}`,
    'Content-Type': 'application/json'
  }
}
```

### 8.2 分页数据处理

```javascript
// 后端返回的分页格式
const pageResponse = {
  code: "00000",
  message: "SUCCESS",
  data: {
    total: 100,    // 总记录数
    list: [...]    // 当前页数据
  }
}

// 前端分页组件配置
const pagination = {
  current: 1,
  pageSize: 20,
  total: pageResponse.data.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
}
```

### 8.3 错误处理

```javascript
// 统一错误处理
const handleApiResponse = (response) => {
  if (response.code === '00000') {
    return response.data;
  } else {
    throw new Error(response.message || '操作失败');
  }
}
```

## 9. 组件复用建议

### 9.1 可复用组件

- 地图选择器组件
- 营业时间选择器组件
- 图片上传组件
- 统计卡片组件

### 9.2 工具函数

- 时间格式化函数
- 地址格式化函数
- 状态文本转换函数
