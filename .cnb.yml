

# 匹配以 release 开头的分支名
v3.2*:
  # 自定义按钮可触发的事件
  web_trigger_one:
    - docker:
        # 声明构建环境，可以在 dockerhub 上 https://hub.docker.com/_/maven 找到您需要maven和jdk版本
        image: maven:3.8.6-openjdk-8
        volumes:
          # 声明式的构建缓存 https://docs.cnb.cool/grammar/pipeline.html#volumes
          - /root/.m2:copy-on-write
      services:
        # 流水线中启用 docker 服务
        - docker
      imports: https://cnb.cool/yuezhikeji/secret/-/blob/main/ecp-uat.yml
      stages:
        - name: mvn package
          script: |
            # 合并 ./settings.xml 和 /root/.m2/settings.xml
            mvn clean package -s ./settings.xml  -Dmaven.test.skip=true
            mvn deploy -s ./settings.xml -Dmaven.test.skip=true
        # 云原生构建自动构建Docker镜像并将它发布到制品库，【上传Docker制品】https://docs.cnb.cool/artifact/docker.html
        - name: docker build
          script:
            - docker build -t ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/app:latest ./cereshop-app
        - name: docker push
          script:
            - docker push ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/app:latest
        - name: 通过 ssh 插件和 jumpserver，到目标机器执行启动命令
          image: tencentcom/ssh
          settings:
            # 内网机器 IP
            host:
              - ${REMOTE_HOST}
            username: ${REMOTE_USERNAME}
            password: ${REMOTE_PASSWORD}
            port: ${REMOTE_PORT}
            command_timeout: 2m
            # 公网机器 IP
            # 清理 dangling 镜像（标签为 <none> 的镜像）
            script: |
              docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
              docker pull ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/app:latest
              docker image prune -f 
              cd /opt/cereshop-app/
              docker-compose down
              docker-compose up -d
  web_trigger_tow:
    - docker:
        # 声明构建环境，可以在 dockerhub 上 https://hub.docker.com/_/maven 找到您需要maven和jdk版本
        image: maven:3.8.6-openjdk-8
        volumes:
          # 声明式的构建缓存 https://docs.cnb.cool/grammar/pipeline.html#volumes
          - /root/.m2:copy-on-write
      services:
        # 流水线中启用 docker 服务
        - docker
      imports: https://cnb.cool/yuezhikeji/secret/-/blob/main/ecp-uat.yml
      stages:
        - name: mvn package
          script: |
            # 合并 ./settings.xml 和 /root/.m2/settings.xml
            mvn clean package -s ./settings.xml  -Dmaven.test.skip=true
            mvn deploy -s ./settings.xml -Dmaven.test.skip=true
        # 云原生构建自动构建Docker镜像并将它发布到制品库，【上传Docker制品】https://docs.cnb.cool/artifact/docker.html
        - name: docker build
          script:
            - docker build -t ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/admin:latest ./cereshop-admin
        - name: docker push
          script:
            - docker push ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/admin:latest
        - name: 通过 ssh 插件和 jumpserver，到目标机器执行启动命令
          image: tencentcom/ssh
          settings:
            # 内网机器 IP
            host:
              - ${REMOTE_HOST}
            username: ${REMOTE_USERNAME}
            password: ${REMOTE_PASSWORD}
            port: ${REMOTE_PORT}
            command_timeout: 2m
            # 公网机器 IP
            # 清理 dangling 镜像（标签为 <none> 的镜像）
            script: |
              docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
              docker pull ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/admin:latest
              docker image prune -f 
              #              cd /root/elderlyCarePlatform/
              #              docker-compose down
              #              docker-compose up -d
  web_trigger_three:
    - docker:
        # 声明构建环境，可以在 dockerhub 上 https://hub.docker.com/_/maven 找到您需要maven和jdk版本
        image: maven:3.8.6-openjdk-8
        volumes:
          # 声明式的构建缓存 https://docs.cnb.cool/grammar/pipeline.html#volumes
          - /root/.m2:copy-on-write
      services:
        # 流水线中启用 docker 服务
        - docker
      imports: https://cnb.cool/yuezhikeji/secret/-/blob/main/ecp-uat.yml
      stages:
        - name: mvn package
          script: |
            # 合并 ./settings.xml 和 /root/.m2/settings.xml
            mvn clean package -s ./settings.xml  -Dmaven.test.skip=true
            mvn deploy -s ./settings.xml -Dmaven.test.skip=true
        # 云原生构建自动构建Docker镜像并将它发布到制品库，【上传Docker制品】https://docs.cnb.cool/artifact/docker.html
        - name: docker build
          script:
            - docker build -t ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/business:latest ./cereshop-business
        - name: docker push
          script:
            - docker push ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/business:latest
        - name: 通过 ssh 插件和 jumpserver，到目标机器执行启动命令
          image: tencentcom/ssh
          settings:
            # 内网机器 IP
            host:
              - ${REMOTE_HOST}
            username: ${REMOTE_USERNAME}
            password: ${REMOTE_PASSWORD}
            port: ${REMOTE_PORT}
            command_timeout: 2m
            # 公网机器 IP
            # 清理 dangling 镜像（标签为 <none> 的镜像）
            script: |
              docker login -u ${CNB_TOKEN_USER_NAME} -p "${CNB_TOKEN}" ${CNB_DOCKER_REGISTRY}
              docker pull ${CNB_DOCKER_REGISTRY}/${CNB_REPO_SLUG_LOWERCASE}/business:latest
              docker image prune -f 
              #              cd /root/elderlyCarePlatform/
              #              docker-compose down
              #              docker-compose up -d