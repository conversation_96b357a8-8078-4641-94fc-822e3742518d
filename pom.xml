<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.1.7.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<groupId>com.shop</groupId>
	<artifactId>cereshop</artifactId>
	<version>3.2</version>

	<packaging>pom</packaging>

	<name>cereshop</name>
	<description>cereshop project for Spring Boot</description>

	<modules>
		<module>cereshop-commons</module>
		<module>cereshop-admin</module>
		<module>cereshop-business</module>
		<module>cereshop-app</module>
	</modules>

	<properties>
		<java.version>1.8</java.version>
		<spring.boot.thymeleaf.vesion>2.1.7.RELEASE</spring.boot.thymeleaf.vesion>
		<aop.version>2.1.7.RELEASE</aop.version>
		<!--<mybatis.version>1.1.1</mybatis.version>-->
		<pinyin4j.version>2.5.0</pinyin4j.version>
		<mysql.connector.version>8.0.22</mysql.connector.version>
		<druid.version>1.1.10</druid.version>
		<gson.version>2.6.1</gson.version>
		<thumbnailator.version>0.4.8</thumbnailator.version>
		<fastjson2.version>2.0.45</fastjson2.version>
		<lombok.version>1.18.22</lombok.version>
		<poi.version>3.17</poi.version>
		<commons-io.version>2.5</commons-io.version>
		<beanutils.version>1.8.3</beanutils.version>
		<common-lang.version>2.6</common-lang.version>
		<shiro.version>1.8.0</shiro.version>
		<thymeleaf-extras-shiro>2.0.0</thymeleaf-extras-shiro>
		<pagehealper.version>1.2.5</pagehealper.version>
		<swagger.version>2.8.0</swagger.version>
		<qr.core.version>3.3.0</qr.core.version>
		<file.upload.version>1.3.1</file.upload.version>
		<redis.version>2.9.3</redis.version>
		<spring.redis.version>2.1.7.RELEASE</spring.redis.version>
		<commons.pool2.version>2.5.0</commons.pool2.version>
		<dubbo.version>0.2.0</dubbo.version>
		<aliyun-sdk-oss.version>3.8.0</aliyun-sdk-oss.version>
		<aliyun-java-sdk-core.version>4.5.9</aliyun-java-sdk-core.version>
		<aliyun-java-sdk-dysmsapi.version>1.1.0</aliyun-java-sdk-dysmsapi.version>
		<imageio-jpeg.version>3.3</imageio-jpeg.version>
		<httpclient.version>4.5.8</httpclient.version>
		<httpcore.version>4.4.10</httpcore.version>
		<httpmime.version>4.5.12</httpmime.version>
		<hutool-all.version>5.3.4</hutool-all.version>
		<configuration.version>1.9</configuration.version>
		<spring.boo.version>2.1.7.RELEASE</spring.boo.version>
		<mybatis-plus-version>3.1.0</mybatis-plus-version>
		<ognl-version>3.2</ognl-version>
		<velocity-version>2.2</velocity-version>
		<redisson-version>3.22.0</redisson-version>
		<UserAgentUtils-version>1.20</UserAgentUtils-version>
		<alipay.version>4.13.58.ALL</alipay.version>
		<wxjava.version>4.6.0</wxjava.version>
		<jjwt.version>0.11.1</jjwt.version>
		<wxv3.version>0.4.7</wxv3.version>
		<sa-token.version>1.44.0</sa-token.version>
		<kotlin.version>1.6.21</kotlin.version>
	</properties>

	<!-- 父工程管理 -->
	<dependencyManagement>
		<dependencies>
			<!-- Spring Boot Redis依赖 -->
			<!-- 注意：1.5版本的依赖和2.0的依赖不一样，注意看哦 1.5我记得名字里面应该没有“data”, 2.0必须是“spring-boot-starter-data-redis” 这个才行-->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-data-redis</artifactId>
				<version>${spring.redis.version}</version>
				<!-- 1.5的版本默认采用的连接池技术是jedis  2.0以上版本默认连接池是lettuce, 在这里采用jedis，所以需要排除lettuce的jar -->
				<exclusions>
					<exclusion>
						<groupId>redis.clients</groupId>
						<artifactId>jedis</artifactId>
					</exclusion>
					<exclusion>
						<groupId>io.lettuce</groupId>
						<artifactId>lettuce-core</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatis-plus-version}</version>
			</dependency>

			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-core</artifactId>
				<version>${mybatis-plus-version}</version>
			</dependency>

			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-extension</artifactId>
				<version>${mybatis-plus-version}</version>
			</dependency>

			<dependency>
				<groupId>ognl</groupId>
				<artifactId>ognl</artifactId>
				<version>${ognl-version}</version>
			</dependency>

			<!--mybatis-plus代码生成器 相关依赖-->
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-generator</artifactId>
				<version>${mybatis-plus-version}</version>
			</dependency>

			<!--代码生成器模板引擎 相关依赖-->
			<dependency>
				<groupId>org.apache.velocity</groupId>
				<artifactId>velocity-engine-core</artifactId>
				<version>${velocity-version}</version>
			</dependency>

			<!-- 添加jedis客户端 -->
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${redis.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>commons-pool2</artifactId>
						<groupId>org.apache.commons</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- redis分布式锁框架 -->
			<dependency>
				<groupId>org.redisson</groupId>
				<artifactId>redisson</artifactId>
				<version>${redisson-version}</version>
			</dependency>

			<!--spring2.0集成redis所需common-pool2-->
			<!-- 必须加上，jedis依赖此  -->
			<!-- spring boot 2.0 的操作手册有标注 大家可以去看看 地址是：https://docs.spring.io/spring-boot/docs/2.0.3.RELEASE/reference/htmlsingle/-->
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-pool2</artifactId>
				<version>${commons.pool2.version}</version>
			</dependency>

			<!--aliyunOSS-->
			<dependency>
				<groupId>com.aliyun.oss</groupId>
				<artifactId>aliyun-sdk-oss</artifactId>
				<version>${aliyun-sdk-oss.version}</version>
			</dependency>

			<!--阿里短信服务-->
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-core</artifactId>
				<version>${aliyun-java-sdk-core.version}</version>
			</dependency>

			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
				<version>${aliyun-java-sdk-dysmsapi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-thymeleaf</artifactId>
				<version>${spring.boot.thymeleaf.vesion}</version>
			</dependency>

			<!--模板引擎-->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-freemarker</artifactId>
				<version>${spring.boot.thymeleaf.vesion}</version>
			</dependency>

			<!-- 支付宝支付 -->
			<dependency>
				<groupId>com.alipay.sdk</groupId>
				<artifactId>alipay-sdk-java</artifactId>
				<version>${alipay.version}</version>
			</dependency>

			<!-- jwt -->
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-api</artifactId>
				<version>${jjwt.version}</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-impl</artifactId>
				<version>${jjwt.version}</version>
			</dependency>
			<dependency>
				<groupId>io.jsonwebtoken</groupId>
				<artifactId>jjwt-jackson</artifactId>
				<version>${jjwt.version}</version>
			</dependency>
			<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
			<dependency>
				<groupId>cn.dev33</groupId>
				<artifactId>sa-token-spring-boot-starter</artifactId>
				<version>${sa-token.version}</version>
			</dependency>

			<!-- Sa-Token 插件：整合SSO -->
			<dependency>
				<groupId>cn.dev33</groupId>
				<artifactId>sa-token-sso</artifactId>
				<version>${sa-token.version}</version>
			</dependency>

			<!-- Sa-Token 插件：整合 Forest 请求工具 -->
			<dependency>
				<groupId>cn.dev33</groupId>
				<artifactId>sa-token-forest</artifactId>
				<version>${sa-token.version}</version>
			</dependency>

			<!-- Kotlin版本统一管理 -->
			<dependency>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-stdlib</artifactId>
				<version>${kotlin.version}</version>
			</dependency>
			<dependency>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-stdlib-jdk8</artifactId>
				<version>${kotlin.version}</version>
			</dependency>
			<dependency>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-stdlib-jdk7</artifactId>
				<version>${kotlin.version}</version>
			</dependency>
			<dependency>
				<groupId>org.jetbrains.kotlin</groupId>
				<artifactId>kotlin-stdlib-common</artifactId>
				<version>${kotlin.version}</version>
			</dependency>
		</dependencies>

	</dependencyManagement>


	<dependencies>
		<!-- https://mvnrepository.com/artifact/eu.bitwalker/UserAgentUtils -->
		<dependency>
			<groupId>eu.bitwalker</groupId>
			<artifactId>UserAgentUtils</artifactId>
			<version>${UserAgentUtils-version}</version>
		</dependency>

		<!-- mail-starter -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
			<version>${spring.boo.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<version>${spring.boo.version}</version>
		</dependency>

		<!-- java的huttool工具 -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool-all.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<version>${spring.boo.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<!-- 排掉内置的tomcat-->
			<!--<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>-->
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-configuration/commons-configuration -->
		<dependency>
			<groupId>commons-configuration</groupId>
			<artifactId>commons-configuration</artifactId>
			<version>${configuration.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- 切面aop-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
			<version>${aop.version}</version>
		</dependency>

		<!-- mybatis依赖 begin -->
		<!--<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>${mybatis.version}</version>
		</dependency>-->

		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>${pinyin4j.version}</version>
		</dependency>

		<!-- mybatis依赖 end -->
		<!-- mysql数据库配置 begin -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql.connector.version}</version>
		</dependency>

		<dependency>
			<groupId>com.github.drtrang</groupId>
			<artifactId>druid-spring-boot2-starter</artifactId>
			<version>${druid.version}</version>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${gson.version}</version>
		</dependency>

		<!-- 图片压缩 -->
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>${thumbnailator.version}</version>
		</dependency>

		<!-- cmyk格式图片转换 -->
		<dependency>
			<groupId>com.twelvemonkeys.imageio</groupId>
			<artifactId>imageio-jpeg</artifactId>
			<version>${imageio-jpeg.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>${httpclient.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>${httpcore.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>${httpmime.version}</version>
		</dependency>

		<dependency>
			<groupId>com.twelvemonkeys.imageio</groupId>
			<artifactId>imageio-tiff</artifactId>
			<version>${imageio-jpeg.version}</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson2.version}</version>
		</dependency>

		<!--lombok-->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>

		<!--POI-->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>${poi.version}</version>
		</dependency>

		<!--POI-->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>${poi.version}</version>
		</dependency>

		<!--POI-->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${poi.version}</version>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons-io.version}</version>
		</dependency>

		<!-- beanutils -->
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>${beanutils.version}</version>
		</dependency>

		<!-- commons-lang-->
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>${common-lang.version}</version>
		</dependency>

		<!-- shiro-->
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
			<version>${shiro.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-beanutils</artifactId>
					<groupId>commons-beanutils</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.github.theborakompanioni</groupId>
			<artifactId>thymeleaf-extras-shiro</artifactId>
			<version>${thymeleaf-extras-shiro}</version>
		</dependency>

		<!-- 分页插件-->
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>${pagehealper.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>mybatis-plus-boot-starter</artifactId>
					<groupId>com.baomidou</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- swagger2
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${swagger.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>org.javassist</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>${swagger.version}</version>
		</dependency>
-->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-spring-boot-starter</artifactId>
			<!--在引用时请在maven中央仓库搜索最新版本号-->
			<version>2.0.2</version>
		</dependency>

		<!-- 生成二维码依赖-->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>${qr.core.version}</version>
		</dependency>

		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>${qr.core.version}</version>
		</dependency>

		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>${file.upload.version}</version>
		</dependency>

		<!-- 这里包含了Zookeeper依赖和Dubbo依赖
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
			<version>${dubbo.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency> -->

		<!-- 支付宝支付 -->
		<dependency>
			<groupId>com.alipay.sdk</groupId>
			<artifactId>alipay-sdk-java</artifactId>
			<version>${alipay.version}</version>
		</dependency>

		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-mp</artifactId>
			<version>${wxjava.version}</version>
		</dependency>

		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-cp</artifactId>
			<version>${wxjava.version}</version>
		</dependency>

		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-pay</artifactId>
			<version>${wxjava.version}</version>
		</dependency>

		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-miniapp</artifactId>
			<version>${wxjava.version}</version>
		</dependency>

		<dependency>
			<groupId>com.github.wechatpay-apiv3</groupId>
			<artifactId>wechatpay-apache-httpclient</artifactId>
			<version>${wxv3.version}</version>
		</dependency>

		<!-- jwt -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.12.5</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-annotations -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.12.5</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.12.5</version>
		</dependency>

		 <dependency>
			<groupId>com.github.whvcse</groupId>
			 <artifactId>easy-captcha</artifactId>
			 <version>1.6.2</version>
		 </dependency>

	</dependencies>

	<distributionManagement>
<!--		<repository>-->
<!--			<id>releases</id>-->
<!--			<name>Internet Release</name>-->
<!--			<url>http://127.0.0.1:8081/nexus/content/repositories/releases/</url>-->
<!--		</repository>-->
<!--		<snapshotRepository>-->
<!--			<id>snapshots</id>-->
<!--			<name>Internet Snapshots</name>-->
<!--			<url>http://127.0.0.1:8081/nexus/content/repositories/snapshots/</url>-->
<!--		</snapshotRepository>-->
			<repository>
				<!--必须与 settings.xml 的 id 一致-->
				<id>yuezhikeji-maven</id>
				<name>maven</name>
				<url>https://maven.cnb.cool/yuezhikeji/maven/-/packages/</url>
			</repository>
	</distributionManagement>


</project>
