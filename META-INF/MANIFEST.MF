Manifest-Version: 1.0
Created-By: Maven Archiver 3.4.0
Build-Jdk-Spec: 17
Class-Path: config/ lib/spring-boot-starter-data-redis-2.1.7.RELEASE.jar
  lib/spring-boot-starter-2.1.7.RELEASE.jar lib/spring-boot-starter-logg
 ing-2.1.7.RELEASE.jar lib/logback-classic-1.2.3.jar lib/logback-core-1.
 2.3.jar lib/log4j-to-slf4j-2.11.2.jar lib/log4j-api-2.11.2.jar lib/jul-
 to-slf4j-1.7.26.jar lib/snakeyaml-1.23.jar lib/spring-data-redis-2.1.10
 .RELEASE.jar lib/spring-data-keyvalue-2.1.10.RELEASE.jar lib/spring-tx-
 5.1.9.RELEASE.jar lib/spring-oxm-5.1.9.RELEASE.jar lib/jedis-2.9.3.jar 
 lib/slf4j-api-1.7.26.jar lib/redisson-3.22.0.jar lib/netty-common-4.1.3
 8.Final.jar lib/netty-codec-4.1.38.Final.jar lib/netty-buffer-4.1.38.Fi
 nal.jar lib/netty-transport-4.1.38.Final.jar lib/netty-resolver-4.1.38.
 Final.jar lib/netty-resolver-dns-4.1.38.Final.jar lib/netty-codec-dns-4
 .1.38.Final.jar lib/netty-handler-4.1.38.Final.jar lib/cache-api-1.1.1.
 jar lib/reactor-core-3.2.11.RELEASE.jar lib/reactive-streams-1.0.2.jar 
 lib/rxjava-3.1.6.jar lib/jboss-marshalling-2.0.11.Final.jar lib/jboss-m
 arshalling-river-2.0.11.Final.jar lib/kryo-5.5.0.jar lib/reflectasm-1.1
 1.9.jar lib/objenesis-3.3.jar lib/minlog-1.3.1.jar lib/jackson-dataform
 at-yaml-2.9.9.jar lib/byte-buddy-1.9.16.jar lib/jodd-bean-5.1.6.jar lib
 /jodd-core-5.1.6.jar lib/commons-pool2-2.5.0.jar lib/aliyun-sdk-oss-3.8
 .0.jar lib/jdom-1.1.jar lib/jettison-1.1.jar lib/stax-api-1.0.1.jar lib
 /aliyun-java-sdk-ram-3.0.0.jar lib/aliyun-java-sdk-sts-3.0.0.jar lib/al
 iyun-java-sdk-ecs-4.2.0.jar lib/aliyun-java-sdk-core-4.5.9.jar lib/comm
 ons-logging-1.2.jar lib/jaxb-api-2.3.1.jar lib/javax.activation-api-1.2
 .0.jar lib/org.jacoco.agent-0.8.5-runtime.jar lib/ini4j-0.5.4.jar lib/o
 pentracing-api-0.33.0.jar lib/opentracing-util-0.33.0.jar lib/opentraci
 ng-noop-0.33.0.jar lib/aliyun-java-sdk-dysmsapi-1.1.0.jar lib/cereshop-
 commons-3.2.jar lib/mybatis-plus-core-3.1.0.jar lib/mybatis-plus-annota
 tion-3.1.0.jar lib/mybatis-3.5.0.jar lib/jsqlparser-1.4.jar lib/mybatis
 -plus-extension-3.1.0.jar lib/mybatis-spring-2.0.0.jar lib/mybatis-plus
 -boot-starter-3.1.0.jar lib/mybatis-plus-3.1.0.jar lib/okhttp-4.10.0.ja
 r lib/okio-jvm-3.0.0.jar lib/bcprov-jdk16-1.46.jar lib/webp-imageio-0.2
 .1.jar lib/sa-token-spring-boot-starter-1.44.0.jar lib/sa-token-servlet
 -1.44.0.jar lib/sa-token-spring-boot-autoconfig-1.44.0.jar lib/sa-token
 -jackson-1.44.0.jar lib/sa-token-sso-1.44.0.jar lib/sa-token-core-1.44.
 0.jar lib/sa-token-sign-1.44.0.jar lib/sa-token-forest-1.44.0.jar lib/f
 orest-core-1.6.4.jar lib/juniversalchardet-1.0.3.jar lib/httpclient-cac
 he-4.5.8.jar lib/hutool-cache-5.8.35.jar lib/hutool-core-5.8.35.jar lib
 /kotlin-stdlib-1.6.21.jar lib/kotlin-stdlib-common-1.6.21.jar lib/annot
 ations-13.0.jar lib/kotlin-stdlib-jdk8-1.6.21.jar lib/kotlin-stdlib-jdk
 7-1.6.21.jar lib/UserAgentUtils-1.20.jar lib/spring-boot-starter-mail-2
 .1.7.RELEASE.jar lib/spring-context-support-5.1.9.RELEASE.jar lib/sprin
 g-beans-5.1.9.RELEASE.jar lib/spring-context-5.1.9.RELEASE.jar lib/java
 x.mail-1.6.2.jar lib/activation-1.1.jar lib/spring-boot-starter-test-2.
 1.7.RELEASE.jar lib/spring-boot-test-2.1.7.RELEASE.jar lib/spring-boot-
 test-autoconfigure-2.1.7.RELEASE.jar lib/json-path-2.4.0.jar lib/json-s
 mart-2.3.jar lib/accessors-smart-1.2.jar lib/asm-5.0.4.jar lib/junit-4.
 12.jar lib/assertj-core-3.11.1.jar lib/mockito-core-2.23.4.jar lib/byte
 -buddy-agent-1.9.16.jar lib/hamcrest-core-1.3.jar lib/hamcrest-library-
 1.3.jar lib/jsonassert-1.5.0.jar lib/android-json-0.0.20131108.vaadin1.
 jar lib/spring-core-5.1.9.RELEASE.jar lib/spring-jcl-5.1.9.RELEASE.jar 
 lib/spring-test-5.1.9.RELEASE.jar lib/xmlunit-core-2.6.3.jar lib/hutool
 -all-5.3.4.jar lib/spring-boot-starter-data-jpa-2.1.7.RELEASE.jar lib/s
 pring-boot-starter-jdbc-2.1.7.RELEASE.jar lib/HikariCP-3.2.0.jar lib/ja
 vax.transaction-api-1.3.jar lib/hibernate-core-5.3.10.Final.jar lib/jbo
 ss-logging-3.3.2.Final.jar lib/javax.persistence-api-2.2.jar lib/javass
 ist-3.23.2-GA.jar lib/antlr-2.7.7.jar lib/jandex-2.0.5.Final.jar lib/cl
 assmate-1.4.0.jar lib/dom4j-2.1.1.jar lib/hibernate-commons-annotations
 -5.0.4.Final.jar lib/spring-data-jpa-2.1.10.RELEASE.jar lib/spring-data
 -commons-2.1.10.RELEASE.jar lib/spring-orm-5.1.9.RELEASE.jar lib/spring
 -aspects-5.1.9.RELEASE.jar lib/commons-lang3-3.8.1.jar lib/spring-boot-
 starter-web-2.1.7.RELEASE.jar lib/spring-boot-starter-json-2.1.7.RELEAS
 E.jar lib/jackson-datatype-jdk8-2.9.9.jar lib/jackson-datatype-jsr310-2
 .9.9.jar lib/jackson-module-parameter-names-2.9.9.jar lib/hibernate-val
 idator-6.0.17.Final.jar lib/validation-api-2.0.1.Final.jar lib/spring-w
 eb-5.1.9.RELEASE.jar lib/spring-webmvc-5.1.9.RELEASE.jar lib/spring-exp
 ression-5.1.9.RELEASE.jar lib/commons-configuration-1.9.jar lib/spring-
 boot-starter-security-2.1.7.RELEASE.jar lib/spring-aop-5.1.9.RELEASE.ja
 r lib/spring-security-config-5.1.6.RELEASE.jar lib/spring-security-core
 -5.1.6.RELEASE.jar lib/spring-security-web-5.1.6.RELEASE.jar lib/spring
 -boot-starter-tomcat-2.1.7.RELEASE.jar lib/javax.annotation-api-1.3.2.j
 ar lib/tomcat-embed-core-9.0.22.jar lib/tomcat-embed-el-9.0.22.jar lib/
 tomcat-embed-websocket-9.0.22.jar lib/spring-boot-devtools-2.1.7.RELEAS
 E.jar lib/spring-boot-2.1.7.RELEASE.jar lib/spring-boot-autoconfigure-2
 .1.7.RELEASE.jar lib/spring-boot-starter-aop-2.1.7.RELEASE.jar lib/aspe
 ctjweaver-1.9.4.jar lib/pinyin4j-2.5.0.jar lib/mysql-connector-java-8.0
 .22.jar lib/druid-spring-boot2-starter-1.1.10.jar lib/druid-spring-boot
 2-autoconfigure-1.1.10.jar lib/spring-jdbc-5.1.9.RELEASE.jar lib/druid-
 1.1.10.jar lib/gson-2.6.1.jar lib/thumbnailator-0.4.8.jar lib/imageio-j
 peg-3.3.jar lib/imageio-core-3.3.jar lib/imageio-metadata-3.3.jar lib/c
 ommon-lang-3.3.jar lib/common-io-3.3.jar lib/common-image-3.3.jar lib/h
 ttpclient-4.5.8.jar lib/commons-codec-1.11.jar lib/httpcore-4.4.10.jar 
 lib/httpmime-4.5.12.jar lib/imageio-tiff-3.3.jar lib/fastjson-2.0.45.ja
 r lib/fastjson2-extension-2.0.45.jar lib/fastjson2-2.0.45.jar lib/lombo
 k-1.18.22.jar lib/poi-3.17.jar lib/commons-collections4-4.1.jar lib/poi
 -ooxml-schemas-3.17.jar lib/xmlbeans-2.6.0.jar lib/poi-ooxml-3.17.jar l
 ib/curvesapi-1.04.jar lib/commons-io-2.5.jar lib/commons-beanutils-1.8.
 3.jar lib/commons-lang-2.6.jar lib/shiro-spring-1.8.0.jar lib/shiro-cor
 e-1.8.0.jar lib/shiro-lang-1.8.0.jar lib/shiro-cache-1.8.0.jar lib/shir
 o-crypto-hash-1.8.0.jar lib/shiro-crypto-core-1.8.0.jar lib/shiro-crypt
 o-cipher-1.8.0.jar lib/shiro-config-core-1.8.0.jar lib/shiro-config-ogd
 l-1.8.0.jar lib/shiro-event-1.8.0.jar lib/shiro-web-1.8.0.jar lib/encod
 er-1.2.2.jar lib/thymeleaf-extras-shiro-2.0.0.jar lib/pagehelper-spring
 -boot-starter-1.2.5.jar lib/mybatis-spring-boot-starter-1.3.2.jar lib/m
 ybatis-spring-boot-autoconfigure-1.3.2.jar lib/pagehelper-spring-boot-a
 utoconfigure-1.2.5.jar lib/pagehelper-5.1.4.jar lib/knife4j-spring-boot
 -starter-2.0.2.jar lib/knife4j-spring-boot-autoconfigure-2.0.2.jar lib/
 knife4j-spring-2.0.2.jar lib/knife4j-annotations-2.0.2.jar lib/knife4j-
 core-2.0.2.jar lib/swagger-models-1.5.22.jar lib/swagger-annotations-1.
 5.22.jar lib/springfox-swagger2-2.9.2.jar lib/springfox-spi-2.9.2.jar l
 ib/springfox-core-2.9.2.jar lib/springfox-schema-2.9.2.jar lib/springfo
 x-swagger-common-2.9.2.jar lib/springfox-spring-web-2.9.2.jar lib/sprin
 g-plugin-core-1.2.0.RELEASE.jar lib/spring-plugin-metadata-1.2.0.RELEAS
 E.jar lib/mapstruct-1.2.0.Final.jar lib/springfox-bean-validators-2.9.2
 .jar lib/knife4j-spring-ui-2.0.2.jar lib/core-3.3.0.jar lib/javase-3.3.
 0.jar lib/jcommander-1.48.jar lib/jai-imageio-core-1.3.1.jar lib/common
 s-fileupload-1.3.1.jar lib/alipay-sdk-java-4.13.58.ALL.jar lib/bcprov-j
 dk15on-1.62.jar lib/weixin-java-mp-4.6.0.jar lib/weixin-java-common-4.6
 .0.jar lib/xstream-1.4.20.jar lib/mxparser-1.2.2.jar lib/xmlpull-1.1.3.
 1.jar lib/jcl-over-slf4j-1.7.26.jar lib/guava-32.1.2-jre.jar lib/failur
 eaccess-1.0.1.jar lib/listenablefuture-9999.0-empty-to-avoid-conflict-w
 ith-guava.jar lib/jsr305-3.0.2.jar lib/checker-qual-3.33.0.jar lib/erro
 r_prone_annotations-2.18.0.jar lib/j2objc-annotations-2.8.jar lib/weixi
 n-java-cp-4.6.0.jar lib/weixin-java-pay-4.6.0.jar lib/qrcode-utils-1.3.
 jar lib/jodd-util-6.1.0.jar lib/bcpkix-jdk15on-1.70.jar lib/bcutil-jdk1
 5on-1.70.jar lib/weixin-java-miniapp-4.6.0.jar lib/wechatpay-apache-htt
 pclient-0.4.7.jar lib/jjwt-api-0.11.1.jar lib/jjwt-impl-0.11.1.jar lib/
 jjwt-jackson-0.11.1.jar lib/jackson-core-2.12.5.jar lib/jackson-annotat
 ions-2.12.5.jar lib/jackson-databind-2.12.5.jar lib/easy-captcha-1.6.2.
 jar
Implementation-Title: cereshop-app
Implementation-Version: 3.2
Main-Class: com.shop.cereshop.app.CereshopAppApplication

